import { NumericValue } from '../../types/project/project';

export const handleKeyDownBlockSpecialCharacters = (e: React.KeyboardEvent<HTMLInputElement>) => {
  const regex = /^[a-zA-Z0-9 ]$/; // Chỉ cho phép chữ cái, số và dấu cách
  if (!regex.test(e.key) && e.key !== 'Backspace' && e.key !== 'Tab') {
    e.preventDefault(); // Chặn ký tự không hợp lệ
  }
};

export const handleBeforeInput = (e: React.FormEvent<HTMLInputElement> & { data: string }) => {
  if (!/^[0-9.]$/.test(e.data)) {
    e.preventDefault();
  }
};
export const handleKeyDownEnterNumber = (e: React.KeyboardEvent<HTMLInputElement>) => {
  const charCode = e?.key;

  if (e?.ctrlKey || e?.metaKey) {
    return;
  }
  // Cho phép số (0-9), d<PERSON><PERSON> chấm (.), Backspace, Tab và các phím điều hướng
  if (
    !/^[0-9.]$/.test(charCode) &&
    charCode !== 'Backspace' &&
    charCode !== 'Tab' &&
    charCode !== 'ArrowLeft' &&
    charCode !== 'ArrowRight' &&
    charCode !== 'Home' &&
    charCode !== 'End'
  ) {
    e?.preventDefault(); // Chặn ký tự không hợp lệ
  }
};

export const normalizeString = (str: string) => {
  return str?.trim().replace(/\s+/g, ' ');
};

//function parseNumericField
export const parseNumericField = (value: NumericValue): number | undefined =>
  typeof value === 'number' ? value : parseFloat(value?.$numberDecimal || '') || undefined;

export const formatNumber = (value: number | string | undefined): string => {
  if (value === null || value === undefined) return '';
  return `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

// export const formatEncryptPhone = (phone: string) => {
//   if (!phone || phone.length < 9) return phone;
//   return phone.replace(
//     /^(\d{3})(\d+)(\d{3})$/,
//     (_, start, middle, end) => `${start}${'*'.repeat(middle.length)}${end}`,
//   );
// };

export const formatEncryptPhone = (phone: string) => {
  if (!phone || phone.length < 3) return phone;
  return phone.slice(0, -3) + '***';
};

export const formatInputNumber = (value: number | string | undefined): string => {
  if (value === '' || value === null || value === undefined) return '0';
  return `${value}`.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const handlePasteNumber = (e: React.ClipboardEvent<HTMLInputElement>) => {
  const paste = e.clipboardData.getData('text');
  if (!/^\d+$/.test(paste)) {
    e.preventDefault();
  }
};

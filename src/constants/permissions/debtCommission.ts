export const PERMISSION_DEBT_COMMISSION = {
  approve: [['debt', 'commission', 'approve']],
  calculate: [['debt', 'commission', 'calculate']],
  create: [['debt', 'commission', 'create']],
  delete: [['debt', 'commission', 'delete']],
  getAll: [['debt', 'commission', 'get', 'all']],
  getCommissionPolicy: [['debt', 'commission', 'get', 'commission', 'policy']],
  getId: [['debt', 'commission', 'get', 'id']],
  listCreate: [['debt', 'commission', 'list', 'create']],
  listDelete: [['debt', 'commission', 'list', 'delete']],
  listGetAll: [['debt', 'commission', 'list', 'get', 'all']],
  listGetId: [['debt', 'commission', 'list', 'get', 'id']],
  listImport: [['debt', 'commission', 'list', 'import']],
  listPublishGetId: [['debt', 'commission', 'list', 'publish', 'get', 'id']],
  listUpdate: [['debt', 'commission', 'list', 'update']],
  policyChangeStatus: [['debt', 'commission', 'policy', 'changeStatus']],
  policyClone: [['debt', 'commission', 'policy', 'clone']],
  policyCreate: [['debt', 'commission', 'policy', 'create']],
  policyDelete: [['debt', 'commission', 'policy', 'delete']],
  policyGetAll: [['debt', 'commission', 'policy', 'get', 'all']],
  policyGetId: [['debt', 'commission', 'policy', 'get', 'id']],
  policyUpdate: [['debt', 'commission', 'policy', 'update']],
  publish: [['debt', 'commission', 'publish']],
  publishGetId: [['debt', 'commission', 'publish', 'get', 'id']],
  update: [['debt', 'commission', 'update']],
  uploadFile: [['debt', 'commission', 'upload', 'file']],
  getAllPeriod: [['debt', 'period', 'get', 'all']],
  getPeriod: [['debt', 'period', 'get']],
  createPeriod: [['debt', 'period', 'create']],
  updatePeriod: [['debt', 'period', 'update']],
  deletePeriod: [['debt', 'period', 'delete']],
  changeStatusPeriod: [['debt', 'period', 'update', 'status']],
};

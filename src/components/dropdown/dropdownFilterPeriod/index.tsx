import { Checkbox, Col, DatePicker, Dropdown, Form, Input, List, Row, Tooltip } from 'antd';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import { CheckboxProps } from 'antd/lib';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import './style.scss';
import { PERIOD } from '../../../constants/common';

interface Props {
  required?: boolean;
  label: string;
  isFilter?: boolean;
  enabled?: boolean;
  defaultValues?: string;
  fieldName: string;
}

export interface TPeriodSelected {
  key: string;
  label: string;
  value: string;
  selected?: boolean;
}

const DropdownFilterPeriod = (props: Props) => {
  const { required, label, defaultValues, fieldName } = props;
  const form = Form.useFormInstance();
  const formPeriod = Form.useWatch(fieldName, form);
  const arrayFormPeriod = useMemo(() => formPeriod?.split(',') || [], [formPeriod]);

  const [openModalMember, setOpenModalMember] = useState(false);
  const [listPeriod, setListPeriod] = useState<TPeriodSelected[]>([]);

  useEffect(() => {
    const selectedPeriod = defaultValues?.split(',') || [];

    const formattedPeriods =
      PERIOD.map(item => {
        return {
          ...item,
          selected: selectedPeriod?.includes(item.value),
        };
      }) || [];

    setListPeriod(formattedPeriods);
  }, [defaultValues]);

  useEffect(() => {
    !formPeriod && setListPeriod(PERIOD.map(item => ({ ...item, selected: false })));
  }, [formPeriod]);

  const handleCheckboxChange = (id: string) => (e: CheckboxChangeEvent) => {
    const updatedList = listPeriod?.map(period =>
      period.value === id ? { ...period, selected: e.target.checked } : period,
    );
    setListPeriod(updatedList);

    const selectedPeriods = updatedList
      .filter(period => period.selected)
      .map(period => period.value)
      .join(',');

    form.setFieldValue([fieldName], selectedPeriods);
  };

  const onCheckAllChange: CheckboxProps['onChange'] = e => {
    const isChecked = e.target.checked;
    const updatedListPeriod = listPeriod?.map(period => ({
      ...period,
      selected: isChecked,
    }));

    const selectedPeriods = isChecked ? updatedListPeriod.map(period => period.value).join(', ') : '';

    form.setFieldValue([fieldName], selectedPeriods);
    setListPeriod(updatedListPeriod);
  };

  const filteredPeriods = useMemo(() => {
    return listPeriod.filter(period => arrayFormPeriod?.includes(period.value));
  }, [listPeriod, arrayFormPeriod]);

  const displayValue = useMemo(() => {
    if (filteredPeriods.length <= 3) {
      return filteredPeriods.map(period => period.label).join(', ');
    } else {
      return `${filteredPeriods
        .slice(0, 3)
        .map(period => period.label)
        .join(', ')},...`;
    }
  }, [filteredPeriods]);

  const tooltipContent = useMemo(() => {
    return filteredPeriods.map(period => period.label).join(', ') || 'Chưa chọn kỳ tính phí';
  }, [filteredPeriods]);

  return (
    <Form.Item label={label} className="wrapper-filter-period" required={required}>
      <Row gutter={16}>
        <Col span={12}>
          <Form.Item
            name="year"
            style={{ width: '100%' }}
            getValueFromEvent={date => (date ? dayjs(date).format('YYYY') : null)}
            getValueProps={value => ({
              value: value ? dayjs().year(value).startOf('year') : null,
            })}
          >
            <DatePicker picker="year" placeholder="Chọn năm" />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item name={fieldName} style={{ width: '100%' }}>
            <Dropdown
              menu={{ items: listPeriod }}
              dropdownRender={() => (
                <div
                  className="dropdown-filter-period"
                  onMouseLeave={() => setTimeout(() => setOpenModalMember(false), 300)}
                >
                  <div className="header">
                    <Checkbox
                      indeterminate={
                        listPeriod?.some(period => period.selected) && !listPeriod?.every(period => period.selected)
                      }
                      onChange={onCheckAllChange}
                      checked={listPeriod?.every(period => period.selected)}
                    >
                      <span> Chọn tất cả</span>
                    </Checkbox>
                  </div>
                  <div className="body">
                    <List
                      dataSource={listPeriod}
                      renderItem={period => (
                        <List.Item>
                          <Checkbox checked={period.selected} onChange={handleCheckboxChange(period.value)}>
                            {period.label}
                          </Checkbox>
                        </List.Item>
                      )}
                    />
                  </div>
                </div>
              )}
              trigger={['click']}
              open={openModalMember}
              onOpenChange={() => setOpenModalMember(!openModalMember)}
            >
              <Tooltip title={tooltipContent} placement="top">
                <Input
                  readOnly
                  placeholder="Chọn kỳ tính phí"
                  value={displayValue}
                  onClick={() => setOpenModalMember(!openModalMember)}
                  style={{ cursor: 'pointer' }}
                />
              </Tooltip>
            </Dropdown>
          </Form.Item>
        </Col>
      </Row>
    </Form.Item>
  );
};

export default DropdownFilterPeriod;

import React, { useCallback, useState, useEffect, useMemo } from 'react';
import { Input, Button, Typography, Form, Tabs, Modal, Select, notification } from 'antd';
import ModalComponent from '../modal';
import { EditOutlined, EyeOutlined } from '@ant-design/icons';
import PdfViewer from '../pdf';
import { createDiscountPolicyProposalNumber, getListTemplate, getPdf } from '../../service/report';
import { useCreateField, useFetch } from '../../hooks';
import { FileTemplate, Pdf, Template } from '../../types/discountPolicy';

const { Title } = Typography;
const { TextArea } = Input;

export interface File {
  name?: string;
  url?: string;
}

interface PolicyReportModalProps {
  isOpen?: boolean;
  onClose?: () => void;
  onSubmit?: (formData: unknown) => void;
  title?: string;
  reload?: () => void;
  record?: any;
  typePolicy?: string;
  projectId?: string;
  formId?: string;
  urlType?: string;
  policyName?: string;
}

const PolicyReportModal: React.FC<PolicyReportModalProps> = ({
  isOpen = false,
  onClose,
  title,
  reload,
  record,
  typePolicy,
  projectId,
  formId,
  urlType,
  policyName,
}) => {
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('edit');
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [pdfBase64, setPdfBase64] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [hasError, setHasError] = useState<boolean>(false);
  const [isPendingGetPdf, setIsPendingGetPdf] = useState<boolean>(false);
  const [isDisabled, setDisabled] = useState<boolean>(false);

  const { mutateAsync: _createDiscountPolicyProposalNumber, isPending } = useCreateField<unknown>({
    keyOfDetailQuery: ['get-detail-discount-policy'],
    apiQuery: createDiscountPolicyProposalNumber,
    isMessageError: false,
    messageSuccess: 'Gửi tờ trình thành công!',
  });

  const { data, isLoading } = useFetch<Template>({
    queryKeyArr: ['get-template', projectId, formId],
    api: () => getListTemplate({ projectId, formId }),
    moreParams: {
      projectId: projectId,
      formId: formId,
    },
    enabled: !!projectId,
  });

  const teamplates: Template[] = useMemo(() => {
    if (Array.isArray(data?.data?.data)) {
      return data?.data?.data as Template[];
    }
    return [];
  }, [data?.data?.data]);

  const { refetch } = useFetch<Pdf>({
    queryKeyArr: ['preview-pdf'],
    api: async () => {
      const payload = {
        policyId: record?.id,
        formId: formId,
        fileUrl: selectedTemplate,
        dear: form.getFieldValue('dear'),
        body: form.getFieldValue('body'),
      };
      try {
        const resp = await getPdf(payload, urlType || '');
        return resp;
      } finally {
      }
    },
    enabled: false,
  });

  const handleTabChange = async (key: string) => {
    setActiveTab(key);
    if (key === 'preview') {
      if (!selectedTemplate) {
        notification.error({ message: 'Vui lòng chọn một mẫu tờ trình trước khi xem trước!' });
        setPdfBlob(null);
        setPdfBase64(null);
        setHasError(true);
        return;
      }
      setDisabled(true);
      try {
        const resp = await refetch();
        const dataRaw = resp.data?.data?.data;
        if (resp?.data?.data?.statusCode === '0') {
          setPdfBlob(dataRaw as unknown as Blob);
          setPdfBase64(typeof dataRaw === 'string' ? dataRaw : null);
          setHasError(false);
        } else {
          notification.error({ message: 'Tệp mẫu không đúng định dạng!' });
          setPdfBlob(null);
          setPdfBase64(null);
          setHasError(true);
        }
      } catch (error) {
        notification.error({ message: 'Đã xảy ra lỗi khi tải file PDF!' });
        setPdfBlob(null);
        setPdfBase64(null);
        setHasError(true);
      } finally {
        setDisabled(false);
      }
    }
  };

  const handleChange = (value: unknown) => {
    setSelectedTemplate(value as string | null);
    setHasError(false);
  };

  useEffect(() => {
    if (teamplates?.length > 0 && (teamplates[0]?.files ?? []).length > 0) {
      const defaultTemplate = teamplates[0]?.files?.[0]?.fileUrl ?? null;
      setSelectedTemplate(defaultTemplate);
      form.setFieldsValue({ filename: defaultTemplate });
    }
  }, [teamplates, form]);

  const handleSubmit = async () => {
    try {
      await form.validateFields();

      const pdfPayload = {
        policyId: record?.id,
        formId: formId,
        fileUrl: selectedTemplate,
        dear: form.getFieldValue('dear'),
        body: form.getFieldValue('body'),
        output: 'link',
      };

      setIsPendingGetPdf(true);
      try {
        const pdfResp = await getPdf(pdfPayload, urlType || '');
        const location = pdfResp?.data?.data?.Location;

        const payload = {
          type: typePolicy,
          id: record?.id,
          sheetName: record?.name,
          sheetUrl: location,
          files: record?.files?.map((file?: File) => ({
            name: file?.name,
            url: file?.url || '',
          })),
        };

        const resp = await _createDiscountPolicyProposalNumber(payload);
        if (resp?.data?.statusCode === '0') {
          form.resetFields();
          onClose?.();
          reload?.();
        }
      } finally {
        setIsPendingGetPdf(false);
      }
    } catch (error) {
      console.log('Validation or API call failed:', error);
      setIsPendingGetPdf(false);
    }
  };

  const handleCancel = useCallback(async () => {
    const defaultTemplate =
      teamplates?.length && teamplates?.[0]?.files?.length ? (teamplates?.[0]?.files?.[0]?.fileUrl ?? null) : null;
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi Trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          if (defaultTemplate) {
            form.setFieldsValue({ filename: defaultTemplate });
          }
          setSelectedTemplate(defaultTemplate);
          form.resetFields();
          reload?.();
          setActiveTab('edit');
          setPdfBlob(null);
          setPdfBase64(null);
          onClose?.();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      if (defaultTemplate) {
        form.setFieldsValue({ filename: defaultTemplate });
      }
      setSelectedTemplate(defaultTemplate);
      form.resetFields();
      reload?.();
      setActiveTab('edit');
      setPdfBlob(null);
      setPdfBase64(null);
      onClose?.();
    }
  }, [form, onClose, reload, setSelectedTemplate, teamplates]);

  const tabItems = [
    {
      key: 'edit',
      label: (
        <div>
          <EditOutlined />
          <span style={{ marginLeft: 10 }}>Soạn thảo</span>
        </div>
      ),
      children: (
        <Form
          form={form}
          labelAlign="left"
          style={{ maxWidth: 600 }}
          labelCol={{
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 6 },
            lg: { span: 6 },
            xl: { span: 5 },
          }}
          wrapperCol={{
            xs: { span: 24 },
            sm: { span: 24 },
            md: { span: 18 },
            lg: { span: 18 },
            xl: { span: 19 },
          }}
        >
          <Form.Item
            label="Tên tệp mẫu"
            name="filename"
            rules={[{ required: true, message: 'Vui lòng chọn tên tệp mẫu' }]}
          >
            <Select
              style={{ width: '100%' }}
              placeholder="Chọn mẫu tờ trình"
              loading={isLoading}
              value={selectedTemplate}
              onChange={handleChange}
              options={teamplates?.[0]?.files?.map((item: FileTemplate) => ({
                value: item.fileUrl,
                label: item.fileName,
              }))}
              notFoundContent="Không tìm thấy mẫu tờ trình"
            />
          </Form.Item>

          <Form.Item label="Kính gửi" name="dear">
            <TextArea rows={2} placeholder="Nhập nội dung kính gửi" maxLength={255} />
          </Form.Item>

          <Form.Item label="Nội dung" name="body">
            <TextArea rows={2} placeholder="Nhập nội dung" maxLength={500} />
          </Form.Item>
        </Form>
      ),
    },
    {
      key: 'preview',
      label: (
        <div>
          <EyeOutlined />
          <span style={{ marginLeft: 10 }}>Xem trước</span>
        </div>
      ),
      children: <div style={{ marginTop: 33 }}>{pdfBlob && <PdfViewer pdfBase64={pdfBase64} />}</div>,
    },
  ];

  return (
    <ModalComponent
      title={title}
      open={isOpen}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Hủy
        </Button>,
        <Button
          key="submit"
          type="primary"
          onClick={handleSubmit}
          disabled={isDisabled || hasError || !selectedTemplate || activeTab === 'edit'}
          loading={isPending || isPendingGetPdf}
        >
          Gửi tờ trình
        </Button>,
      ]}
      className="policy-report-modal"
    >
      <Title level={5} style={{ marginBottom: 16, color: 'rgba(0, 0, 0, 1)' }}>
        {`Chính sách ${policyName} ${record?.name || title || ''}`}
      </Title>
      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        tabBarStyle={{ marginBottom: 16 }}
        renderTabBar={() => (
          <div style={{ display: 'flex', gap: 8 }}>
            {tabItems.map(item => (
              <Button
                key={item.key}
                onClick={() => handleTabChange(item.key)}
                style={{
                  backgroundColor: activeTab === item.key ? '#f0f0f0' : '#fff',
                  border: activeTab === item.key ? 'none' : '1px solid #d9d9d9',
                  borderRadius: 4,
                  padding: '4px 12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 4,
                }}
              >
                {item.label}
              </Button>
            ))}
          </div>
        )}
      />
    </ModalComponent>
  );
};

export default PolicyReportModal;

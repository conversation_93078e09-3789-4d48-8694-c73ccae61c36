import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef } from 'react';
import { FORMAT_DATE } from '../../constants/common';
import { useFetch } from '../../hooks';
import { getTimeConfigDebtCommission } from '../../service/timeConfigCommissionDebt';
import { TPeriod } from '../../types/timeConfigFeeCommission';
import { handleErrors } from '../../service/error/errorsService';
import { Response } from '../../types/common/common';

interface Props {
  required?: boolean;
  label: string;
  disabled?: boolean;
  fieldName: string;
  clearFormValueDependency?: () => void;
}
const FormPeriodDebt = (props: Props) => {
  const { required, label, disabled, fieldName, clearFormValueDependency } = props;
  const form = Form.useFormInstance();
  const formYear = Form.useWatch('year', form);
  const project = Form.useWatch(fieldName, form);

  const listIdProject = Array.isArray(project) ? project?.map(item => item?.id).join('.') : project?.id;
  const prevQueryParamsRef = useRef<{ year?: string; project?: string }>({});

  const { data } = useFetch<TPeriod[]>({
    api: () => getTimeConfigDebtCommission({ year: formYear, projects: listIdProject }),
    queryKeyArr: ['period', formYear, listIdProject],
    withFilter: false,
    enabled: !!formYear && !!listIdProject,
  });

  useEffect(() => {
    if (data?.data && data?.data?.statusCode !== '0') {
      handleErrors(data?.data as Response);
    }
  }, [data?.data, data?.data?.statusCode]);

  // Kiểm tra và reset period khi các tham số của API thay đổi
  useEffect(() => {
    if (!!formYear && !!listIdProject) {
      const currentParams = { year: formYear, project: listIdProject };
      const prevParams = prevQueryParamsRef.current;

      if (
        (prevParams.year && Number(prevParams.year) !== Number(formYear)) ||
        (prevParams.project && prevParams.project !== listIdProject)
      ) {
        form.setFieldValue('period', undefined);
      }

      prevQueryParamsRef.current = currentParams;
    }
  }, [formYear, listIdProject, form]);

  // Lấy dữ liệu kỳ tính phí từ API và đặt giá trị ban đầu cho form
  const dataPeriod = useMemo(
    () => (formYear && listIdProject ? data?.data?.data : []),
    [data?.data?.data, formYear, listIdProject],
  );

  const handleSelect = (_: string, option: TPeriod) => {
    form.setFieldsValue({
      periodName: option?.periodName,
      periodTo: option?.periodEndDate,
      periodFrom: option?.periodStartDate,
    });
    clearFormValueDependency && clearFormValueDependency();
  };

  return (
    <Form.Item label={label} className="wrapper-period" required={required}>
      <Row gutter={24} className="group-period">
        <Col span={12}>
          <Form.Item
            name="year"
            style={{ width: '100%' }}
            rules={[{ required: true, message: 'Vui lòng chọn năm' }]}
            initialValue={dayjs().format('YYYY')}
            normalize={value => value || dayjs().format('YYYY')}
            getValueFromEvent={date => (date ? dayjs(date).format('YYYY') : null)}
            getValueProps={value => {
              return {
                value: value ? dayjs().year(value).startOf('year') : null,
              };
            }}
          >
            <DatePicker picker="year" placeholder="Chọn năm" disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="period"
            style={{ width: '100%' }}
            rules={[{ required: true, message: 'Vui lòng chọn kỳ thiết lập' }]}
          >
            <Select
              placeholder="Chọn kỳ"
              options={dataPeriod?.map(item => ({
                ...item,
                value: `${dayjs(item?.periodStartDate).format(FORMAT_DATE)} → ${dayjs(item?.periodEndDate).format(FORMAT_DATE)}`,
              }))}
              onSelect={handleSelect}
              onClear={() => {
                form.setFieldsValue({ periodName: undefined, periodTo: undefined, periodFrom: undefined });
                clearFormValueDependency && clearFormValueDependency();
              }}
              allowClear
              disabled={disabled || !project || !formYear}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form.Item>
  );
};

export default FormPeriodDebt;

import { useInfiniteQuery } from '@tanstack/react-query';
import { Select, Spin, Tooltip, Typography } from 'antd';
import { SelectProps } from 'antd/lib';
import debounce from 'lodash/debounce';
import isArray from 'lodash/isArray';
import isEqual from 'lodash/isEqual';
import type { BaseSelectRef } from 'rc-select';
import { memo, useEffect, useMemo, useRef, useState } from 'react';
import { DEFAULT_PARAMS_PAGESIZE } from '../../../constants/common';
import { FetchResponse, TDataList, useScrollHandlerLazyLoading } from '../../../hooks';
import { ISelectLazyLoading, OptionTypeSelect } from '../../../types/common/common';
import { normalizeString } from '../../../utilities/regex';

function areEqual<T>(prevProps: TSingleSelectLazy<T>, nextProps: TSingleSelectLazy<T>) {
  return (
    isEqual(prevProps.queryKey, nextProps.queryKey) &&
    isEqual(prevProps.moreParams, nextProps.moreParams) &&
    isEqual(prevProps.defaultValues, nextProps.defaultValues) &&
    isEqual(prevProps.disabled, nextProps.disabled) &&
    isEqual(prevProps.enabled, nextProps.enabled) &&
    isEqual(prevProps.apiQuery, nextProps.apiQuery)
  );
}
interface TSingleSelectLazy<T>
  extends ISelectLazyLoading<T>,
    Pick<SelectProps, 'getPopupContainer' | 'style' | 'onChange'> {
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  handleSelect?: (values: any) => void;
  allowClear?: boolean;
  onFocus?: () => void;
  onOptionsChange?: (options: OptionTypeSelect[]) => void;
}

function SingleSelectLazy<T>(props: TSingleSelectLazy<T>) {
  const {
    enabled = true,
    defaultValues,
    handleSelect,
    apiQuery,
    queryKey,
    keysLabel,
    placeholder,
    suffixIcon,
    disabled = false,
    moreParams,
    allowClear = true,
    onFocus,
    staleTime,
    gcTime,
    getPopupContainer,
    onOptionsChange,
    onChange,
  } = props;
  const [filterData, setFilterData] = useState<OptionTypeSelect[] | undefined>();
  const [selectValues, setSelectValues] = useState<OptionTypeSelect>();
  const [search, setSearch] = useState<string>();
  const selectRef = useRef<BaseSelectRef>(null);

  const { data, fetchNextPage, hasNextPage, isFetchingNextPage, isLoading } = useInfiniteQuery<
    FetchResponse<TDataList<{ [key: string]: unknown }[]>>
  >({
    queryKey: [...(queryKey || []), search, ...(moreParams ? Object.values(moreParams) : [])],
    queryFn: ({ pageParam = 1 }) =>
      apiQuery
        ? apiQuery({ page: pageParam as number, pageSize: DEFAULT_PARAMS_PAGESIZE, search, ...moreParams }).then(
            res => res.data as FetchResponse<TDataList<{ [key: string]: unknown }[]>>,
          )
        : Promise.reject(),
    initialPageParam: 1,
    staleTime: staleTime || 0,
    enabled: !!enabled,
    gcTime, // Xác định khoảng thời gian (ms) sau khi dữ liệu bị xóa khỏi cache
    getNextPageParam: lastPage => {
      const hasMore = lastPage?.data?.rows?.length > 0 && lastPage?.data?.totalPages > lastPage?.data?.page;
      return hasMore ? Number(lastPage?.data?.page) + 1 : undefined;
    },
  });

  useEffect(() => {
    setSelectValues(defaultValues);
  }, [defaultValues]);

  const scrollHandlerLazy = useScrollHandlerLazyLoading({
    hasNextPage,
    isFetchingNextPage,
    fetchNextPage,
  });

  const flatData = useMemo(() => data?.pages.flatMap(page => page?.data?.rows ?? page?.data) || [], [data]);

  //  Tính toán options cuối cùng - kết hợp filterData với giá trị đã chọn
  const finalOptions = useMemo(() => {
    const options = [...(filterData || [])];

    // Nếu có giá trị đã chọn và không có trong options, thêm vào
    if (selectValues && selectValues.value && !options.some(opt => opt.value === selectValues.value)) {
      options.unshift({
        ...selectValues,
        value: selectValues.value,
        label: (
          <Tooltip title={typeof selectValues.label === 'string' ? selectValues.label : undefined}>
            <Typography.Text
              ellipsis
              style={{ verticalAlign: 'middle', color: disabled ? 'rgba(0, 0, 0, 0.25)' : 'rgba(0, 0, 0, 0.88)' }}
            >
              {selectValues.label}
            </Typography.Text>
          </Tooltip>
        ),
      });
    }

    return options;
  }, [disabled, filterData, selectValues]);

  useEffect(() => {
    if (flatData) {
      const newFilterData = flatData
        .filter(item => item && item.id) // Lọc các mục hợp lệ (có id)
        .map(item => {
          let label: string;

          if (isArray(keysLabel)) {
            // Xử lý trường hợp keysLabel là mảng
            label = keysLabel
              .map(name => {
                const value = item[name as keyof typeof item];
                return value != null ? String(value) : ''; // Trả về chuỗi rỗng nếu giá trị không tồn tại
              })
              .filter(val => val) // Lọc bỏ các giá trị rỗng
              .join(' - ');
          } else {
            // Xử lý trường hợp keysLabel là chuỗi
            const value = item[keysLabel as keyof typeof item];
            label = value != null ? String(value) : ''; // Giá trị mặc định nếu không có
          }

          // Nếu label rỗng, có thể cung cấp giá trị mặc định
          if (!label) {
            label = ''; // Giá trị mặc định cho label
          }

          return {
            value: item.id as string,
            label: (
              <Tooltip title={label}>
                <Typography.Text ellipsis style={{ verticalAlign: 'baseline' }}>
                  {label}
                </Typography.Text>
              </Tooltip>
            ),
            ...item,
          };
        });

      setFilterData(newFilterData);
    }
  }, [flatData, keysLabel, selectValues]);

  const handleSearch = debounce((value: string) => {
    setSearch(value ? normalizeString(value) : undefined);
  }, 500);

  const handleChange = (_: string, option: OptionTypeSelect | OptionTypeSelect[]) => {
    const selectedOption = isArray(option) ? option[0] : option;
    handleSelect && handleSelect(selectedOption as T);
    setSelectValues(selectedOption);
    onChange && onChange(selectedOption, selectedOption);
    setSearch(undefined);
    setTimeout(() => {
      if (selectRef.current && selectRef.current.blur) {
        selectRef.current.blur();
      }
    }, 0);
  };
  const handleClear = () => {
    handleSelect && handleSelect(undefined as T);
    setSelectValues(undefined);
    setSearch(undefined);
    onChange && onChange(undefined, {} as OptionTypeSelect);
  };
  const handleDropdownVisibleChange = (open: boolean) => {
    if (!open) {
      setSearch(undefined);
    }
  };

  useEffect(() => {
    if (finalOptions && onOptionsChange) {
      onOptionsChange(finalOptions);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [finalOptions]);

  return (
    <Select
      ref={selectRef}
      showSearch
      disabled={disabled}
      value={!isLoading ? selectValues?.value : undefined}
      getPopupContainer={trigger =>
        getPopupContainer ? getPopupContainer(trigger) : (trigger.parentNode as HTMLElement)
      }
      onPopupScroll={scrollHandlerLazy}
      popupClassName="popup-select-single"
      placeholder={placeholder}
      onChange={handleChange}
      suffixIcon={suffixIcon}
      filterOption={false}
      onSearch={handleSearch}
      onClear={handleClear}
      allowClear={allowClear}
      dropdownRender={menu => (
        <Spin spinning={isLoading || isFetchingNextPage} size="small" tip="Loading...">
          {menu}
        </Spin>
      )}
      style={{ width: '100%' }}
      options={finalOptions}
      onFocus={onFocus}
      onDropdownVisibleChange={handleDropdownVisibleChange}
    />
  );
}

export default memo(SingleSelectLazy, areEqual);

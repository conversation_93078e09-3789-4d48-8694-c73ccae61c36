import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { useEffect, useMemo, useRef } from 'react';
import { FORMAT_DATE } from '../../constants/common';
import { useFetch } from '../../hooks';
import { getTimeConfigFeeCommission } from '../../service/timeConfigFeeCommission';
import { TPeriod } from '../../types/timeConfigFeeCommission';
import { handleErrors } from '../../service/error/errorsService';
import { Response } from '../../types/common/common';

interface Props {
  required?: boolean;
  label: string;
  disabled?: boolean;
  fieldPos: string;
  clearFormValueDependency?: () => void;
  messageValidate?: string;
}

const FormPeriod = (props: Props) => {
  const { required, label, disabled, fieldPos, clearFormValueDependency, messageValidate } = props;
  const form = Form.useFormInstance();
  const formYear = Form.useWatch('year', form);
  const orgCharts = Form.useWatch(fieldPos, form);

  const listIdOrgCharts = Array.isArray(orgCharts) ? orgCharts?.map(item => item?.id).join('.') : orgCharts?.id;
  const prevQueryParamsRef = useRef<{ year?: string; orgcharts?: string }>({});

  const { data } = useFetch<TPeriod[]>({
    api: () => getTimeConfigFeeCommission({ year: formYear, orgcharts: listIdOrgCharts }),
    queryKeyArr: ['period', formYear, listIdOrgCharts],
    withFilter: false,
    enabled: !!formYear && !!listIdOrgCharts,
  });

  useEffect(() => {
    if (data?.data && data?.data?.statusCode !== '0') {
      handleErrors(data?.data as Response);
    }
  }, [data?.data, data?.data?.statusCode]);

  // Kiểm tra và reset period khi các tham số của API thay đổi
  useEffect(() => {
    // Chỉ thực hiện khi có đủ điều kiện để gọi API
    if (!!formYear && !!listIdOrgCharts) {
      const currentParams = { year: formYear, orgcharts: listIdOrgCharts };
      const prevParams = prevQueryParamsRef.current;

      // Kiểm tra xem tham số có thay đổi hay không
      if (
        (prevParams.year && Number(prevParams.year) !== Number(formYear)) ||
        (prevParams.orgcharts && prevParams.orgcharts !== listIdOrgCharts)
      ) {
        // Reset period về undefined khi tham số thay đổi
        form.setFieldValue('period', undefined);
      }

      // Cập nhật tham số mới
      prevQueryParamsRef.current = currentParams;
    }
  }, [formYear, listIdOrgCharts, form]);

  const dataPeriod = useMemo(
    () => (formYear && listIdOrgCharts ? data?.data?.data : []),
    [data?.data?.data, formYear, listIdOrgCharts],
  );

  const handleSelect = (_: string, option: TPeriod) => {
    const selectedOption = Array.isArray(option) ? option[0] : option;
    form.setFieldsValue({
      periodObj: {
        periodName: selectedOption?.periodName,
        periodTo: selectedOption?.periodEndDate,
        periodFrom: selectedOption?.periodStartDate,
      },
    });
    clearFormValueDependency && clearFormValueDependency();
  };

  return (
    <Form.Item label={label} className="wrapper-period" required={required}>
      <Row gutter={24} className="group-period">
        <Col span={12}>
          <Form.Item
            name="year"
            style={{ width: '100%' }}
            rules={[{ required: true, message: 'Vui lòng chọn năm' }]}
            initialValue={dayjs().format('YYYY')}
            normalize={value => value || dayjs().format('YYYY')}
            getValueFromEvent={date => (date ? dayjs(date).format('YYYY') : null)}
            getValueProps={value => {
              return {
                value: value ? dayjs().year(value).startOf('year') : null,
              };
            }}
          >
            <DatePicker picker="year" placeholder="Chọn năm" disabled={disabled} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            name="period"
            style={{ width: '100%' }}
            rules={[{ required: true, message: messageValidate || 'Vui lòng chọn kỳ' }]}
          >
            <Select
              placeholder="Chọn kỳ tính phí"
              options={dataPeriod?.map(item => ({
                ...item,
                value: `${dayjs(item?.periodStartDate).format(FORMAT_DATE)} → ${dayjs(item?.periodEndDate).format(FORMAT_DATE)}`,
              }))}
              onSelect={handleSelect}
              onClear={() => {
                form.setFieldValue('periodObj', undefined);
                clearFormValueDependency && clearFormValueDependency();
              }}
              allowClear
              disabled={disabled || !orgCharts || !formYear}
            />
          </Form.Item>
        </Col>
      </Row>
    </Form.Item>
  );
};

export default FormPeriod;

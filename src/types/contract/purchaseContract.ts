// import { Dayjs } from 'dayjs';
// import { TCreatedBy } from '../common/common';

import { UploadFile } from 'antd/es/upload/interface';
import { CompanyInformation, Customer, PrimaryTransaction } from './depositContract';
import { Dayjs } from 'dayjs';

// eslint-disable-next-line prettier/prettier
export type TListPurchaseContract = {
  _id: string;
  id: string;
  name: string;
  code: string;
  type: string;
  status: string;
  startDate: string;
  expiredDate: string;
  signedDate: string;
  primaryTransaction: PrimaryTransaction;
  policyPayment: object;
  policyDiscount: object;
  salesPolicy: object;
  staffsInvolved: [];
  policyDiscounts: policyDiscounts[];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: {
    type: string;
    value: number;
    stagePayment: string;
  };

  oldContract: object;
  isTransferred: boolean;
  currency: string;
  transferType: string;
  reason: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean;
  hasInterest: boolean;
  interest: object;
  depositConfirmFromCustomer: boolean;
  transferConfirmFromCustomer: boolean;
  liquidation: object;

  interestCalculations: [];
  description: string;
  active: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
  deposit: {
    id?: string;
    code?: string;
    name?: string;
  };
  purchase: object;
  files: [];
  filesDelivery: [];
  paymentPercent: number;
  deliveryItems: [];
  deliveryDate: string;
  isSendDelivery: boolean;
  deliveryHistories: [];
  deliveryResult: object;
  handoverStatus: {
    type: string;
    default: string;
  };
  handoverSchedule: object;
  companyInformation: CompanyInformation;
  tradeHistory: object;
  pathDtt: [];
  syncErpData: object;
  changeInstallment: boolean;
  releaseStartDate: string;
  releaseEndDate: string;
  liquidate: string;
  orgCode: string;
  transactionSuccess: boolean;
  transactionState: string;
  employeeRole: string;
  employeeRevenueRate: boolean;
};

export type TFilterPurchaseContract = {
  page?: number;
  pageSize?: number;
  status?: string;
  projectId?: string[] | string | null;
  discountPolicyIds?: string[] | string | null;
  paymentPolicyIds?: string[] | string | null;
  type?: string;
  search?: string;
};

export type TPos = {
  id: string;
  code: string;
  name: string;
};

export type TPurchaseContract = {
  _id: string;
  id: string;
  name: string;
  code: string;
  type: string;
  status: string;
  startDate: string;
  expiredDate: string;
  signedDate: string;
  primaryTransaction: PrimaryTransaction;
  policyPayment: PolicyPayment;
  policyDiscount: object;
  salesPolicy: {
    id?: string;
    code?: string;
    name?: string;
  };
  staffsInvolved: [];
  policyDiscounts: policyDiscounts[];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: {
    type: string;
    value: number;
    stagePayment: string;
  };

  oldContract: object;
  isTransferred: boolean;
  currency: string;
  transferType: string;
  reason: string;
  isDebtRemind: boolean;
  isShowedInstallment: boolean;
  hasInterest: boolean;
  interest: object;
  depositConfirmFromCustomer: boolean;
  transferConfirmFromCustomer: boolean;
  liquidation: object;

  interestCalculations: [];
  description: string;
  active: boolean;
  createdBy: string;
  createdDate: string;
  modifiedBy: string;
  modifiedDate: string;
  deposit: {
    id?: string;
    code?: string;
    name?: string;
  };
  purchase: object;
  files: ExtendedUploadFile[];
  filesDelivery: [];
  paymentPercent: number;
  deliveryItems: [];
  deliveryDate: string;
  isSendDelivery: boolean;
  deliveryHistories: [];
  deliveryResult: object;
  handoverStatus: {
    type: string;
    default: string;
  };
  handoverSchedule: object;
  companyInformation: CompanyInformation;
  tradeHistory: object;
  pathDtt: [];
  syncErpData: object;
  changeInstallment: boolean;
  releaseStartDate: string;
  releaseEndDate: string;
  liquidate: string;
  orgCode: string;
  transactionSuccess: boolean;
  transactionState: string;
  employeeRole: string;
  employeeRevenueRate: boolean;
  businessArea?: {
    id?: string;
    name?: string;
    code?: string;
  };
  distributionChannel?: {
    id?: string;
    name?: string;
    code?: string;
  };
  productCategory?: {
    id?: string;
    name?: string;
    code?: string;
  };
  loanBankInfo?: {
    bankCode?: string;
    bankName?: string;
  };
  loanType?: string;
  loanTermYear?: number;
  loanAmount?: number;
  sapCode?: string;
  poNumber?: string;
  discountValue?: string;
  issuedPrice?: string;
};

export type TPurchaseContractPayload = {
  id?: string;
  primaryTransactionId?: string;
  policyPaymentId: string;
  policyDiscountIds: [];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: {
    stagePayment: string;
    type: string;
    value: number;
  };
  type: string;
  files: ExtendedUploadFile[];
  startDate: string | Dayjs | null;
  expiredDate?: string | Dayjs | null;
  signedDate?: string | Dayjs | null;
  transferType?: string;
  isDebtRemind?: boolean;
  companyInformation?: CompanyInformation;
  changeInstallment?: boolean;
  releaseEndDate?: string;
  releaseStartDate?: string;
  salesPolicy: {
    id: string;
    name: string;
    code?: string;
  };
  staffsInvolved?: staffs[];
  contractDuration?: [string, string];
  releaseDate?: [string, string];
  priceType?: string;
  businessArea?: {
    id?: string;
    name?: string;
    code?: string;
  };
  distributionChannel?: {
    id?: string;
    name?: string;
    code?: string;
  };
  productCategory?: {
    id?: string;
    name?: string;
    code?: string;
  };
  loanBankInfo?: {
    bankCode?: string;
    bankName?: string;
  };
  loanType?: string;
  loanTermYear?: number;
  loanAmount?: number;
  sapCode?: string;
  poNumber?: string;
  discountValue?: string;
  issuedPrice?: string;
};

export type TPurchaseContractForm = {
  custumer2: Customer;
  primaryTransactionId: string;
  policyPaymentId: string;
  policyDiscountIds: [];
  calcCurrencyFirst: boolean;
  calcPriceVat: boolean;
  calcContractPrice: boolean;
  maintenanceFee: {
    stagePayment: string;
    type: string;
    value: number;
  };
  type: string;
  files: File[];
  startDate: string | Dayjs | null;
  expiredDate?: string | Dayjs | null;
  signedDate?: string | Dayjs | null;
  transferType?: string;
  customer2?: string;
  isDebtRemind?: boolean;
  companyInformation?: CompanyInformation;
  changeInstallment?: boolean;
  releaseEndDate?: string;
  releaseStartDate?: string;
  salesPolicy: {
    id: string;
    code: string;
    name: string;
  };
  staffsInvolved?: staffs[];
  contractDuration?: [string, string];
  releaseDate?: [string, string];
  priceType?: string;
  maintenanceFeeValue?: number;
  maintenanceFeeType?: string;
  stagePayment?: string;
  businessArea: {
    id?: string;
    nameVN?: string;
    code?: string;
  };
  distributionChannel: {
    id?: string;
    name?: string;
    code?: string;
  };
  productCategory: {
    id?: string;
    name?: string;
    code?: string;
  };
  loanBankInfo: {
    bankCode?: string;
    bankName?: string;
  };
  loanType?: string;
  loanTermYear?: number;
  loanAmount?: number;
  loanBankCode?: string;
  sapCode?: string;
  poNumber?: string;
  discountValue?: string;
  issuedPrice?: string;
};

export interface staffs {
  id: string;
  code: string;
  name: string;
  percent: number;
}
export interface Employee {
  id: string;
  name: string;
  code: string;
}

// Interfaces
interface Receipt {
  status: string;
  amount: number;
  code: string;
  receiptDate?: string | Dayjs | null;
}
export interface Installment {
  id?: string;
  receipts: Receipt[];
  isToContract: boolean;
  name: string;
  totalAmount: number;
  totalTransfered?: number;
  descriptionProgress?: number;
  paymentDueDate?: string | Dayjs | null;
  type: string;
  value?: string;
  type2?: string;
  value2?: string;
}
interface PolicyPayment {
  id?: string;
  name?: string;
  schedule?: { installments?: Installment[] };
}

interface policyDiscounts {
  id?: string;
  name?: string;
}

export interface PaymentHistoryData {
  stt: string | number;
  isToContract: boolean;
  name: string;
  totalAmount: number;
  totalTransfered: number;
  totalRemaining: string | number;
  value: string;
  code: string;
  date: string | Dayjs | null;
  descriptionProgress?: number;
  paymentDueDate: string | Dayjs | null;
}

export interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

import { Dayjs } from 'dayjs';
import { AddressType } from '../../components/selectAddress';
import { TCreatedBy, TProjectDropdown } from '../common/common';
import { TEmployeeAll } from '../customers';

export type TFilterTraining = {
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
  createdBy?: string;
  search?: string;
};

export type TListOfTraining = {
  id: string;
  name: string;
  code: string;
  project: TProjectDropdown;
  createdBy: TCreatedBy;
  createdDate: string;
  modifiedBy: TCreatedBy;
  modifiedDate: string;
  isActive: number;
  note: string;
  livestreamUrl: string;
  urlEvent: string;
};

export type TTraining = {
  id: string;
  name: string;
  isActive: number;
  eventType: TEventType;
  timeStartEvent?: string;
  urlEvent: string;
  contentEvent: string;
  contentOffEvent: string;
  project: {
    name: string;
    id: string;
  };
  livestreamUrl: string;
  typeLiveStream: string;
  registerUrl: string;
  hotline?: string;
  fLive: number;
  displayBannerStartTime: string;
  displayBannerEndTime: string;
  startTimeCheckIn: string;
  endTimeCheckIn: string;
  skipAuthen?: boolean;
  isEventEnded?: boolean;
  hideChat?: boolean;
  hideReaction?: boolean;
  lockComment?: boolean;
  disableFullScreen?: boolean;
  allowRequestDemand?: boolean;
  allowRaiseHand?: boolean;
  unRequiredEmailRegister?: boolean;
  image: string;
  eventDescription?: string;
  note?: string;
  timeSlotNames?: string;
  saleUnitNames?: { name: string; id: string }[];
  limitRegister?: string;
  notification?: string;
  templateConfig?: {
    smsBrandName?: string;
    smsContent?: string;
    resend?: boolean;
    subject?: string;
    body?: string;
  };
  transactionStatuses?: string[]; // or use enum if possible
  prizeName?: string;
  prizes?: TPrize[];
  salesProgram: {
    id: string;
    name: string;
  };
  transactionPrizeName?: string;
  transactionPrizes?: TPrize[];
  prizeBackground?: string;
  prizeBackgroundLive?: string;
  transactionPrizeBackground?: string;
  listAdmin?: TEmployeeAll[];
  listSpinAdditional?: {
    email: string;
    id: string;
    name: string;
    phoneNumber: string;
  }[];
  interestedArea?: TAddress;
  colorPrize: string;
  colorPrizeTransaction: string;
  colorTitle: string;
  colorTitleTransaction: string;
  colorWiner: string;
  colorWinerTransaction: string;
  countGuest: number;
  modifiedBy?: TCreatedBy;
  modifiedDate?: string;
  createdBy?: TCreatedBy;
  createdDate?: string;
};

type TAddress = {
  address?: string;
  province: AddressType;
  district: AddressType;
  ward: AddressType;
};

export type TPrize = {
  prizeName: string;
  prizeType: number;
  prizeValue: string;
  amount: number;
  prizeImage: string;
  prizeStatus: 'INIT' | 'START_SPINING' | 'STOP_SPINING'; // nếu status có thêm giá trị khác, bổ sung tại đây
  isSelected: boolean;
  prizes: TPrizeItem[];
};

export type TPrizeItem = {
  id: string;
  prizeStatus: 'START_SPINING' | 'STOP_SPINING' | 'INIT'; // có thể mở rộng thêm nếu có trạng thái khác
  user: string | null;
  userData: TUserData | null;
};

export type TUserData = {
  id: string;
  email: string;
  phoneNumber: string;
  name: string;
  dvbh: string;
  cmnd: string;
};

export type TEventType = 'ONLINE' | 'OFFLINE';

export type IUserEvent = {
  id: string;
  checkInCode: string;
  checkInTimes: number;
  cmnd: string;
  code: string;
  createdDate: string;
  date: string;
  description: string;
  dvbh: string; // đơn vị bán hàng
  email: string;
  employeeImport: boolean;
  eventId: string;
  eventName: string;
  fromImport: boolean;
  isCheckIn: boolean;
  isGuest: boolean;
  isPassingGuests: boolean;
  isSeller: boolean;
  isSendCheckInCode: boolean;
  isSendMail: boolean;
  isUpdatedProfile: boolean;
  modifiedDate: string;
  name: string;
  phoneNumber: string;
  posId: string;
  raiseHand: boolean;
  actionName: string;
  children?: IUserEvent[];
  createdBy?: TCreatedBy;
};

export type TTableUserEvent = {
  id: string;
  posName: string;
  children?: IUserEvent[];
};

export interface IActionsSpin {
  id: string;
  priceLevel: number;
  type: 'customer' | 'transaction';
}

export interface IConfirmSpin {
  eventId: string;
  userEmail: string;
  winnerId: string;
  type: 'customer' | 'transaction';
}

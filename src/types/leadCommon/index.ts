import { Dayjs } from 'dayjs';
import { TUpdatedBy } from '../common/common';

export type TListOfLeads = {
  id: string;
  name: string;
  phone: string;
  email: string;
  profileUrl: string;
  type: string;
  createdDate: string;
  updatedDate: string;
  source: string;
  code: string;
  note: string;
  importedBy: TImportedBy;
  createdBy: string;
  isHot: boolean;
  repo: TRepo;
  pos: TPosition;
  createdByObj: TUpdatedBy;
  modifiedByObj: TUpdatedBy;
};
export type TRepoConfig = {
  code: string;
  name: string;
  visiblePhone: boolean;
};
export type TRepo = {
  id: string;
  name: string;
  code: string;
  config: TRepoConfig;
};
export type TPosition = {
  id: string;
  name: string;
};

export type TImportedBy = {
  id: string;
  name: string | null;
};

export type TFilterLeadCommon = {
  createdFrom?: string | Dayjs | null;
  createdTo?: string | Dayjs | null;
  status?: string;
  source?: string;
  type?: string;
  isHot?: string;
  idSource?: string;
  exploitStatus?: string;
  search?: string;
};

export type TLeadCommon = {
  source: string;
  repoId: string;
  repoConfigCode: string;
  assignee: string;
  name: string;
  phone: string;
  email?: string;
  description?: string;
  profileUrl?: string;
  note?: string;
};

export type TImportLeadCommon = {
  files: File;
  source: string;
  repoId: string;
  repoConfigCode: string;
};

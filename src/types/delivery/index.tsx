import { RcFile, UploadFile } from 'antd/lib/upload';
import { Dayjs } from 'dayjs';
import { GroupType } from '../../page/deliveryManagement/utils';

export interface IDelivery {
  [key: string]: unknown;
  _id: string;
  name: string;
  type: string;
  imageUrl: string;
  status: number;
  code: string;
  investor: {
    id: string;
    name: string;
  };
  project: TProject;
  urlEsalekit?: string;
  esalekit?: boolean;
  salesProgramIds?: string[];
  deliveryDate?: string;
}

export interface IFilterDelivery {
  page?: number;
  pageSize?: number;
  search?: string;
  projectId?: string;
  syncErpDataCode?: string;
  id?: string;
}

export type FrameItem = {
  startTime: string;
  endTime: string;
  // ...other props
};

export type TProject = {
  id: string;
  code: string;
  name: string;
  type: string;
};

export type THistory = {
  modifiedDate: string | null;
  modifiedBy: string | null;
  propertyStatusName: string | null;
  primaryStatus: string | null;
  actionName: string | null;
  posName: string | null;
  modifiedByName: string | null;
  reason: string | null;
  prevPosName: string | null;
};

export type TAttribute = {
  id: string;
  attributeId: string;
  controlType?: string;
  groupName?: string;
  attributeName: string;
  value?: string;
};

export type TBankInfo = {
  bank: string;
  value: string;
  branch: string;
  accountNumber: string;
};

export type TPersonInfo = {
  identities: {
    _id: string;
    value: string;
    date: string;
    place: string;
  }[];
  email: string;
  phone: string;
  name: string;
  address?: {
    fullAddress?: string;
  };
};

export type TCustomer = {
  bankInfo: TBankInfo;
  code: string;
  personalInfo: TPersonInfo;
  taxCode: string;
  infor: TPersonInfo;
};

export type TPrimaryTransaction = {
  bookingTicketCode?: string;
  customer?: TCustomer;
  propertyUnit?: IPropertyUnit;
};

export type TMaintenanceFee = {
  value: number;
  stagePayment: string;
  type: string;
};

export interface IPropertyUnit {
  id: string;
  project: string;
  code: string;
  primaryStatus: string;
  shortCode: string;
  floor: string;
  bedroom: string;
  block: string;
  price: number;
  priceVat: number;
  priceAbove: number;
  priceAboveVat: number;
  contractPrice: number;
  attributes?: TAttribute[];
  primaryTransaction?: TPrimaryTransaction;
  paymentPercent?: number;
  maintenanceFee?: TMaintenanceFee;
  historiesHandover: THistory[];
  totalTransfered?: number;
  totalAmount?: number;
  handoverStatus?: string;
  cerHandoverStatus?: string;
  handoverSchedule?: string | Dayjs;
  colorSyncErp?: string;
  items?: IPropertyUnit[];
  deliveryId?: string;
  handoverStartTime?: string;
  handoverEndTime?: string;
  status?: string;
}

export type TBlock = {
  id?: string;
  projectId?: string;
  block?: string;
  floors?: string[];
  rooms?: string[];
  softDelete?: boolean;
  createdDate?: Date;
  updatedDate?: Date;
};

export type TGetDataBlock = {
  blocks: {
    id?: string;
    projectId?: string;
    block?: string;
    floors?: string;
    rooms?: string;
    softDelete?: boolean;
    createdDate?: Date;
    updatedDate?: Date;
  }[];
};

export type TChangeXDStatus = {
  projectId?: string;
  documentHandoverStatusReason?: string;
  unitStatuses?: { id?: string; status?: string }[];
};

export type TChangeXDStatusModal = {
  statusXD?: string;
  key: string;
  id?: string;
};

export type TGetDataCalenderHandover = {
  actionName: string;
  createdBy: string;
  createdDate: string;
  deliveryId: string;
  handoverChangeRequestEndTime: string;
  handoverChangeRequestStartTime: string;
  handoverEndTime: string;
  handoverStartTime: string;
  id: string;
  modifiedBy: string;
  modifiedDate: string;
  status: string;
  handoverApartment: {
    primaryTransaction: { propertyUnit: { code: string; id: string } };
  };
};

export type THandoverCalender = {
  primaryTransaction?: { propertyUnit: { code: string; id: string }; customer?: TCustomer };
  id?: string;
  ownershipCertificateId?: string;
};

export type TEmployee = {
  id?: string;
  code?: string;
  email?: string;
  name?: string;
  phone?: string;
  pos?: {
    name: string;
    id: string;
  };
};

export type TCreateHandoverCalender = {
  id?: string;
  handoverTime?: string;
  handoverApartment?: THandoverCalender;
  supportEmployee?: TEmployee;
  handoverStartTime?: string;
  handoverEndTime?: string;
  deliveryId?: string;
  status?: string;
  ownershipCertificateId?: string;
};

export type TDetailHandoverProduct = {
  id?: string;
  deliveryConfirmTimes?: number;
  handoverTime?: string;
  handoverApartment?: THandoverCalender;
  supportEmployee?: TEmployee;
  handoverStartTime?: string;
  handoverEndTime?: string;
  deliveryId?: string;
  status?: string;
  deliveryHistories?: TDeliveryHistory[];
  deliveryItems?: TDeliveryItem[];

  primaryTransaction?: {
    propertyUnit: { code: string; block: string; floor: string; area: string; direction: string };
    customer?: TCustomer;
    project?: TProject;
  };
  type?: string;
  handoverStatus?: string;
  deliveryDate?: string;
  files?: RcFile[];

  cerHandoverStatus?: string;

  cerInProcessItems?: TDeliveryItem[];
  cerReadyHandoverItems?: TDeliveryItem[];
  eligibleItems?: TDeliveryItem[];
};

export type TDeliveryHistoryItemList = {
  description?: string;
  indexValue?: string;
  isPass?: boolean;
  reason?: string;
  title?: string;
  type?: string;
  imageFile?: UploadFile[];
  id?: string;
  key?: string;
};
export type TDeliveryHistoryItem = {
  name?: string;
  list?: TDeliveryHistoryItemList[];
};

export type TDeliveryHistory = {
  items?: TDeliveryHistoryItem[];
  key: string;
};

export type TDeliveryItem = {
  name: string;
  list?: TDeliveryHistoryItemList[];
  children?: TDeliveryHistoryItemList[];
  id: string;
  isPass?: boolean;
  parentIndex?: string;
};

export type TListHandoverUnit = {
  primaryTransaction?: TPrimaryTransaction;
  supportEmployee?: TEmployee;
  deliveryHistories?: TDeliveryHistory[];
};

export type TSendEmail = {
  id?: string;
  contracts?: { id?: string }[];
};

//Type GroupHistory
export interface ListItem {
  files: RcFile[];
  isPass: boolean;
  title: string;
  type: 'index' | 'category';
  indexValue: string;
  description: string;
  reason: string;
  image?: string;
}

export interface HistoryItem {
  name: string;
  list: ListItem[];
}

export interface History {
  items: HistoryItem[];
  deliveryDate?: string;
  isPass?: boolean;
  files?: RcFile[];
}
export interface GroupedHistory {
  historyIndex: number;
  groupType: GroupType;
  deliveryDate: string | null;
  items: HistoryItem[]; // Keep original grouping per item
}

export interface GroupedResult {
  index: GroupedHistory[];
  categoryPass: GroupedHistory[];
  categoryFail: GroupedHistory[];
}

export type TAttachment = {
  name?: string;
  file?: RcFile;
  url?: string;
  id?: string;
  uid?: string;
  key?: string;
};

export type TFilterListHandoverUnit = {
  handoverStatus?: string[];
  certHandoverStatus?: string[];
  search?: string;
  blocks?: string[];
  floors?: string[];
  rooms?: string[];
};

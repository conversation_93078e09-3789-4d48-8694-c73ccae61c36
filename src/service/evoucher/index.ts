import { RcFile } from 'antd/lib/upload';
import { axiosInstance, deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { IEVoucher } from '../../types/eVoucher';

export const getListEVoucher = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/admin/evoucher`,
    params as Record<string, unknown> | undefined,
  );
};

export const changeStatusEVoucher = async (data: { id: string; isActive: number }) => {
  return await putRequest(
    `${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/admin/evoucher/change-status`,
    data as Record<string, unknown>,
  );
};

export const deleteEVoucher = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(`${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/admin/evoucher`, {
    id: payload.id,
    reasonDelete: payload.softDeleteReason,
  });
};

export const applyEVoucher = async (data: { id: string }) => {
  return await putRequest(
    `${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/admin/evoucher/apply`,
    data as Record<string, unknown>,
  );
};

export const exportEVoucher = async (params: unknown) => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/admin/evoucher/report-e-voucher/export`,
    {
      params: { ...(params as Record<string, unknown> | undefined), page: undefined },
      responseType: 'arraybuffer',
    },
  );
};

export const getDetailEVoucher = async (id: string | undefined) => {
  const response = await getRequest(`${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/admin/evoucher/${id}`);
  return response;
};

export const createEVoucher = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/admin/evoucher`,
    payload as Record<string, unknown> | undefined,
  );
};

export const updateEVoucher = async (payload: IEVoucher) => {
  return await putRequest(`${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/admin/evoucher`, payload);
};

export const uploadImage = async (file: RcFile, path: string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('path', path);

  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_e_voucher}/api/v1/public/evoucher/upload-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const getEmployees = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/public/evoucher/employee`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getOrgCharts = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_e_voucher}/${typeQueryVersionApi.api_v1}/public/evoucher/get-dropdown-orgcharts`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

import { RcFile } from 'antd/lib/upload';
import { axiosInstance, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { ICustomers } from '../../types/customers';
import {
  IDelivery,
  IFilterDelivery,
  TDetailHandoverProduct,
  THandoverCalender,
  TSendEmail,
} from '../../types/delivery';

export const getListDelivery = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/handover`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailDelivery = async (params: IFilterDelivery) => {
  const { projectId, ...restPayload } = params;
  const response = await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.query}/propertyUnit/primary/assign/${projectId}`,
    { syncErpDataCode: restPayload?.syncErpDataCode } as Record<string, unknown> | undefined,
  );
  return response;
};

export const getDetailBlock = async (id: string | undefined) => {
  const response = await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.query}/propertyUnit/${id}`);
  return response;
};

export const getBlockByProjectId = async (params: unknown) => {
  const { projectId } = params as Record<string, unknown>;
  const response = await getRequest(`${urlDomainApi.msx_property}/${typeQueryVersionApi.api_v2}/project/${projectId}`);
  return response;
};

export const getListHandoverUnit = async (params?: unknown) => {
  const { deliveryId } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/primary-contract/handover/${deliveryId}`,
    params as Record<string, unknown>,
  );
  return response;
};

export const getDetailHandoverUnit = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/primary-contract/${id}`);
};

export const changeXDStatus = async (data: ICustomers) => {
  return await putRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.domain}/propertyUnit/changeStatusXD`,
    data as Record<string, unknown>,
  );
};

export const getListHandoverSchedule = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/handover-schedule`,
    params as Record<string, unknown>,
  );
  return response;
};

export const getListEmployee = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/handover-schedule/employee`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getListHandoverByStatus = async (params?: unknown) => {
  const { id } = params as Record<string, unknown>;
  const response = await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/primary-contract/handover-by-status/${id}`,
  );
  return response;
};

export const getDetailDeliveryConfig = async (params?: unknown) => {
  const { deliveryId, handoverStartTime, type, ownershipCertificateId } = params as Record<string, unknown>;

  return (await type) === 'handover'
    ? getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/handover/${deliveryId}`, {
        handoverStartTime: handoverStartTime,
      })
    : getRequest(
        `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v1}/ownership-certificate/${ownershipCertificateId}`,
        {
          handoverStartTime: handoverStartTime,
        },
      );
};

export const createHandoverSchedule = async (data: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/handover-schedule/create-handover-schedule`,
    data as Record<string, unknown>,
  );
};

export const updateHandoverSchedule = async (payload: THandoverCalender) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/handover-schedule/update-handover-schedule`,
    payload,
  );
};

export const exportHandoverUnitTab = async (id: string) => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/primary-contract/handover/report/${id}`,
    {
      //   params: params as Record<string, unknown> | undefined,
      responseType: 'arraybuffer',
    },
  );
};

export const sendDeliveryNotify = async (payload: TSendEmail) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/primary-contract/sendDeliveryNotify`,
    payload,
  );
};

export const deliveryConfirm = async (payload: IDelivery) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/primary-contract/deliveryConfirm`,
    payload,
  );
};

export const uploadFile = async (files: RcFile[]) => {
  const formData = new FormData();
  files?.map(file => {
    formData.append(`file`, file);
  });

  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_primary_contract}/api/domain/v1/primary-contract/upload-file`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const uploadImgReason = async (files: RcFile[]) => {
  const formData = new FormData();
  files?.map(file => {
    formData.append(`file`, file);
  });

  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_primary_contract}/api/domain/v1/primary-contract/upload-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const confirmDelivery = async (data: TDetailHandoverProduct) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/primary-contract/delivery-confirm-ownership-certificate`,
    data as Record<string, unknown>,
  );
};

export const uploadMultipleFile = async (files: RcFile[]) => {
  const formData = new FormData();
  files?.map(file => {
    formData.append(`file`, file);
  });

  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_primary_contract}/api/domain/v1/primary-contract/upload-multiple-files`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

import { RcFile } from 'antd/es/upload';
import { axiosInstance, deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TPayloadUpdateStatus } from '../../types/commission';

export const getAllOfCommission = async (params: unknown) => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission`, {
    ...(params as Record<string, unknown> | undefined),
    type: 'FEE',
  });
};

export const softDeleteCommission = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission/${payload.id}`,
    payload as Record<string, unknown> | undefined,
  );
};

export const getDetailOfCommission = async (params: unknown) => {
  const { id, restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission/${id}`,
    restParams as Record<string, unknown>,
  );
};
export const getDetailOfCommissionPublish = async (params: unknown) => {
  const { id, restParams } = params as Record<string, unknown>;
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission/get-limited/${id}`,
    restParams as Record<string, unknown>,
  );
};

export const getSalesPolicyOfCommission = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission/get-commission-policy`,
    params as Record<string, unknown> | undefined,
  );
};

export const getExpenseList = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/expense-list`,
    params as Record<string, unknown> | undefined,
  );
};

export const createCommission = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission`,
    payload as Record<string, unknown> | undefined,
  );
};

export const updateCommission = async (payload: unknown) => {
  return await putRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission`, payload);
};

export const downloadListTransaction = async (params: unknown) => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/expense-list/downloadListTransaction`,
    {
      params: params as Record<string, unknown> | undefined,
      responseType: 'arraybuffer',
    },
  );
};

export const updateStatusCommission = async (payload: {
  id?: string;
  status?: string;
  adjustmentVersionId?: string;
  cancelPublishReason?: string;
}) => {
  return await putRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission/update-status`,
    payload,
  );
};
export const updateAnNounced = async (payload?: TPayloadUpdateStatus) => {
  return await putRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission/announced/${payload?.id}`,
    payload,
  );
};

export const postExpenseDraft = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/expense-list/calc-data`,
    payload,
  );
};

export const getListHistoryImportCommissions = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/importHistory`,
    params as Record<string, unknown> | undefined,
  );
};

export const getAllProjectsOfCommission = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission/get-all-project`,
    params as Record<string, unknown> | undefined,
  );
};

export const getByIdProjectsOfCommission = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_commission}/${typeQueryVersionApi.query}/commission/get-project/${id}`);
};

export const uploadFileVersion = async (file: RcFile, id: string) => {
  const formData = new FormData();
  formData.append('files', file);
  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/commission-list/import?commissionId=${id}`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const getListEmployeeAll = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_demand}/${typeQueryVersionApi.query}/employee/dropdown`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const postMergeData = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_commission}/${typeQueryVersionApi.domain}/expense-list/merge-data`,
    payload,
  );
};

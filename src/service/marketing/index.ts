import { RcFile } from 'antd/lib/upload';
import { axiosInstance, deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';
import { TUpdateMarketing } from '../../types/marketing';

export const getListMarketing = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing`,
    params as Record<string, unknown> | undefined,
  );
};

export const changeStatusMarketing = async (data: { id: string; isActive: number }) => {
  return await putRequest(
    `${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/admin/evoucher/change-status`,
    data as Record<string, unknown>,
  );
};

export const deleteMarketing = async (payload: { [key: string]: unknown }) => {
  return await deleteRequest(`${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing`, {
    id: payload.id,
    reasonDelete: payload.softDeleteReason,
  });
};

export const applyMarketing = async (data: { id: string }) => {
  return await putRequest(
    `${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/admin/evoucher/apply`,
    data as Record<string, unknown>,
  );
};

export const exportMarketing = async () => {
  return await axiosInstance.get(
    `${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/admin/evoucher/report-e-voucher/export`,
    {
      //   params: params as Record<string, unknown> | undefined,
      responseType: 'arraybuffer',
    },
  );
};

export const getDetailMarketing = async (id: string | undefined) => {
  const response = await getRequest(`${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing/${id}`);
  return response;
};

export const createMarketing = async (payload: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing`,
    payload as Record<string, unknown> | undefined,
  );
};

export const updateMarketing = async (payload: TUpdateMarketing) => {
  return await putRequest(`${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing`, payload);
};

export const uploadImage = async (file: RcFile, path: string) => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('path', path);

  try {
    const response = await axiosInstance.post(
      `${urlDomainApi.msx_marketing}/api/v1/public/evoucher/upload-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      },
    );
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const getEmployees = async (params?: unknown) => {
  const response = await getRequest(
    `${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/public/evoucher/employee`,
    params as Record<string, unknown> | undefined,
  );
  return response;
};

export const getProjects = async () => {
  return await getRequest(`${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing/project`);
};

export const getCostCenter = async () => {
  return await getRequest(`${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing/cost-center`);
};

export const getCostItem = async () => {
  return await getRequest(`${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing/cost-item`);
};

export const uploadFile = async (files: RcFile[]) => {
  const formData = new FormData();
  files?.map(file => {
    formData.append(`file`, file);
  });

  try {
    const response = await axiosInstance.post(`${urlDomainApi.msx_marketing}/api/v1/marketing/upload-file`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response;
  } catch (error) {
    console.error('Lỗi khi tải lên:', error);
    throw error;
  }
};

export const getListProject = async () => {
  return await getRequest(`${urlDomainApi.msx_marketing}/${typeQueryVersionApi.api_v1}/marketing/project`);
};

export const getListCostCenter = async () => {
  return await getRequest(`${urlDomainApi.msx_masterdata_producer}/${typeQueryVersionApi.api_v1}/cost-center/get-all`);
};

export const getListCostItem = async () => {
  return await getRequest(`${urlDomainApi.msx_masterdata_producer}/${typeQueryVersionApi.api_v1}/cost-item/get-all`);
};

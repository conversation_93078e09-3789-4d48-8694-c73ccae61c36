import { deleteRequest, getRequest, postRequest, putRequest } from '..';
import { typeQueryVersionApi, urlDomainApi } from '../../configs/domainApi';

export const getListLiquidation = async (params: unknown) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/liquidation`, {
    ...(params as Record<string, unknown> | undefined),
  });
};
export const getListProjectAdmin = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_property}/${typeQueryVersionApi.query}/project/admin`,
    params as Record<string, unknown> | undefined,
  );
};
export const getListContract = async (params: unknown) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/primary-contract?status=approved&searchInLiquidation=true`,
    params as Record<string, unknown> | undefined,
  );
};
export const createLiquidation = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/liquidation`,
    params as Record<string, unknown> | undefined,
  );
};
export const getDetailLiquidation = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.query}/liquidation/${id}`);
};

export const updateLiquidation = async (params: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/liquidation`,
    params as Record<string, unknown> | undefined,
  );
};
export const requestApproveLiquidation = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/liquidation/requestApproveLiquidation`,
    params as Record<string, unknown> | undefined,
  );
};
export const approveLiquidation = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/liquidation/approveLiquidation`,
    params as Record<string, unknown> | undefined,
  );
};

export const getListLiquidationProposal = async (params: unknown) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/proposal`, {
    ...(params as Record<string, unknown> | undefined),
  });
};
export const createLiquidationProposal = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/proposal`,
    params as Record<string, unknown> | undefined,
  );
};
export const getDetailLiquidationProposal = async (id: string) => {
  return await getRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/proposal/${id}`);
};
export const requestApproveLiquidationProposal = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/proposal/requestApproveProposal`,
    params as Record<string, unknown> | undefined,
  );
};
export const approveLiquidationProposal = async (params: unknown) => {
  return await postRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/proposal/approveProposal`,
    params as Record<string, unknown> | undefined,
  );
};

export const updateLiquidationProposal = async (params: unknown) => {
  return await putRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/proposal`,
    params as Record<string, unknown> | undefined,
  );
};
export const getLiquidationProposalByContract = async (contractId: string) => {
  return await getRequest(
    `${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/proposal/getByContract/${contractId}`,
  );
};
export const deleteLiquidationProposal = async (id: string) => {
  return await deleteRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.api_v2}/proposal/${id}`);
};
export const deleteLiquidationContract = async (id: string) => {
  return await deleteRequest(`${urlDomainApi.msx_primary_contract}/${typeQueryVersionApi.domain}/liquidation/${id}`);
};

import { CompatClient, Message, Stomp } from '@stomp/stompjs';
import { useEffect, useRef } from 'react';
import SockJS from 'sockjs-client';

interface IStompClient {
  url: string;
  username?: string;
  debug?: boolean;
  onMessageReceived: (message: Message) => void;
  onConnect?: (client: CompatClient) => void;
  onDisconnect?: (client: CompatClient) => void;
}

const useStompClient = ({ url, username, onMessageReceived, debug = false, onConnect, onDisconnect }: IStompClient) => {
  const clientRef = useRef<CompatClient | null>(null);

  useEffect(() => {
    const connect = () => {
      const socket = () => new SockJS(`${url}/noti-socket/noti`);
      const client = Stomp.over(socket);

      if (!debug) client.debug = () => {};
      client.reconnectDelay = 1000;

      client.connect(
        {},
        () => {
          client.subscribe(`/topic/crm/${username}`, message => {
            onMessageReceived(message);
          });
          if (onConnect) onConnect(client);
        },
        (error: unknown) => {
          if (debug) console.error('[STOMP] Error, will attempt reconnect', error);
        },
      );

      clientRef.current = client;
    };

    connect();

    return () => {
      if (onDisconnect && clientRef.current) {
        onDisconnect(clientRef.current);
      }
      clientRef.current?.disconnect(() => {});
    };
  }, [url, username, onMessageReceived, debug, onConnect, onDisconnect]);
};

export default useStompClient;

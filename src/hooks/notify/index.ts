import { CompatClient, Message, Stomp } from '@stomp/stompjs';
import { useEffect, useRef } from 'react';
import SockJS from 'sockjs-client';

interface IStompClient {
  url: string;
  username?: string;
  debug?: boolean;
  onMessageReceived: (message: Message) => void;
}

const useStompClient = ({ url, username, onMessageReceived, debug = false }: IStompClient) => {
  const clientRef = useRef<CompatClient | null>(null);

  useEffect(() => {
    const connect = () => {
      const socket = () => new SockJS(`${url}/noti-socket/noti`);
      const client = Stomp.over(socket);

      if (!debug) client.debug = () => {};
      client.reconnectDelay = 1000;

      client.connect(
        {},
        () => {
          if (debug) console.log('[STOMP] Connected');
          client.subscribe(`/topic/crm/${username}`, message => {
            console.log('message :', message);
            onMessageReceived(message);
          });
        },
        (error: unknown) => {
          if (debug) console.error('[STOMP] Error, will attempt reconnect', error);
          // reconnectDelay will handle reconnection
        },
      );

      clientRef.current = client;
    };

    connect();

    return () => {
      clientRef.current?.disconnect(() => {
        if (debug) console.log('[STOMP] Disconnected');
      });
    };
  }, [url, username, onMessageReceived, debug]);
};

export default useStompClient;

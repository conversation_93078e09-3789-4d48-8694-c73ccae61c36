import { Form } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useState } from 'react';
import DatePickerFilter from '../../../components/datePicker/DatePickerFilter';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import { DEFAULT_PARAMS, FORMAT_DATE_API } from '../../../constants/common';
import useFilter from '../../../hooks/filter';
import { TFilterTraining } from '../../../types/training/training';

const FilterSearchTraining = ({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<TFilterTraining | undefined>>;
}) => {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [, setFilter] = useFilter();

  const handleSubmitFilter = (values: TFilterTraining) => {
    const newFilter: Record<string, unknown> = {
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
      // createdBy: values?.createdBy ? values.createdBy : null,
    };
    setFilterParams(prev => ({ ...prev, ...newFilter }));
    setFilter(DEFAULT_PARAMS);
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setIsOpenFilter(false);
      setFilterParams(prev => ({ search: prev?.search }));
      setFilter({});
    }, 100);
  };

  // const handleSelectEmployee = (values: { option: TEmployeeAll }[]) => {
  //   form.setFieldsValue({
  //     createdBy: values
  //       ?.map(item => item?.option.accountId)
  //       .filter(item => item !== undefined)
  //       .join(','),
  //   });
  // };

  const onChangeSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, search: search }));
    setFilter(DEFAULT_PARAMS);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        onChangeSearch={onChangeSearch}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            {/* <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListAllEmployeeDropdown}
                queryKey={['employee-dropdown']}
                keysLabel={['username', 'name']}
                handleListSelect={handleSelectEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'username'}
              />
            </Form.Item> */}
            <DatePickerFilter startDate="startDate" endDate="endDate" />
          </>
        }
      />
    </>
  );
};

export default FilterSearchTraining;

import { App, But<PERSON>, Flex, notification, Space } from 'antd';
import { ColumnsType } from 'antd/es/table';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { PERMISSION_TRAINING } from '../../../constants/permissions/training';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { getAllTraining, putStatusTraining, startEvent } from '../../../service/training';
import { TFilterTraining, TListOfTraining } from '../../../types/training/training';
import ModalCreateTraining from '../createTraining';
import { useStoreTraining } from '../storeTraining';
import { Columns } from './Columns';
import FilterSearchTraining from './FilterSearchTraining';
import './styles.scss';
import { useEffect, useState } from 'react';
import { LIVE_STREAM_TRAINING, TRAINING } from '../../../configs/path';
import { useMutation } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';

const ListOfTraining = () => {
  const { create, game: changeStatus, getId: checkGetDetail } = useCheckPermissions(PERMISSION_TRAINING);
  const { setOpenModalCreate, setInitialValues } = useStoreTraining();
  const { modal } = App.useApp();
  const navigate = useNavigate();
  const [filterParams, setFilterParams] = useState<TFilterTraining>();

  useEffect(() => {
    setInitialValues();
  }, [setInitialValues]);

  const { data, isLoading } = useFetch<TListOfTraining[]>({
    queryKeyArrWithFilter: ['training', filterParams],
    api: getAllTraining,
    moreParams: { ...filterParams },
  });

  const { mutateAsync, isPending } = useUpdateField({
    keyOfListQuery: ['training'],
    apiQuery: putStatusTraining,
    isMessageError: false,
    isMessageSuccess: false,
  });

  const dataSource = data?.data?.data?.rows || [];

  const { mutateAsync: start } = useMutation({
    mutationFn: startEvent,
  });

  const actionColumns: ColumnsType<TListOfTraining> = [
    ...Columns,
    {
      title: 'Hành động',
      key: 'actionButton',
      render: (_, record) => {
        return (
          <div className="action-button-training">
            <Space>
              <Button
                className="btn-purple"
                disabled={record?.isActive === 2 ? true : false}
                onClick={async () => {
                  const res = await start(record?.urlEvent);
                  if (res?.data?.statusCode === '0') {
                    navigate(`${LIVE_STREAM_TRAINING}/${record?.urlEvent}`);
                    window.location.reload();
                  }
                }}
              >
                Bắt đầu
              </Button>
              <Button
                className="btn-orange"
                onClick={() => {
                  navigate(`${TRAINING}/${record?.id}/prize-spin`);
                  window.location.reload();
                }}
              >
                Quay thưởng
              </Button>
              <Button className="btn-blue">Đăng ký</Button>
            </Space>
          </div>
        );
      },
    },
    {
      dataIndex: 'action',
      key: 'action',
      width: '50px',
      align: 'center',
      render: (_, record) => {
        const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
        const handleActiveTraining = () => {
          return modalConfirm({
            modal: modal,
            loading: isPending,
            title: `${textModalConfirmActive} sự kiện`,
            content: `Bạn có muốn ${textModalConfirmActive} sự kiện này không?`,
            handleConfirm: async () => {
              const res = await mutateAsync({
                id: record?.id,
                isActive: record?.isActive === 1 ? 2 : 1,
              });
              if (res?.data?.statusCode === '0') {
                notification.success({ message: `${textModalConfirmActive} sự kiện thành công` });
              }
            },
          });
        };

        const openViewDetail = () => {
          window.open(`${TRAINING}/${record?.id}`, '_blank', 'noopener,noreferrer');
        };

        return (
          <ActionsColumns
            handleViewDetail={checkGetDetail ? openViewDetail : undefined}
            textModalConfirmActive={textModalConfirmActive}
            handleActive={changeStatus ? handleActiveTraining : undefined}
          />
        );
      },
    },
  ];
  const handleOpenModalCreate = () => {
    setOpenModalCreate(true);
  };
  const handleCancelModalCreate = () => {
    setOpenModalCreate(false);
  };

  return (
    <div className="wrapper-list-of-training">
      <BreadCrumbComponent />
      <Flex className="header-filter-training" justify="space-between" style={{ marginBottom: 16 }}>
        <FilterSearchTraining setFilterParams={setFilterParams} />
        {create && (
          <Button type="primary" onClick={handleOpenModalCreate}>
            Tạo mới
          </Button>
        )}
      </Flex>
      <div className="table-training">
        <TableComponent
          queryKeyArr={['training', filterParams]}
          rowKey="id"
          dataSource={dataSource}
          columns={actionColumns}
          scroll={{ x: '1500px' }}
          loading={isLoading}
        />
      </div>

      <ModalCreateTraining onCancel={handleCancelModalCreate} />
    </div>
  );
};

export default ListOfTraining;

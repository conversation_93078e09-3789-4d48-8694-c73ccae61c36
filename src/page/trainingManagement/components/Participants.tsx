import { DeleteOutlined } from '@ant-design/icons';
import { App, Button, Checkbox, Col, Form, notification, Radio, Table, TableColumnsType, Typography } from 'antd';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { OPTIONS_ATTEND_ACCORDINGLY } from '../../../constants/common';
import { useDeleteField, useFetch } from '../../../hooks';
import { getListEmployeeAll } from '../../../service/customers';
import { getOrgChartDropdown } from '../../../service/lead';
import { deleteUserEvent, getAllUserEvent } from '../../../service/training';
import { TEmployeeAll } from '../../../types/customers';
import { IUserEvent, TTableUserEvent } from '../../../types/training/training';
import { useStoreTraining } from '../storeTraining';

const { Item } = Form;
const { Text } = Typography;

function groupByPosId(data: IUserEvent[]) {
  const map = new Map<string, TTableUserEvent>();
  for (const item of data) {
    const { posId, dvbh } = item;
    if (!map.has(posId)) {
      map.set(posId, {
        id: posId,
        posName: dvbh,
        children: [],
      });
    }
    map.get(posId)?.children?.push(item);
  }
  return Array.from(map.values());
}

const columns: TableColumnsType = [
  Table.EXPAND_COLUMN,
  {
    title: 'Sàn',
    dataIndex: 'posName',
    width: '30%',
    render: text => <Text>{text}</Text>,
  },
  {
    title: 'Tài khoản',
    width: '30%',
    dataIndex: 'username',
  },
  {
    title: 'Họ tên',
    width: '30%',
    dataIndex: 'name',
  },
];

const Participants = () => {
  const { id } = useParams();
  const { modal } = App.useApp();
  const { openModalCreate, initialValues, setIsModified } = useStoreTraining();
  const [dataAllUserEvent, setDataAllUserEvent] = useState<TTableUserEvent[]>();

  const form = Form.useFormInstance();

  const { data } = useFetch<IUserEvent[]>({
    api: getAllUserEvent,
    queryKeyArr: ['getAllUserEvent', id],
    moreParams: { id, isEmployee: true },
    withFilter: false,
    enabled: !!id,
  });
  const dataSource = data?.data?.data;

  const { mutateAsync: deleteUser, isPending } = useDeleteField({
    apiQuery: deleteUserEvent,
    keyOfDetailQuery: ['getAllUserEvent', id],
    isMessageError: false,
    isMessageSuccess: false,
  });
  const actionColumns: TableColumnsType = [
    ...columns,
    {
      title: '',
      width: '5%',
      dataIndex: 'actions',
      render: (_, record) => {
        const handleDelete = async () => {
          return modalConfirm({
            modal: modal,
            loading: isPending,
            title: `Xóa nhân viên`,
            content: `Bạn có muốn xóa nhân viên này không?`,
            handleConfirm: async () => {
              const res = await deleteUser(record?.id);
              if (res?.data?.statusCode === '0') {
                notification.success({ message: `xóa nhân viên thành công` });
              }
            },
          });
        };

        return !record?.children && <Button type="text" danger icon={<DeleteOutlined />} onClick={handleDelete} />;
      },
    },
  ];
  useEffect(() => {
    if (dataSource) {
      const newData = groupByPosId(dataSource) || [];
      setDataAllUserEvent(newData);
    }
  }, [dataSource]);

  const handleSelectListCustomer = (values: TEmployeeAll) => {
    form.setFieldsValue({ listCustomer: values });
    setIsModified(true);
  };

  const handleSelectListAdmin = (values: { option: TEmployeeAll }[]) => {
    form.setFieldsValue({
      listAdmin: values?.map(item => ({
        ...item?.option,
      })),
    });
    setIsModified(true);
  };
  return (
    <>
      <Col span={24}>
        <h3>Thành phần tham dự</h3>
      </Col>
      <Col span={24}>
        <Item label="Quản trị viên" name="listAdmin">
          <MultiSelectLazy
            enabled={!!openModalCreate || !!id}
            apiQuery={getListEmployeeAll}
            queryKey={['employee-dropdown']}
            keysLabel={['name', 'username']}
            handleListSelect={handleSelectListAdmin}
            defaultValues={
              initialValues?.listAdmin && initialValues?.listAdmin?.length > 0
                ? initialValues?.listAdmin.map(item => ({
                    ...item,
                    label: item?.name || '',
                    value: item?.id || '',
                  }))
                : []
            }
            placeholder="Chọn nhân viên"
            keysTag={['username']}
          />
        </Item>
      </Col>
      <Col span={24}>
        <Item label="Tham dự theo" name="Attend">
          <Radio.Group options={OPTIONS_ATTEND_ACCORDINGLY} />
        </Item>
      </Col>
      <Col span={24}>
        {/* <Item name="allowRegisterCustomer" valuePropName="checked"> */}
        <Checkbox>Cho phép ĐVBH đăng ký khách hàng</Checkbox>
        {/* </Item> */}
      </Col>
      <Col span={24}>
        <Form.Item noStyle shouldUpdate={(prevValues, currentValues) => prevValues.Attend !== currentValues.Attend}>
          {({ getFieldValue }) => {
            const attendType = getFieldValue('Attend');

            if (!attendType) return null; // Don't render if no selection is made

            const apiFunction = attendType === 'orgChart' ? getOrgChartDropdown : getListEmployeeAll;

            return (
              <Item label="Đơn vị/ NVKD tham dự" name="listCustomer">
                <SingleSelectLazy
                  apiQuery={apiFunction}
                  enabled={!!openModalCreate || !!id}
                  queryKey={['dropdown', attendType]}
                  keysLabel={['name']}
                  handleSelect={handleSelectListCustomer}
                  placeholder={attendType === 'orgChart' ? 'Chọn đơn vị' : 'Chọn nhân viên'}
                />
              </Item>
            );
          }}
        </Form.Item>
      </Col>
      <Col span={24}>
        <>
          <Text strong>
            Đơn vị: {dataAllUserEvent?.length} &nbsp;&nbsp;&nbsp; NVKD: {dataSource?.length}
          </Text>
          <Table
            className="table-participants"
            columns={actionColumns}
            expandable={{
              expandedRowRender: () => <p></p>,
              rowExpandable: record => record.children?.length > 0,
            }}
            dataSource={dataAllUserEvent}
            pagination={false}
            rowKey="id"
          />
        </>
      </Col>
    </>
  );
};

export default Participants;

import { Col, Form, FormInstance, Row } from 'antd';
import { useParams } from 'react-router-dom';
import { useStoreTraining } from '../../../storeTraining';
import ContactTraining from '../../Contact';
import DropdownReport from '../../DropdownReport';
import InformationTraining from './conponents/InformationTraining';
import './styles.scss';

interface Props {
  form: FormInstance;
}

const TabGeneralInfo = (props: Props) => {
  const { id } = useParams();
  const { form } = props;
  const { initialValues, setIsModified } = useStoreTraining();
  console.log('initialValues :', initialValues);

  return (
    <Form
      form={form}
      layout="vertical"
      className="wrapper-tab-general-information"
      onFieldsChange={() => {
        setIsModified(true);
      }}
      initialValues={{
        ...initialValues,
        isActive: initialValues?.isActive || 1,
        eventType: initialValues?.eventType || 'ONLINE',
      }}
    >
      <Row gutter={24}>
        {id && <DropdownReport />}
        <Col xs={24} xl={12}>
          <InformationTraining />
        </Col>
        <Col xs={24} xl={12}>
          <ContactTraining />
        </Col>
      </Row>
    </Form>
  );
};

export default TabGeneralInfo;

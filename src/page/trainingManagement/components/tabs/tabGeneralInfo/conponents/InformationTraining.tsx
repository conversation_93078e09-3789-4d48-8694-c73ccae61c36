import { Col, Form, Input, Radio, Row, Select } from 'antd';
import { RadioChangeEvent } from 'antd/lib';
import { useParams } from 'react-router-dom';
import MultiSelectLazy from '../../../../../../components/select/mutilSelectLazy';
import { OPTIONS_EVENT_TYPE, OPTIONS_STATUS_COMMON } from '../../../../../../constants/common';
import { getOrgChartDropdown } from '../../../../../../service/lead';
import { TListDropdown } from '../../../../../../types/salesPolicy';
import { TEventType } from '../../../../../../types/training/training';
import { useStoreTraining } from '../../../../storeTraining';
import InputPositiveFormItem from '../../../InputPositiveFormItem';
import InformationTrainingOnline from './InformationTrainingOnline';
import InformationTrainingOffline from './InformationTrainingOffline';
import Participants from '../../../Participants';
import FormUploadImage from '../../../../../../components/upload/FormUploadImage';
import { PlusOutlined } from '@ant-design/icons';

const { Item } = Form;

const InformationTraining = () => {
  const { id } = useParams();
  const form = Form.useFormInstance();
  const { eventType, setEventType, openModalCreate, initialValues, setIsModified } = useStoreTraining();

  const handleChangeEventType = (e: RadioChangeEvent) => {
    setEventType(e.target.value as TEventType);
    setIsModified(true);
  };

  const handleSelectListPos = (values: TListDropdown[]) => {
    form.setFieldsValue({
      saleUnitNames: values ? values.map(value => ({ id: value?.id, name: value?.name })) : undefined,
    });
    setIsModified(true);
  };
  return (
    <Row gutter={24}>
      <Col span={24}>
        <h3>Thông tin chung</h3>
      </Col>
      <Col span={24}>
        <Item
          label="Tên sự kiện"
          name="name"
          rules={[{ required: true, message: 'Vui lòng nhập Tên sự kiện', whitespace: true }]}
        >
          <Input placeholder="Nhập tên sự kiện" />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Mã sự kiện" name="code">
          <Input disabled />
        </Item>
      </Col>
      <Col span={12}>
        <Item label="Trạng thái" name="isActive" rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}>
          <Select options={OPTIONS_STATUS_COMMON} />
        </Item>
      </Col>
      <Col span={24}>
        <Item label="Loại sự kiện" name="eventType" labelAlign="left" layout={'horizontal'} labelCol={{ span: 8 }}>
          <Radio.Group
            disabled={!!id}
            options={OPTIONS_EVENT_TYPE}
            onChange={handleChangeEventType}
            defaultValue={'ONLINE'}
            value={eventType}
          />
        </Item>
      </Col>
      {eventType === 'ONLINE' && <InformationTrainingOnline />}
      {eventType === 'OFFLINE' && <InformationTrainingOffline />}
      <Col span={24}>
        <InputPositiveFormItem
          label="Số lượng đăng ký tối đa"
          placeholder="Nhập số lượng đăng ký tối đa"
          name="timeSlotNames"
        />
      </Col>
      <Col span={24}>
        <Item label="Danh sách đơn vị bán hàng" name="saleUnitNames">
          <MultiSelectLazy
            apiQuery={getOrgChartDropdown}
            enabled={!!openModalCreate || !!id}
            queryKey={['orgChart-exchanges']}
            keysLabel={'name'}
            placeholder="Thêm đơn vị bán hàng"
            handleListSelect={handleSelectListPos}
            keysTag={'name'}
            defaultValues={initialValues?.saleUnitNames?.map(item => ({
              name: item?.name,
              label: item?.name,
              value: item?.id,
            }))}
          />
        </Item>
      </Col>
      <Col span={24}>
        <InputPositiveFormItem label="Suất đăng ký" placeholder="Nhập suất đăng ký" name="limitRegister" />
      </Col>
      <Col span={24}>
        <FormUploadImage
          isValidate
          path="image"
          defaultImage={initialValues?.image}
          reset={false}
          fieldName={'image'}
          label="Hình ảnh đại diện"
          fileSize={5}
          textType="Tối đa 5 MB, định dạng: .jpeg, .jpg, .png, .svg, .gif"
          moreTypes={['image/gif', 'image/svg+xml']}
          onChange={() => {
            setIsModified(true);
          }}
          iconUpload={<PlusOutlined style={{ color: '#00000073' }} />}
        />
      </Col>
      {id && <Participants />}
    </Row>
  );
};

export default InformationTraining;

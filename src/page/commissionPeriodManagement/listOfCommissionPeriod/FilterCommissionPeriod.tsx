import { Col, DatePicker, Form, Row } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useState } from 'react';
import DropdownFilterPeriod from '../../../components/dropdown/dropdownFilterPeriod';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { DEFAULT_PARAMS, FORMAT_DATE, FORMAT_DATE_API } from '../../../constants/common';
import useFilter from '../../../hooks/filter';
import { getOrgChartDropdown } from '../../../service/lead';
import { TFilterCommission } from '../../../types/commission';
import { TListDropdown } from '../../../types/salesPolicy';

const FilterCommissionPeriod = ({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<TFilterCommission>>;
}) => {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [, setFilter] = useFilter();

  const handleSubmitFilter = (values: TFilterCommission) => {
    const newFilter: Record<string, unknown> = {
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
      periodName: values?.periodName || null,
      year: values?.year ? values.year : null,
      posName: values?.posName ? values.posName : null,
      // createdBy: values?.createdBy ? values.createdBy : null,
    };
    setFilterParams(prev => ({ ...prev, ...newFilter }));
    setFilter(DEFAULT_PARAMS);
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setIsOpenFilter(false);
      setFilter({});
      setFilterParams(prev => ({ search: prev?.search }));
    }, 100);
  };

  // const handleSelectEmployee = (values: { option: TEmployeeAll }[]) => {
  //   form.setFieldsValue({
  //     createdBy: values
  //       ?.map(item => item?.option?.accountId)
  //       .filter(item => item !== undefined)
  //       .join(','),
  //   });
  // };

  const onChangeSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, search: search }));
    setFilter(DEFAULT_PARAMS);
  };

  const handleSelectListPos = (values: TListDropdown[]) => {
    form.setFieldsValue({
      posName: values?.map(value => value?.name).join(','),
    });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        onChangeSearch={onChangeSearch}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Đơn vị" name="posName">
              <MultiSelectLazy
                apiQuery={getOrgChartDropdown}
                queryKey={['orgChart-exchanges']}
                enabled={isOpenFilter}
                keysTag={'name'}
                keysLabel={'name'}
                placeholder="Chọn đơn vị"
                handleListSelect={handleSelectListPos}
              />
            </Form.Item>
            {/* <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListAllEmployeeDropdown}
                queryKey={['employee-dropdown']}
                keysLabel={['username', 'name']}
                handleListSelect={handleSelectEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'username'}
              />
            </Form.Item> */}
            <DropdownFilterPeriod label="Kỳ tính hoa hồng" fieldName="periodName" />
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endDate = form.getFieldValue('endDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endDate && current > dayjs(endDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startDate = form.getFieldValue('startDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startDate && current < dayjs(startDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterCommissionPeriod;

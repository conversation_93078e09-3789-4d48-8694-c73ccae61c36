import { <PERSON><PERSON>, <PERSON>, <PERSON>, Modal, Row } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import FormPeriod from '../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { COMMISSION_PERIOD_LIST } from '../../../configs/path';
import { useCreateField } from '../../../hooks';
import {
  createCommissionPeriod,
  getAllOfCommissionIndicatorPos,
  getAllOfCommissionPolicyPos,
  getSalesPolicyOfCommissionPeriod,
} from '../../../service/commissionPeriod';
import { getOrgChartDropdown } from '../../../service/lead';
import { TSalePolicy } from '../../../types/commission';
import { ICommissionPolicy } from '../../../types/commissionPolicy';
import { Units } from '../../../types/units/units';
import './styles.scss';
import { useBeforeUnload } from 'react-router-dom';

interface Props {
  handleCancel: () => void;
  open: boolean;
}

const CreateCommissionPeriod = (props: Props) => {
  const { handleCancel, open } = props;
  const [form] = Form.useForm();

  const pos = Form.useWatch('pos', form);
  const period = Form.useWatch('period', form);
  const periodObj = form.getFieldValue('periodObj');
  const year = dayjs(Form.useWatch('year', form)).format('YYYY');
  const salePolicy = Form.useWatch('salePolicy', form);
  const commissionPolicyManager = Form.useWatch('commissionPolicyManager', form);
  const commissionPolicyPersonal = Form.useWatch('commissionPolicyPersonal', form);
  const indicator = Form.useWatch('indicator', form);

  const disabled = useMemo(() => {
    return !!(pos && period && year);
  }, [pos, period, year]);

  const { mutateAsync, isPending } = useCreateField({
    apiQuery: createCommissionPeriod,
    keyOfListQuery: ['list-of-commission-period'],
    isMessageError: false,
  });

  const handleSubmit = async () => {
    const allValues = form.getFieldsValue(true);
    const periodObj = form.getFieldValue('periodObj');

    if (!allValues) return;

    const newData = {
      type: 'COMM',
      ...periodObj,
      pos: {
        name: allValues?.pos?.name,
        code: allValues?.pos?.code,
        id: allValues?.pos?.id,
      },
      salePolicyCode: allValues.salePolicy?.code,
      indicatorCode: allValues.indicator?.code,
      commissionPolicyPersonalCode: allValues.commissionPolicyPersonal?.code,
      commissionPolicyManagerCode: allValues.commissionPolicyManager?.code,
      year: allValues.year,
    };

    const res = await mutateAsync(newData);
    const idCommission = res?.data?.data ? (res.data.data as { id: string }).id : undefined;
    if (res?.data?.statusCode === '0') {
      handleCancel();
      window.open(`${COMMISSION_PERIOD_LIST}/${idCommission}`, '_blank', 'noopener,noreferrer');
    }
  };

  const handleSelectSalePolicy = (value: TSalePolicy) => {
    form.setFieldsValue({ salePolicy: value ? value : undefined });
  };

  const handleSelectSalePos = (value: Units) => {
    form.setFieldsValue({
      pos: value
        ? {
            id: value?.id,
            name: value?.name,
            code: value?.code,
          }
        : undefined,
    });
    handleClearFormFields();
    form.setFieldsValue({
      year: new Date().getFullYear().toString(),
      period: undefined,
    });
  };

  const handleClearFormFields = () => {
    form.setFieldsValue({
      salePolicy: undefined,
      indicator: undefined,
      commissionPolicyPersonal: undefined,
      commissionPolicyManager: undefined,
    });
  };

  const handleSelectCommissionPolicyPosManager = (value: ICommissionPolicy) => {
    form.setFieldsValue({ commissionPolicyManager: value ? value : undefined });
  };

  const handleSelectCommissionPolicyPosPersonal = (value: ICommissionPolicy) => {
    form.setFieldsValue({ commissionPolicyPersonal: value ? value : undefined });
  };

  const handleSelectCommissionIndicatorPos = (value: ICommissionPolicy) => {
    form.setFieldsValue({ indicator: value ? value : undefined });
  };

  useBeforeUnload(event => {
    if (open && form.isFieldsTouched()) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <Modal
      className="wrapper-modal-create-commission"
      title="Tạo mới kỳ tính hoa hồng"
      open={open}
      footer={[
        <Button type="primary" loading={isPending} onClick={() => form.submit()}>
          Tạo mới
        </Button>,
      ]}
      onCancel={() => {
        if (!form.isFieldsTouched()) {
          handleCancel();
          return;
        }
        showConfirmCancelModal({
          onConfirm: handleCancel,
        });
      }}
      destroyOnClose
      maskClosable={false}
      afterClose={() => form.resetFields()}
      width={645}
    >
      <Form form={form} onFinish={handleSubmit} requiredMark={false} layout="vertical">
        <Row gutter={24}>
          <Col span={24}>
            <Form.Item label="Đơn vị" name="pos" rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}>
              <SingleSelectLazy
                apiQuery={getOrgChartDropdown}
                queryKey={['orgChart-exchanges']}
                keysLabel={'name'}
                placeholder="Chọn đơn vị"
                handleSelect={handleSelectSalePos}
              />
            </Form.Item>
          </Col>
          <Col span={24}>
            <FormPeriod
              label="Kỳ tính hoa hồng"
              fieldPos="pos"
              clearFormValueDependency={handleClearFormFields}
              messageValidate="Vui lòng chọn kỳ tính hoa hồng"
            />
          </Col>
          <Col span={12}>
            <Form.Item
              label="Chính sách phí - hoa hồng"
              name="salePolicy"
              rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
            >
              <SingleSelectLazy
                apiQuery={getSalesPolicyOfCommissionPeriod}
                queryKey={['sales-policy']}
                keysLabel={['code', 'name']}
                placeholder="Chọn chính sách phí - hoa hồng"
                handleSelect={handleSelectSalePolicy}
                disabled={!disabled}
                enabled={disabled}
                moreParams={{ name: pos?.name, isActive: 1 }}
                defaultValues={{
                  value: salePolicy?.id,
                  label: salePolicy?.name,
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Chỉ tiêu"
              name="indicator"
              rules={[{ required: true, message: 'Vui lòng chọn chỉ tiêu' }]}
            >
              <SingleSelectLazy
                apiQuery={getAllOfCommissionIndicatorPos}
                queryKey={['indicator']}
                keysLabel={'code'}
                placeholder="Chọn chỉ tiêu"
                handleSelect={handleSelectCommissionIndicatorPos}
                disabled={!disabled}
                enabled={disabled && !!pos?.name && !!period && !!year}
                moreParams={{
                  name: pos?.name,
                  periodFrom: periodObj?.periodFrom,
                  periodTo: periodObj?.periodTo,
                  year: year,
                }}
                defaultValues={{
                  value: indicator?.id,
                  label: indicator?.name,
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Bộ chỉ tiêu KPI - Cá nhân "
              name="commissionPolicyPersonal"
              rules={[{ required: true, message: 'Vui lòng chọn bộ chỉ tiêu KPI - Cá nhân' }]}
            >
              <SingleSelectLazy
                apiQuery={getAllOfCommissionPolicyPos}
                queryKey={['commission-policy-pos']}
                keysLabel={['code', 'name']}
                placeholder="Chọn bộ chỉ tiêu KPI - cá nhân"
                moreParams={{ type: 'Cá nhân', name: pos?.name }}
                handleSelect={handleSelectCommissionPolicyPosPersonal}
                disabled={!disabled}
                enabled={disabled && !!pos?.name}
                defaultValues={{
                  value: commissionPolicyPersonal?.[0]?.id,
                  label: commissionPolicyPersonal?.[0]?.name,
                }}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Bộ chỉ tiêu KPI - Quản lý"
              name="commissionPolicyManager"
              rules={[{ required: true, message: 'Vui lòng chọn bộ chỉ tiêu KPI - Quản lý' }]}
            >
              <SingleSelectLazy
                apiQuery={getAllOfCommissionPolicyPos}
                queryKey={['commission-policy-pos', 'manager']}
                keysLabel={['code', 'name']}
                moreParams={{ type: 'Quản lý', name: pos?.name }}
                placeholder="Chọn bộ chỉ tiêu KPI - Quản lý"
                handleSelect={handleSelectCommissionPolicyPosManager}
                disabled={!disabled}
                enabled={disabled && !!pos?.name}
                defaultValues={{
                  value: commissionPolicyManager?.[0]?.id,
                  label: commissionPolicyManager?.[0]?.name,
                }}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default CreateCommissionPeriod;

import { App, But<PERSON>, Col, Flex, Form, Input, Radio, Row, Spin, Table, Tabs, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { useBeforeUnload, useParams, useSearchParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import FormPeriod from '../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { updateStatusCommission } from '../../../service/commission';
import {
  calculateCommissionPeriod,
  getAllOfCommissionIndicatorPos,
  getAllOfCommissionPolicyPos,
  getByIdCommissionPeriod,
  getSalesPolicyOfCommissionPeriod,
  putCommissionPeriod,
} from '../../../service/commissionPeriod';
import { getOrgChartDropdown } from '../../../service/lead';
import {
  CommissionTransaction,
  TAdjustmentVersion,
  TCommission,
  TDataSubmitCommissionPeriod,
  TFilterTransaction,
  TSalePolicy,
  TUpdateStatusProps,
} from '../../../types/commission';
import { ICommissionPolicy } from '../../../types/commissionPolicy';
import { Units } from '../../../types/units/units';
import FilterTransaction from '../../commissionManagement/detailOfCommission/components/FilterTransaction';
import { transactionColumns, versionColumns } from './columns';
import { GroupButtonAction } from './components/GroupsButtonAction';
import './styles.scss';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME, STATUS_ADJUSTMENT_VERSION } from '../../../constants/common';

const { Title, Text } = Typography;

const formatDataSubmit = (data: TCommission): TDataSubmitCommissionPeriod => {
  return {
    ...data?.periodObj,
    commissionCode: data?.code,
    pos: data?.pos,
    salePolicyCode: data?.salePolicy?.code,
    year: String(data.year),
    indicatorCode: data?.indicator.code,
    commissionPolicyPersonalCode: data?.commissionPolicyPersonal?.code,
    commissionPolicyManagerCode: data?.commissionPolicyManager?.code,
  };
};

const DetailCommissionPeriod = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const account = JSON.parse(sessionStorage.getItem('dataAccount') || '{}');
  const params = Object.fromEntries([...searchParams]);
  const { modal, notification } = App.useApp();
  const { update, getById } = useCheckPermissions(PERMISSION_COMMISSION);
  const [form] = Form.useForm();
  const pos = Form.useWatch('pos', form);
  const salePolicy = Form.useWatch('salePolicy', form);
  const commissionPolicyManager = Form.useWatch('commissionPolicyManager', form);
  const commissionPolicyPersonal = Form.useWatch('commissionPolicyPersonal', form);
  const indicator = Form.useWatch('indicator', form);
  const period = Form.useWatch('period', form);
  const year = Form.useWatch('year', form);
  const periodObj = form.getFieldValue('periodObj');

  const [filterParams, setFilterParams] = useState<TFilterTransaction>();
  const [isModified, setIsModified] = useState(false);
  const [tab, setTab] = useState<'original' | 'adjustment'>(
    params?.tabData === 'original' || params?.tabData === 'adjustment' ? params.tabData : 'original',
  );
  const [initialValue, setInitialValue] = useState<TCommission>({} as TCommission);
  const [dataRoot, setDataRoot] = useState<CommissionTransaction[]>();
  const [selectedAdjustment, setSelectedAdjustment] = useState<TAdjustmentVersion | null>(null);

  const { data, isLoading: isLoadingRoot } = useFetch<TCommission>({
    api: getByIdCommissionPeriod,
    queryKeyArr: ['get-by-id-commission-period', id, filterParams].filter(Boolean),
    moreParams: { id, ...(filterParams || {}) },
    withFilter: false,
    enabled: !!id && !!getById,
  });
  const dataSource = data?.data?.data;
  const isCreate = dataSource?.createdBy && account?.username === dataSource.createdBy?.userName;

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: putCommissionPeriod,
    keyOfDetailQuery: ['get-by-id-commission-period', id],
    isMessageError: false,
  });

  const updateCommissionStatus = useUpdateField({
    apiQuery: updateStatusCommission,
    keyOfDetailQuery: ['get-by-id-commission-period', id],
    isMessageSuccess: false,
    isMessageError: false,
  });

  useEffect(() => {
    const initialValue = {
      ...dataSource,
      periodObj: {
        periodFrom: dataSource?.periodFrom,
        periodTo: dataSource?.periodTo,
        periodName: dataSource?.name,
      },
      transactions: dataSource?.transactions?.map(item => ({
        ...item,
        employees: item?.employees?.[0],
        children: item?.employees?.[0]?.managers?.map(item => ({
          employees: { ...item, code: item?.code, name: item?.name },
        })),
      })),
    };
    form.setFieldsValue(initialValue);

    setInitialValue(initialValue as TCommission);
  }, [dataSource, form]);

  const disabled = useMemo(() => {
    return !!(pos && period && year);
  }, [pos, period, year]);

  const handleSubmit = async () => {
    await form.validateFields();
    const allValues = form.getFieldsValue(true);
    const payload = formatDataSubmit(allValues);

    const res = await mutateAsync({ ...payload, id });
    if (res?.data?.statusCode === '0') {
      setIsModified(false);
    }
  };

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  const handleChangeTab = (key: string) => {
    setTab(key as 'original' | 'adjustment');
    setSearchParams({ ...params, tabData: key });
  };

  const handleStatusUpdate = async ({ status, successMessage, cancelPublishReason }: TUpdateStatusProps) => {
    const idVersion = tab === 'original' ? dataSource?.adjustmentVersions[0]?.id : selectedAdjustment?.id;

    const res = await updateCommissionStatus.mutateAsync({
      id,
      status,
      adjustmentVersionId: idVersion,
      cancelPublishReason,
    });

    if (res?.data?.statusCode === '0') {
      notification.success({
        message: successMessage,
      });
      setSelectedAdjustment(null);
    }
  };

  const handleCharge = useCallback(async () => {
    const allValues = form.getFieldsValue(true);

    await form.validateFields();
    const payload = formatDataSubmit(allValues);
    const res = await calculateCommissionPeriod(payload);
    setIsModified(true);
    setDataRoot(JSON.stringify(res?.data?.data?.data) === '{}' ? [] : res?.data?.data?.data);
  }, [form]);

  const expandedRowKeys = dataSource?.transactions?.map(item => item?.id);

  const RadioColumns: ColumnsType<TAdjustmentVersion> = [
    {
      title: '',
      dataIndex: 'radio',
      key: 'radio',
      align: 'center',
      width: 48,
      render: (_, record) => (
        <Radio
          disabled={!isCreate && record.status !== STATUS_ADJUSTMENT_VERSION.WAITING}
          checked={selectedAdjustment?.id === record.id}
          onClick={() => {
            if (selectedAdjustment?.id === record.id) {
              setSelectedAdjustment(null);
            } else {
              setSelectedAdjustment(record);
            }
          }}
        />
      ),
    },
    ...versionColumns,
  ];

  const items = [
    {
      label: 'Dữ liệu gốc',
      key: 'original',
      disabled: !isCreate,
      children: (
        <Table
          className="table-root-list"
          columns={transactionColumns as unknown as ColumnsType}
          dataSource={dataRoot ? dataRoot || [] : initialValue?.transactions}
          loading={isLoadingRoot}
          rowClassName={record => (record.children ? 'parent-row' : '')}
          rowKey={'id'}
          pagination={false}
          footer={() => <></>}
          scroll={{ x: 'max-content' }}
          expandable={{
            expandIcon: () => null,
            expandedRowKeys, // Luôn mở rộng các dòng cha
          }}
        />
      ),
    },

    {
      label: 'Dữ liệu điều chỉnh',
      key: 'adjustment',
      children: (
        <Table
          className="table-adjustment-list"
          pagination={false}
          footer={() => <></>}
          rowKey={'id'}
          columns={RadioColumns}
          dataSource={initialValue?.adjustmentVersions}
        />
      ),
    },
  ];

  const handleSelectSalePos = (value: Units) => {
    form.setFieldsValue({
      pos: value
        ? {
            id: value?.id,
            name: value?.name,
            code: value?.code,
          }
        : undefined,
      year: new Date().getFullYear().toString(),
      period: undefined,
    });
    handleClearFormFields();
    setIsModified(true);
  };

  const handleSelectSalePolicy = (value: TSalePolicy) => {
    form.setFieldsValue({ salePolicy: value });
    setIsModified(true);
  };

  const handleSelectCommissionPolicyPosManager = (value: ICommissionPolicy) => {
    form.setFieldsValue({ commissionPolicyManager: value });
    setIsModified(true);
  };

  const handleSelectCommissionPolicyPosPersonal = (value: ICommissionPolicy) => {
    form.setFieldsValue({ commissionPolicyPersonal: value });
    setIsModified(true);
  };

  const handleSelectCommissionIndicatorPos = (value: ICommissionPolicy) => {
    form.setFieldsValue({ indicator: value });
    setIsModified(true);
  };

  const validateForm = () => {
    setIsModified(true);
  };
  const handleClearFormFields = () => {
    form.setFieldsValue({
      salePolicy: undefined,
      indicator: undefined,
      commissionPolicyPersonal: undefined,
      commissionPolicyManager: undefined,
    });
    setIsModified(true);
  };

  useBeforeUnload(event => {
    if (isModified) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <>
      <Spin spinning={isLoadingRoot}>
        <BreadCrumbComponent titleBread={dataSource?.code} />
        <Form
          form={form}
          layout="vertical"
          onValuesChange={validateForm}
          onFinish={handleSubmit}
          initialValues={initialValue}
          className="wrapper-detail-commission"
        >
          <Title level={5}>Thông tin chi tiết</Title>
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col sm={12} xs={24}>
              <Row gutter={24}>
                <Col sm={12} xs={24}>
                  <Form.Item label="Mã đợt tính hoa hồng" required name="code">
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col sm={12} xs={24}>
                  <Form.Item label="Loại" required>
                    <Input disabled value={'Tính hoa hồng'} />
                  </Form.Item>
                </Col>

                <Col span={24}>
                  <Form.Item
                    label="Đơn vị"
                    name="pos"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getOrgChartDropdown}
                      queryKey={['orgChart-exchanges']}
                      keysLabel={'name'}
                      placeholder="Chọn đơn vị"
                      handleSelect={handleSelectSalePos}
                      defaultValues={{ value: pos?.code, label: pos?.name }}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <FormPeriod
                    required
                    label="Kỳ tính hoa hồng"
                    fieldPos="pos"
                    clearFormValueDependency={handleClearFormFields}
                  />
                </Col>

                <Col sm={12} xs={24}>
                  <Form.Item
                    label="Chính sách phí - hoa hồng"
                    required
                    name="salePolicy"
                    rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getSalesPolicyOfCommissionPeriod}
                      queryKey={['sales-policy']}
                      keysLabel={['code', 'name']}
                      placeholder="Chọn chính sách phí - hoa hồng"
                      handleSelect={handleSelectSalePolicy}
                      disabled={!disabled}
                      enabled={disabled && !!pos?.name}
                      moreParams={{ name: pos?.name, isActive: 1 }}
                      defaultValues={{
                        value: salePolicy?.id,
                        label: salePolicy?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Chỉ tiêu"
                    name="indicator"
                    rules={[{ required: true, message: 'Vui lòng chọn chỉ tiêu' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getAllOfCommissionIndicatorPos}
                      queryKey={['indicator']}
                      keysLabel={'code'}
                      placeholder="Chọn chỉ tiêu"
                      handleSelect={handleSelectCommissionIndicatorPos}
                      disabled={!disabled}
                      enabled={disabled && !!pos?.name && !!period && !!year}
                      moreParams={{
                        name: pos?.name,
                        periodFrom: periodObj?.periodFrom,
                        periodTo: periodObj?.periodTo,
                        year: year,
                        isActive: 1,
                      }}
                      defaultValues={{
                        value: indicator?.id,
                        label: indicator?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Bộ chỉ tiêu KPI - Cá nhân"
                    name="commissionPolicyPersonal"
                    rules={[{ required: true, message: 'Vui lòng chọn bộ chỉ tiêu KPI - Cá nhân' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getAllOfCommissionPolicyPos}
                      queryKey={['commission-policy-pos']}
                      keysLabel={['code', 'name']}
                      placeholder="Chọn bộ chỉ tiêu KPI - cá nhân"
                      moreParams={{ type: 'Cá nhân', name: pos?.name, isActive: 1 }}
                      handleSelect={handleSelectCommissionPolicyPosPersonal}
                      disabled={!disabled}
                      enabled={disabled && !!pos?.name}
                      defaultValues={{
                        value: commissionPolicyPersonal?.id,
                        label: commissionPolicyPersonal?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    label="Bộ chỉ tiêu KPI - Quản lý"
                    name="commissionPolicyManager"
                    rules={[{ required: true, message: 'Vui lòng chọn bộ chỉ tiêu KPI - Quản lý' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getAllOfCommissionPolicyPos}
                      queryKey={['commission-policy-pos', 'manager']}
                      keysLabel={['code', 'name']}
                      moreParams={{ type: 'Quản lý', name: pos?.name, isActive: 1 }}
                      placeholder="Chọn bộ chỉ tiêu KPI - Quản lý"
                      handleSelect={handleSelectCommissionPolicyPosManager}
                      disabled={!disabled}
                      enabled={disabled && !!pos?.name}
                      defaultValues={{
                        value: commissionPolicyManager?.id,
                        label: commissionPolicyManager?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                {update && (
                  <Col span={24}>
                    <Form.Item>
                      <Button
                        type="primary"
                        onClick={() => handleCharge()}
                        disabled={
                          !pos ||
                          !period ||
                          !year ||
                          !salePolicy ||
                          !indicator ||
                          !commissionPolicyPersonal ||
                          !commissionPolicyManager
                        }
                      >
                        Tính hoa hồng
                      </Button>
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Col>
            <Col sm={12} xs={24}>
              <Row gutter={24}>
                <Col lg={6} xs={8}>
                  <Text disabled>Ngày cập nhật: </Text>
                </Col>
                <Col lg={18} xs={16}>
                  <Text disabled>
                    {dayjs(dataSource?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                    {`${dataSource?.createdBy?.userName} - ${dataSource?.createdBy?.fullName}`}
                  </Text>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col lg={6} xs={8}>
                  <Text disabled>Ngày tạo: </Text>
                </Col>
                <Col lg={18} xs={16}>
                  <Text disabled>
                    {dayjs(dataSource?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                    {`${dataSource?.modifiedBy?.userName} - ${dataSource?.modifiedBy?.fullName}`}
                  </Text>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
        <Title level={5}>Danh sách giao dịch</Title>
        <Flex justify={isCreate ? 'space-between' : 'flex-end'}>
          {!!isCreate && <FilterTransaction setFilterParams={setFilterParams} />}
          <GroupButtonAction
            modal={modal}
            notification={notification}
            versionAdjustment={selectedAdjustment}
            tab={tab}
            handleStatusUpdate={handleStatusUpdate}
            setSelectedAdjustment={setSelectedAdjustment}
            loadingButton={updateCommissionStatus.isPending}
          />
        </Flex>
        <Tabs className="tabs-transaction" type="card" activeKey={tab} onChange={handleChangeTab} items={items} />
        {isModified && update && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={isPending}
          />
        )}
      </Spin>
    </>
  );
};

export default DetailCommissionPeriod;

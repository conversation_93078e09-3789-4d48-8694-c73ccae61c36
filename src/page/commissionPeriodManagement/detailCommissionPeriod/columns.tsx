import { Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { FORMAT_DATE_TIME, OPTIONS_STATUS_ADJUSTMENT_VERSION } from '../../../constants/common';
import { CommissionTransaction, TAdjustmentVersion } from '../../../types/commission';
import { formatCurrency } from '../../../utilities/shareFunc';
import dayjs from 'dayjs';

export const transactionColumns: ColumnsType<CommissionTransaction> = [
  {
    title: 'Mã giao dịch',
    dataIndex: 'code',
    key: 'code',
  },
  { title: 'Dự án', dataIndex: ['project', 'name'], key: 'projectName' },
  { title: 'Mã BĐS', dataIndex: ['propertyUnit', 'view1'], key: 'propertyUnitVIew1' },
  { title: 'Đợt', dataIndex: 'transactionPhase', key: 'transactionPhase' },
  { title: 'Tình trạng', dataIndex: 'transactionState', key: 'transactionState' },
  {
    title: 'Tên khách hàng',
    dataIndex: ['customer', 'personalInfo', 'name'],
    key: 'nameCustomer',
  },
  {
    title: 'Giá trị',
    dataIndex: ['propertyUnit', 'price'],
    key: 'propertyUnitPrice',
    render: (value: number) => formatCurrency(String(value)),
  },
  {
    title: 'Tỷ lệ doanh thu',
    dataIndex: ['revenue', 'rate'],
    key: 'revenueRate',
    render: (value: number) => (value ? `${(value * 100).toFixed(1)}%` : ''),
  },
  {
    title: 'Tỷ lệ chia',
    dataIndex: ['employees', 'revenueRate'],
    key: 'employeesRevenueRate',
    render: (value: number) => (value ? `${value * 100}%` : ''),
  },
  { title: 'Sàn/ TTGD', dataIndex: ['pos', 'name'], key: 'pos' },
  {
    title: 'Doanh thu',
    dataIndex: 'commissionRevenue',
    key: 'commissionRevenue',
    render: (value: number) => {
      return formatCurrency(String(value));
    },
  },
  {
    title: 'Doanh thu theo đợt',
    dataIndex: 'commissionReceived',
    key: 'commissionReceived',
    render: (value: number) => formatCurrency(String(value)),
  },
  {
    title: 'Vị trí',
    dataIndex: ['employees', 'role'],
    key: 'employeeRole',
  },
  { title: 'Mã nhân viên', dataIndex: ['employees', 'code'], key: 'employeeCode' },
  { title: 'Tên nhân viên', dataIndex: ['employees', 'name'], key: 'employeeName' },
  {
    title: 'Tỷ lệ hoa hồng',
    dataIndex: ['employees', 'commissions', 0, 'rate'],
    key: ' employeesCommissionsRate',
  },
  {
    title: 'Tiền lương năng suất',
    dataIndex: ['employees', 'commissions', 0, 'revenueReceived'],
    key: 'employeesCommissionsRevenue',
    render: (value: number) => formatCurrency(String(value)),
  },
  {
    title: 'TN cố định/ thưởng nóng/ thưởng thêm',
    dataIndex: ['employees', 'commissions', 0, 'bonus'],
    key: 'employeesCommissionsRate',
  },
  {
    title: 'Tổng thu nhập phải trả',
    dataIndex: ['employees', 'commissions', 0, 'totalExpectedIncome'],
    key: 'totalExpectedIncome',
    render: (value: number) => formatCurrency(String(value)),
  },
];

export const versionColumns: ColumnsType<TAdjustmentVersion> = [
  {
    title: 'Phiên bản',
    dataIndex: 'version',
    key: 'version',
  },
  {
    title: 'Ngày tải lên',
    dataIndex: 'uploadDate',
    key: 'uploadDate',
    render: (text, record) => (
      <div>
        {text ? dayjs(text).format(FORMAT_DATE_TIME) : '-'}
        <br />
        {record?.uploadBy?.fullName ? record?.uploadBy?.fullName : '-'}
      </div>
    ),
  },
  {
    title: 'Log file',
    dataIndex: 'fileName',
    key: 'fileName',
    render: (text, record: TAdjustmentVersion) => (
      <Typography.Link href={record?.fileUrl} target="_blank" download>
        {text}
      </Typography.Link>
    ),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    render: value => {
      const status = OPTIONS_STATUS_ADJUSTMENT_VERSION.find(item => item.value === value);
      return status ? <Typography.Text style={{ color: status?.color }}>{status?.name}</Typography.Text> : 'null';
    },
  },
];

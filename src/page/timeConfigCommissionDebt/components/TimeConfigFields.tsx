import { Col, Form, Input, Row, Select, Switch } from 'antd';
import { useMemo } from 'react';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions } from '../../../hooks';
import { TListDropdown } from '../../../types/salesPolicy';
import { useStoreTimeConfigDebtCommission } from '../storeTimeConfigDebt';
import { sendGetListOfDropdownProjects } from '../../../service/salesPolicy';

interface ITimeConfigFields {
  action: 'create' | 'modify';
}
const TimeConfigFields = ({ action }: ITimeConfigFields) => {
  const form = Form.useFormInstance();

  const { tab, initialValue, setIsModified } = useStoreTimeConfigDebtCommission();
  const { periodChangeStatus } = useCheckPermissions(PERMISSION_COMMISSION);
  const isProjectTab = tab === 'project';
  const days = Array.from({ length: 31 }, (_, i) => i + 1);

  const formatProjectsSelected = useMemo(
    () =>
      action === 'modify'
        ? initialValue?.projects?.map(item => ({
            ...item,
            label: item?.name as string,
            value: item?.id as string,
          }))
        : undefined,
    [action, initialValue?.projects],
  );

  const handleStartDayChange = (value: number) => {
    let endDay = value - 1;
    if (value === 1) {
      endDay = 31;
    }
    form.setFieldsValue({ periodEndDate: value ? `Ngày ${endDay}` : undefined });
  };

  const handleSelectListProject = (values: TListDropdown[]) => {
    action === 'modify' && setIsModified(true);
    form.setFieldsValue({ projects: values.map(item => ({ id: item?.id, name: item.name, code: item.code })) });
  };

  return (
    <Col lg={12} xs={24}>
      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Tên cấu hình"
            name="name"
            rules={[{ required: true, message: 'Vui lòng nhập tên cấu hình', whitespace: true }]}
          >
            <Input placeholder="Nhập tên cấu hình" maxLength={255} />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Ngày đầu kỳ"
            name="periodStartDate"
            rules={[{ required: true, message: 'Vui lòng chọn ngày đầu kỳ' }]}
          >
            <Select
              placeholder="Chọn ngày đầu kỳ"
              allowClear
              options={days.map(day => ({ label: `Ngày ${day}`, value: day }))}
              onChange={handleStartDayChange}
            />
          </Form.Item>
        </Col>
        <Col span={12}>
          <Form.Item
            label="Ngày cuối kỳ"
            name="periodEndDate"
            getValueProps={value => ({
              value: value ? `Ngày ${value}` : null,
            })}
          >
            <Input placeholder="Tự động hiển thị" disabled />
          </Form.Item>
        </Col>
        {isProjectTab && (
          <Col span={24}>
            <Form.Item label="Dự án" name="projects" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
              <MultiSelectLazy
                apiQuery={sendGetListOfDropdownProjects}
                enabled={isProjectTab}
                queryKey={['projects-dropdown']}
                keysLabel={['code', 'name']}
                placeholder="Chọn dự án"
                keysTag={'name'}
                handleListSelect={handleSelectListProject}
                defaultValues={formatProjectsSelected}
              />
            </Form.Item>
          </Col>
        )}
        <Col span={12}>
          <Form.Item
            label="Trạng thái"
            layout="horizontal"
            labelCol={{ span: 12 }}
            labelAlign="left"
            name="isActive"
            valuePropName="checked"
            getValueFromEvent={checked => (checked ? 1 : 2)}
            getValueProps={value => ({
              checked: value === 1,
            })}
          >
            <Switch disabled={!periodChangeStatus} />
          </Form.Item>
        </Col>

        <Col span={12}>
          <Form.Item shouldUpdate>
            {({ getFieldValue }) => {
              const status = getFieldValue('isActive');
              return (
                <span style={{ color: status === 1 ? '#389E0D' : '#CF1322' }}>
                  {status === 1 ? 'Đã kích hoạt' : 'Vô hiệu hóa'}
                </span>
              );
            }}
          </Form.Item>
        </Col>
      </Row>
    </Col>
  );
};

export default TimeConfigFields;

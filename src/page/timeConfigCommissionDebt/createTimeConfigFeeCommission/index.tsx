import { Button, Form } from 'antd';
import { useBeforeUnload } from 'react-router-dom';
import ModalComponent from '../../../components/modal';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import { DEBT_COMMISSION_PERIOD } from '../../../configs/path';
import { useCreateField } from '../../../hooks';
import { createTimeConfigDebtCommission } from '../../../service/timeConfigCommissionDebt';
import {
  TListOfTimeConfigDebtCommission,
  TSubmitTimeConfigDebtCommission,
} from '../../../types/timeConfigCommisionDebt';
import TimeConfigFields from '../components/TimeConfigFields';
import { useStoreTimeConfigDebtCommission } from '../storeTimeConfigDebt';

interface Props {
  handleCancel: () => void;
  open: boolean;
}

const CreateTimeConfigDebtCommission = (props: Props) => {
  const { handleCancel, open } = props;
  const [form] = Form.useForm();
  const { tab, setInitialValue } = useStoreTimeConfigDebtCommission();

  const { mutateAsync, isPending } = useCreateField({
    keyOfListQuery: ['list-of-time-config-Debt-commission'],
    apiQuery: createTimeConfigDebtCommission,
    messageSuccess: 'Tạo mới cấu hình thành công',
    isMessageError: false,
  });

  const handleSubmit = async (values: TSubmitTimeConfigDebtCommission) => {
    const newData = {
      isActive: values.isActive,
      periodStartDate: values.periodStartDate,
      name: values.name,
      projects: values?.projects,
    };
    const res = await mutateAsync(newData);
    const data = res?.data?.data as TListOfTimeConfigDebtCommission;
    if (res?.data?.statusCode === '0') {
      handleCancel();
      window.open(`${DEBT_COMMISSION_PERIOD}/${data?.id}?tab=${tab}`, '_blank', 'noopener,noreferrer');
      setInitialValue(data);
    }
  };

  useBeforeUnload(event => {
    if (open && form.isFieldsTouched()) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <ModalComponent
      className="wrapper-modal-create-time-config-commission"
      title={`Tạo mới cấu hình`}
      open={open}
      footer={[
        <Button type="primary" loading={isPending} onClick={() => form.submit()} key="submit">
          Tạo mới
        </Button>,
      ]}
      onCancel={() => {
        if (!form.isFieldsTouched()) {
          handleCancel();
          return;
        }
        showConfirmCancelModal({
          onConfirm: handleCancel,
        });
      }}
      destroyOnClose
      afterClose={() => form.resetFields()}
      maskClosable={false}
    >
      <Form
        form={form}
        onFinish={handleSubmit}
        initialValues={{
          isActive: 1,
        }}
        layout="vertical"
      >
        <TimeConfigFields action="create" />
      </Form>
    </ModalComponent>
  );
};

export default CreateTimeConfigDebtCommission;

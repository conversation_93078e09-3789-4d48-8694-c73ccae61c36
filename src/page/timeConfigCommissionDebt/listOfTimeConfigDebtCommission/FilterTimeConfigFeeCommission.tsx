import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { useState } from 'react';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import { DEFAULT_PARAMS, FORMAT_DATE, FORMAT_DATE_API, OPTIONS_STATUS_PROJECT } from '../../../constants/common';
import useFilter from '../../../hooks/filter';
import { getListEmployeeAll } from '../../../service/customers';
import { sendGetListOfDropdownProjects } from '../../../service/salesPolicy';
import { IResponseObjMultiSelect } from '../../../types/common/common';
import { TListDropdown } from '../../../types/salesPolicy';
import { TFilterTimeConfigDebtCommission } from '../../../types/timeConfigCommisionDebt';
import { useStoreTimeConfigDebtCommission } from '../storeTimeConfigDebt';

const FilterTimeConfigDebtCommission = () => {
  const [form] = Form.useForm();
  const [, setFilter] = useFilter();
  const [initialValues, setInitialValues] = useState<TFilterTimeConfigDebtCommission>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const { setTabFilter, tab, getCurrentFilter } = useStoreTimeConfigDebtCommission();

  const handleSubmitFilter = (values: TFilterTimeConfigDebtCommission) => {
    const newFilter = {
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : undefined,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : undefined,
      isActive: values?.isActive ? String(values?.isActive) : undefined,
      projects: values?.projects ? String(values.projects) : undefined,
      createdBy: values?.createdBy ? String(values.createdBy) : undefined,
      tab: tab,
    };
    setTabFilter(tab, { ...getCurrentFilter(), ...newFilter });
    setFilter({ ...DEFAULT_PARAMS, tab: tab }); // Reset filter to default params with current tab
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setTabFilter(tab, { tab: tab, search: getCurrentFilter()?.search });
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleSelectEmployee = (values: IResponseObjMultiSelect[]) => {
    form.setFieldsValue({ createdBy: values.map(item => item?.option?.accountId).join(',') });
  };

  const handleSearch = (value: string) => {
    const search = value ? value : '';
    setFilter({ ...DEFAULT_PARAMS, tab: tab });
    setTabFilter(tab, { ...getCurrentFilter(), search });
  };

  const handleSelectListProject = (values: TListDropdown[]) => {
    form.setFieldsValue({
      projects: values?.map(value => value?.id).join(','),
    });
  };
  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearch}
        extraFormItems={
          <>
            {tab === 'project' && (
              <Form.Item label="Dự án" name="projects">
                <MultiSelectLazy
                  apiQuery={sendGetListOfDropdownProjects}
                  enabled={isOpenFilter}
                  queryKey={['projects-dropdown']}
                  keysLabel={'name'}
                  placeholder="Chọn dự án"
                  handleListSelect={handleSelectListProject}
                  keysTag={'name'}
                />
              </Form.Item>
            )}
            <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                apiQuery={getListEmployeeAll}
                queryKey={['employee-dropdown']}
                keysLabel={['username', 'name']}
                handleListSelect={handleSelectEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'username'}
              />
            </Form.Item>
            <Form.Item label="Trạng thái" name="isActive">
              <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_STATUS_PROJECT} />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endDate = form.getFieldValue('endDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endDate && current > dayjs(endDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startDate = form.getFieldValue('startDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startDate && current < dayjs(startDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterTimeConfigDebtCommission;

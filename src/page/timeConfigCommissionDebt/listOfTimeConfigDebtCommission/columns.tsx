import { ColumnsType } from 'antd/es/table';

import { Tooltip, Typography } from 'antd';
import dayjs from 'dayjs';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import {
  TListOfTimeConfigDebtCommission,
  TProjectTimeConfigDebtCommission,
} from '../../../types/timeConfigCommisionDebt';

const { Text } = Typography;

export const columnsTimeConfigDebtCommissionDefault: ColumnsType<TListOfTimeConfigDebtCommission> = [
  {
    title: 'Tên cấu hình',
    dataIndex: 'name',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Ngày đầu kỳ',
    dataIndex: 'periodStartDate',
    key: 'periodStartDate',
    render: (value: string) => (value ? `Ngày ${value}` : '-'),
  },
  {
    title: 'Ngày cuối kỳ',
    dataIndex: 'periodEndDate',
    key: 'periodEndDate',
    render: (value: string) => (value ? `Ngày ${value}` : '-'),
  },
  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    key: 'isActive',
    render: (value: number) =>
      value === 1 ? (
        <Text style={{ color: '#389E0D' }}>Đã kích hoạt</Text>
      ) : (
        <Text style={{ color: '#CF1322' }}>Vô hiệu hoá</Text>
      ),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    render: (value: string, record: TListOfTimeConfigDebtCommission) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
        <br />
        <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'} </Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    render: (value: string, record: TListOfTimeConfigDebtCommission) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
        <br />
        <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
      </>
    ),
  },
];

export const columnsTimeConfigDebtCommissionProject: ColumnsType<TListOfTimeConfigDebtCommission> = [
  {
    title: 'Tên cấu hình',
    dataIndex: 'name',
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Ngày đầu kỳ',
    dataIndex: 'periodStartDate',
    key: 'periodStartDate',
    render: (value: string) => (value ? `Ngày ${value}` : '-'),
  },
  {
    title: 'Ngày cuối kỳ',
    dataIndex: 'periodEndDate',
    key: 'periodEndDate',
    render: (value: string) => (value ? `Ngày ${value}` : '-'),
  },
  {
    title: 'Dự án',
    dataIndex: 'projects',
    key: 'projects',
    width: ' 255px',
    render: (value: TProjectTimeConfigDebtCommission[]) => {
      if (!value || value.length === 0) return '-';

      const tooltipTitle = value.map(item => item?.name).join(', ');
      const displayText =
        value.length <= 3
          ? tooltipTitle
          : `${value
              .slice(0, 3)
              .map(item => item?.name)
              .join(', ')},...`;

      return <Tooltip title={tooltipTitle}>{displayText}</Tooltip>;
    },
  },
  {
    title: 'Trạng thái',
    dataIndex: 'isActive',
    key: 'isActive',
    render: (value: number) =>
      value === 1 ? (
        <Text style={{ color: '#389E0D' }}>Đã kích hoạt</Text>
      ) : (
        <Text style={{ color: '#CF1322' }}>Vô hiệu hoá</Text>
      ),
  },
  {
    title: 'Ngày tạo',
    dataIndex: 'createdDate',
    key: 'createdDate',
    render: (value: string, record: TListOfTimeConfigDebtCommission) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
        <br />
        <Text>{record?.createdBy?.fullName ? record.createdBy.fullName : '-'} </Text>
      </>
    ),
  },
  {
    title: 'Ngày cập nhật',
    dataIndex: 'modifiedDate',
    key: 'modifiedDate',
    render: (value: string, record: TListOfTimeConfigDebtCommission) => (
      <>
        <Text>{value ? dayjs(value).format(FORMAT_DATE_TIME) : '-'}</Text>
        <br />
        <Text>{record?.modifiedBy?.fullName ? record.modifiedBy.fullName : '-'}</Text>
      </>
    ),
  },
];

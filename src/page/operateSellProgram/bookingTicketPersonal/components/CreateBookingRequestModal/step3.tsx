import React, { useState, useEffect, useCallback, useMemo } from 'react';
import { Form, Input, Select, DatePicker, Checkbox, Button, Row, Col, Radio, UploadFile, Typography } from 'antd';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { useCreateField, useFetch } from '../../../../../hooks';
import {
  Bank,
  BankOption,
  CreateBookingTicket,
  CreateDemandBusiness,
  CreateDemandIndividual,
  DuplicateResp,
  PropertyUnit,
  SaleProgram,
  UpdateCustomer,
  UpdateDemand,
} from '../../../../../types/bookingRequest';
import { getBanks } from '../../../../../service/bank';
import ModalComponent from '../../../../../components/modal';
import SelectAddress, { AddressType } from '../../../../../components/selectAddress';
import dayjs from 'dayjs';
import { FORMAT_DATE, OPTIONS_GENDER, REGEX_PHONE_VN } from '../../../../../constants/common';
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import { handleKeyDownEnterNumber } from '../../../../../utilities/regex';
import {
  createBookingTicket,
  createDemandBusiness,
  createDemandIndividual,
  getProductUnits,
  getSalePrograms,
  updateCustomer,
  updateDemand,
} from '../../../../../service/bookingTicket';
import { useParams } from 'react-router-dom';
import UploadFileBookingTicket from '../UploadFileBookingTicket';
import { getProvinces } from '../../../../../service/address';
import { hasNonNullValue } from '../../../../../utilities/shareFunc';
import { BankInfo } from '../../../../../types/offer';
import { getDetailProject } from '../../../../../service/project';
import { DetailProject } from '../../../../../types/project/project';

const { Title } = Typography;
const { Option } = Select;
const { TextArea } = Input;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

interface CreateBookingRequestStep3Props {
  visible: boolean;
  onCancel: (hasFormChanged?: boolean, resetForm?: () => void) => void;
  onSave: (values: Record<string, any>) => void;
  initialData?: DuplicateResp;
  duplicateData?: DuplicateResp | null;
  step1Data?: DuplicateResp;
}

const CreateBookingRequestStep3: React.FC<CreateBookingRequestStep3Props> = ({
  visible,
  onCancel,
  onSave,
  initialData,
  duplicateData,
}) => {
  const { id: projectId } = useParams<{ id: string }>();
  const [form] = Form.useForm();
  const { data: detailProjects } = useFetch<DetailProject>({
    queryKeyArr: ['detail-project', projectId],
    api: () => getDetailProject(projectId),
    withFilter: false,
    enabled: !!projectId,
    cacheTime: 10,
  });
  const initialProject = detailProjects?.data?.data;
  const amountTemplateFiles = initialProject?.setting?.templateFiles;

  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const issueDate = Form.useWatch('issueDate', form);
  const issueLocation = Form.useWatch('issueLocation', form);
  const address = Form.useWatch(['address'], form);
  const isAddressNonNull = hasNonNullValue(address);
  const companyAddress = Form.useWatch('companyAddress', form);
  const isCompanyAdressNonNull = hasNonNullValue(companyAddress);
  const rootAddress = Form.useWatch(['rootAddress'], form);
  const isRootAddressNonNull = hasNonNullValue(rootAddress);

  const type = Form.useWatch(['type'], form);
  const bankInfoOptions = Form.useWatch('bankAccount', form) || [];

  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [propertyUnitParams, setPropertyUnitParams] = useState({
    salesProgramIds: '',
    projectId: '',
  });
  const [yearOnly, setYearOnly] = useState(false);
  const [isModified, setIsModified] = useState(false);
  const [productSelectKey, setProductSelectKey] = useState(0);
  // const [bankAccountsCount, setBankAccountsCount] = useState(1);
  const isKHCT = duplicateData?.customerType === 'customer';

  const defaultBankOptions: BankOption[] = useMemo(
    () =>
      bankInfoOptions
        .filter((bank: Bank) => bank?.bankCode && bank?.bankName)
        .map((bank: Bank, index: number) => ({
          value: `${bank.bankCode}-${index}`,
          label: `${bank?.bankName || 'N/A'} - ${bank?.accountNumber || ''} - ${bank?.beneciary || ''}`,
          originalBankCode: bank.bankCode,
        })),
    [bankInfoOptions],
  );

  const { data: dataProvinces } = useFetch<AddressType[]>({
    queryKeyArrWithFilter: ['get-list-identity'],
    api: getProvinces,
  });
  const provinces = dataProvinces?.data?.data;

  const { data: dataBanks, isLoading: isLoadingBanks } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
  });
  const banks = dataBanks?.data?.data || [];

  const { mutateAsync: _createBookingTicket } = useCreateField<CreateBookingTicket>({
    apiQuery: createBookingTicket,
    keyOfListQuery: ['get-booking-ticket'],
    isMessageError: false,
  });

  const { mutateAsync: _createDemandIndividual } = useCreateField<CreateDemandIndividual>({
    apiQuery: createDemandIndividual,
    keyOfListQuery: ['get-create-demand-individual'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const { mutateAsync: _createDemandBusiness } = useCreateField<CreateDemandBusiness>({
    apiQuery: createDemandBusiness,
    keyOfListQuery: ['get-create-demand-business'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const { mutateAsync: _updateCustomer } = useCreateField<UpdateCustomer>({
    apiQuery: updateCustomer,
    keyOfListQuery: ['get-update-customer'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const { mutateAsync: _updateDemand } = useCreateField<UpdateDemand>({
    apiQuery: updateDemand,
    keyOfListQuery: ['get-update-demand'],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const handleSave = async () => {
    try {
      const identitiesOld = duplicateData?.data?.[0]?.personalInfo?.identities;
      const customerId = duplicateData?.data?.[0]?.id;
      const leadCode = duplicateData?.data?.[0]?.id;
      const personalInfoOld = duplicateData?.data?.[0]?.personalInfo;
      const infoOld = duplicateData?.data?.[0]?.info;
      const companyOld = duplicateData?.data?.[0]?.company;

      const values = await form.validateFields();
      const { mainBank, salesProgramId, demandPropertyId } = values;

      const payloadCreateTicket: CreateBookingTicket = {
        ticketType: 'YCDCH',
        employeeTakeCareId: values?.employeeTakeCareId,
        projectId: projectId,
        salesProgramId: salesProgramId || '',
        demandPropertyId: demandPropertyId || '',
        customer: {
          useResidentialAddress: values?.info?.cloneAddress,
          code: values?.code,
          gender: values?.info?.gender,
          onlyYear: values?.info?.onlyYear,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly
            ? values?.info?.birthdayYear
              ? dayjs(values?.info?.birthdayYear).format('YYYY')
              : ''
            : '',
          name: values.personalInfo?.name,
          phone: values.personalInfo?.phone,
          email: values.personalInfo?.email,
          identityType: values?.identityType,
          identityNumber: values?.type === 'business' ? values?.taxCode : values?.identityNo,
          identityIssueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
          identityIssueLocation:
            typeof values?.issueLocation === 'object' ? values?.issueLocation?.label : values?.issueLocation,
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
          taxCode: values?.taxCode,
          bankInfo: {
            name: mainBank?.name || '',
            accountNumber: mainBank?.accountNumber || '',
            beneciary: mainBank?.beneciary || '',
          },
          position: values?.position,
          type: values?.type,
          company: { ...values?.company, address: companyAddress },
        },
        files: fileList.map(file => ({
          uid: file.uid || '',
          name: file.name,
          url: file.key || '',
          absoluteUrl: file?.absoluteUrl || '',
          uploadName: file.name,
        })),
        demandCategory: values?.demandCategory,
        note: values?.description,
      };

      const payloadCreateDemandIndividual: CreateDemandIndividual = {
        type: values?.type,
        name: values.personalInfo?.name,
        phone: values.personalInfo?.phone,
        gender: values?.info?.gender,
        leadCode: leadCode,
        continueCreate: true,
        bankInfo:
          values?.bankAccount?.map((bank: BankInfo) => ({
            code: bank?.bankCode || '',
            name: bank?.bankName || '',
            accountNumber: bank?.accountNumber || '',
            beneficiary: bank?.beneciary || '',
            branchCode: '',
          })) || [],
        info: {
          ...values?.info,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly
            ? values?.info?.birthdayYear
              ? dayjs(values?.info?.birthdayYear).format('YYYY')
              : ''
            : '',
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
        },
        personalInfo: {
          ...values?.personalInfo,
          identities: [
            {
              type: values?.identityType,
              value: values?.identityNo,
              date: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
              place: values?.issueLocation?.value,
            },
          ],
        },
      };

      const payloadCreateDemandBusiness: CreateDemandBusiness = {
        type: values?.type,
        name: values.personalInfo?.name,
        phone: values.personalInfo?.phone,
        leadCode: leadCode,
        continueCreate: true,
        taxCode: values?.taxCode,
        companyName: values?.company?.name,
        company: {
          ...values?.company,
          issueDate: values?.issueDate ? dayjs(values?.issueDate).format(FORMAT_DATE) : '',
          issueLocation: values?.issueLocation,
          address: values?.companyAddress,
        },
        bankInfo:
          values?.bankAccount?.map((bank: BankInfo) => ({
            code: bank?.bankCode || '',
            name: bank?.bankName || '',
            accountNumber: bank?.accountNumber || '',
            beneficiary: bank?.beneciary || '',
            branchCode: '',
          })) || [],
        info: {
          ...values?.info,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly
            ? values?.info?.birthdayYear
              ? dayjs(values?.info?.birthdayYear).format('YYYY')
              : ''
            : '',
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
        },
        personalInfo: {
          ...values?.personalInfo,
          identities: identitiesOld,
        },
      };

      const payloadUpdateCustomer: UpdateCustomer = {
        id: customerId,
        company: { ...companyOld, ...values?.company, address: companyAddress, issueLocation: values?.issueLocation },
        personalInfo: {
          ...personalInfoOld,
          ...values?.personalInfo,
          identities: identitiesOld,
        },
        info: {
          ...infoOld,
          ...values?.info,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly
            ? values?.info?.birthdayYear
              ? dayjs(values?.info?.birthdayYear).format('YYYY')
              : ''
            : '',
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
        },
        bankInfo:
          values?.bankAccount?.map((bank: BankInfo) => ({
            code: bank?.bankCode || '',
            name: bank?.bankName || '',
            accountNumber: bank?.accountNumber || '',
            beneficiary: bank?.beneciary || '',
            branchCode: '',
          })) || [],
        takeNote: values?.takeNote,
      };

      const payloadUpdateDemand: UpdateDemand = {
        id: customerId,
        type: values?.type,
        company: { ...companyOld, ...values?.company, address: companyAddress, issueLocation: values?.issueLocation },
        info: {
          ...infoOld,
          ...values?.info,
          birthday: !yearOnly ? (values?.info?.birthday ? dayjs(values?.info?.birthday).format(FORMAT_DATE) : '') : '',
          birthdayYear: yearOnly
            ? values?.info?.birthdayYear
              ? dayjs(values?.info?.birthdayYear).format('YYYY')
              : ''
            : '',
          rootAddress: { ...values.rootAddress },
          address: { ...values?.address },
        },
        bankInfo:
          values?.bankAccount?.map((bank: BankInfo) => ({
            code: bank?.bankCode || '',
            name: bank?.bankName || '',
            accountNumber: bank?.accountNumber || '',
            beneficiary: bank?.beneciary || '',
            branchCode: '',
          })) || [],
        personalInfo: {
          ...personalInfoOld,
          ...values?.personalInfo,
          identities: identitiesOld,
        },
        continueUpdate: true,
      };

      const currentType = duplicateData?.type;

      if (duplicateData?.customerType === 'initial') {
        let resp;
        if (currentType === 'individual') {
          resp = await _createDemandIndividual(payloadCreateDemandIndividual);
        } else if (currentType === 'business') {
          resp = await _createDemandBusiness(payloadCreateDemandBusiness);
        }

        const currentCode = (resp?.data?.data as { code?: string })?.code ?? '';

        if (resp?.data?.statusCode === '0') {
          const updatedPayloadCreateTicket = {
            ...payloadCreateTicket,
            customer: {
              ...payloadCreateTicket.customer,
              code: currentCode || payloadCreateTicket?.customer?.code, // Sử dụng resp?.code nếu có, nếu không giữ nguyên giá trị cũ
            },
          };
          const ticketResp = await _createBookingTicket(updatedPayloadCreateTicket);
          if (ticketResp?.data?.statusCode === '0') {
            onSave(payloadCreateTicket);
          }
        }
      } else if (duplicateData?.customerType === 'demand') {
        const resp = await _updateDemand(payloadUpdateDemand);
        if (resp?.data.statusCode === '0') {
          const resp = await _createBookingTicket(payloadCreateTicket);
          if (resp?.data.statusCode === '0') {
            onSave(payloadCreateTicket);
          }
        }
      } else if (duplicateData?.customerType === 'customer') {
        const resp = await _updateCustomer(payloadUpdateCustomer);
        if (resp?.data.statusCode === '0') {
          const resp = await _createBookingTicket(payloadCreateTicket);
          if (resp?.data.statusCode === '0') {
            onSave(payloadCreateTicket);
          }
        }
      }
    } catch (e) {
      console.log('Validation failed:', e);
    }
  };

  const validateForm = () => {
    setIsModified(true);
  };

  // const handleAddBankAccount = () => {
  //   if (bankAccountsCount >= 10) {
  //     return;
  //   }
  //   setBankAccountsCount(bankAccountsCount + 1);
  // };

  const handleRemoveBankAccount = (name: number) => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    const bankAccount = form.getFieldValue('bankAccount');
    const removedBank = bankAccount[name];

    // Kiểm tra xem tài khoản bị xóa có phải là tài khoản chính không
    const isMainBankRemoved = currentMainBankId === `${removedBank?.bankCode}-${name}`;

    // Xóa tài khoản
    // setBankAccountsCount(bankAccountsCount - 1);

    if (isMainBankRemoved) {
      // Nếu tài khoản chính bị xóa, đặt lại mainBankId và mainBank
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: {
          name: '',
          accountNumber: '',
          beneciary: '',
        },
      });
    } else if (currentMainBankId) {
      // Nếu tài khoản chính không bị xóa, cập nhật lại mainBankId để phản ánh chỉ số mới
      const [bankCode, currentIndex] = currentMainBankId.split('-');
      const currentIndexNum = parseInt(currentIndex);

      // Nếu chỉ số của tài khoản chính lớn hơn chỉ số bị xóa, giảm chỉ số đi 1
      if (currentIndexNum > name) {
        const newMainBankId = `${bankCode}-${currentIndexNum - 1}`;
        form.setFieldsValue({
          mainBankId: newMainBankId,
        });
      }
    }
  };

  const handleSelectBankInfo = (value: string) => {
    const selectedOption = defaultBankOptions.find(option => option.value === value);
    const selectedBank = bankInfoOptions.find((bank: Bank) => bank.bankCode === selectedOption?.originalBankCode);
    if (selectedBank) {
      form.setFieldsValue({
        mainBankId: value,
        mainBank: {
          name: selectedBank.bankName || '',
          accountNumber: selectedBank.accountNumber || '',
          beneciary: selectedBank.beneciary || '',
        },
      });
    } else {
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: {
          name: '',
          accountNumber: '',
          beneciary: '',
        },
      });
    }
  };

  const handleCloneAddress = useCallback(
    (e: CheckboxChangeEvent) => {
      if (e.target.checked) {
        form.setFieldsValue({
          rootAddress: form.getFieldValue('address'),
        });
      }
    },
    [form],
  );

  const handleSelectSaleProgram = (value: SaleProgram) => {
    if (value) {
      // Khi chọn chương trình bán hàng
      const newParams = {
        salesProgramIds: value?.id || '',
        projectId: value?.project?.id || '',
      };

      setPropertyUnitParams(newParams);
      setProductSelectKey(prev => prev + 1); // Force re-render component mã sản phẩm
      form.setFieldsValue({
        salesProgramId: value?.id,
        demandPropertyId: undefined, // Reset mã sản phẩm khi chọn chương trình mới
      });
      form.validateFields(['salesProgramId']);
    } else {
      // Khi clear/xóa chương trình bán hàng
      setPropertyUnitParams({
        salesProgramIds: '',
        projectId: '',
      });
      setProductSelectKey(prev => prev + 1); // Force re-render component mã sản phẩm
      form.setFieldsValue({
        salesProgramId: undefined,
        demandPropertyId: undefined, // Reset mã sản phẩm khi clear chương trình
      });
      form.validateFields(['salesProgramId']);
    }
  };

  const handleSelectProductCode = (value: PropertyUnit) => {
    form.setFieldsValue({ demandPropertyId: value?.id || undefined });
    form.validateFields(['demandPropertyId']);
  };

  useEffect(() => {
    if (initialData) {
      form.setFieldsValue(initialData);
      form.setFieldsValue({
        employeeTakeCareId: initialData.employeeTakeCare?.id,
      });
    }

    if (duplicateData?.data?.length) {
      const customerData = duplicateData.data[0];
      const identity = customerData.personalInfo?.identities?.[0];
      const bankInfo = customerData.bankInfo || [];
      const mainBankInfo = bankInfo[0] || {};
      form.setFieldsValue({
        type: customerData.type,
        code: customerData.code,
        identityType: customerData?.type === 'business' ? 'MST' : identity?.type,
        identityNo: identity?.value,
        issueDate:
          customerData.type === 'business'
            ? customerData?.company?.issueDate
              ? dayjs(customerData?.company?.issueDate, FORMAT_DATE)
              : null
            : identity?.date
              ? dayjs(identity?.date, FORMAT_DATE)
              : null,
        issueLocation: customerData.type === 'business' ? customerData?.company?.issueLocation : identity?.place,
        // employeeTakeCareId: customerData.employee?.id,
        address: customerData.info?.address,
        rootAddress: customerData.info?.rootAddress,
        companyAddress: customerData.company?.address,
        company: {
          name: customerData.company?.name,
        },
        personalInfo: {
          name: customerData.personalInfo?.name,
          phone: customerData.personalInfo?.phone,
          email: customerData.personalInfo?.email,
        },
        info: {
          birthday: customerData?.info?.birthday ? dayjs(customerData.info.birthday, FORMAT_DATE) : null,
          birthdayYear: customerData?.info?.birthdayYear ? dayjs(customerData.info.birthdayYear, 'YYYY') : null,
          gender: customerData.info?.gender,
          onlyYear: customerData?.info?.onlyYear,
          cloneAddress: customerData.info?.cloneAddress,
        },
        bankAccount: bankInfo.map((bank: BankInfo) => ({
          bankName: bank.name || '',
          bankCode: bank.code || '',
          accountNumber: bank.accountNumber || '',
          beneciary: bank.beneficiary || '',
        })),
        mainBank: {
          name: mainBankInfo.name || '',
          accountNumber: mainBankInfo.accountNumber || '',
          beneciary: mainBankInfo.beneciary || '',
        },
        description: customerData.description,
      });
      setYearOnly(!!customerData.info?.onlyYear);
      // setBankAccountsCount(bankInfo.length || 1);
    }
  }, [duplicateData, form, initialData]);

  useEffect(() => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    if (currentMainBankId) {
      const [bankCode, index] = currentMainBankId.split('-');
      const bankAccount = form.getFieldValue('bankAccount') || [];
      const selectedBank = bankAccount[parseInt(index)];

      // Chỉ clear mainBankId nếu tài khoản chính không còn tồn tại hoặc bankCode không khớp
      if (!selectedBank || selectedBank.bankCode !== bankCode) {
        form.setFieldsValue({
          mainBankId: undefined,
          mainBank: {
            name: '',
            accountNumber: '',
            beneciary: '',
          },
        });
      }
    }
  }, [bankInfoOptions, form]);

  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  // Đảm bảo mã sản phẩm được reset khi không có chương trình bán hàng
  useEffect(() => {
    if (!propertyUnitParams.salesProgramIds) {
      const currentDemandPropertyId = form.getFieldValue('demandPropertyId');
      if (currentDemandPropertyId) {
        form.setFieldsValue({ demandPropertyId: undefined });
      }
    }
  }, [propertyUnitParams.salesProgramIds, form]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleCancel = () => {
    onCancel(form.isFieldsTouched(), () => form.resetFields());
  };

  return (
    <ModalComponent
      title="Tạo mới yêu cầu đặt chỗ 2/2"
      open={visible}
      onCancel={handleCancel}
      footer={[
        <Button key="save" type="primary" onClick={handleSave}>
          Lưu
        </Button>,
      ]}
    >
      <Form form={form} layout="vertical" onValuesChange={validateForm}>
        <Row gutter={32}>
          <Col span={16}>
            {/* Thông tin khách hàng */}
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Loại khách hàng"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại khách hàng!' }]}
                >
                  <Select placeholder="Chọn loại khách hàng" disabled>
                    <Option value="individual">Cá nhân</Option>
                    <Option value="business">Doanh nghiệp</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <p style={{ marginBottom: 8 }}>
              Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
            </p>
            <Row
              gutter={[8, 8]}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Col span={6}>
                <Form.Item
                  label="Loại giấy tờ"
                  name="identityType"
                  rules={[{ required: true, message: 'Vui lòng chọn loại giấy tờ' }]}
                >
                  <Select placeholder="Chọn loại giấy tờ" disabled>
                    {type === 'individual' ? (
                      <>
                        <Option value="CCCD">Căn cước công dân</Option>
                        <Option value="CMND">CMT</Option>
                        <Option value="Hộ chiếu">Hộ chiếu</Option>
                      </>
                    ) : (
                      <Option value="MST">Mã số thuế</Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Số giấy tờ"
                  name={type === 'business' ? 'taxCode' : 'identityNo'}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số giấy tờ',
                      whitespace: true,
                    },
                  ]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={15} disabled={isKHCT} />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Ngày cấp"
                  name="issueDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày cấp' }]}
                >
                  <DatePicker placeholder="Chọn ngày cấp" format={FORMAT_DATE} disabled={isKHCT && issueDate != null} />
                </Form.Item>
              </Col>
              <Col span={6}>
                {type === 'individual' ? (
                  <Form.Item
                    label="Nơi cấp"
                    name="issueLocation"
                    rules={[{ required: true, message: 'Vui lòng chọn nơi cấp!' }]}
                  >
                    <Select
                      filterOption={(input, option) =>
                        typeof option?.label === 'string'
                          ? option.label.toLowerCase().includes(input.toLowerCase())
                          : false
                      }
                      allowClear
                      options={provinces?.map(item => ({
                        value: item.code,
                        label: item.nameVN,
                      }))}
                      labelInValue
                      showSearch
                      placeholder="Chọn nơi cấp"
                      onChange={value => {
                        form.setFieldsValue({
                          issueLocation: value || undefined,
                        });
                      }}
                      disabled={isKHCT && issueLocation != null}
                    />
                  </Form.Item>
                ) : (
                  <Form.Item
                    label="Nơi cấp"
                    name="issueLocation"
                    rules={[{ required: true, message: 'Vui lòng nhập nơi cấp', whitespace: true }]}
                  >
                    <Input placeholder="Nhập nơi cấp" maxLength={255} />
                  </Form.Item>
                )}
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={type === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                  name={type === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                  rules={[
                    {
                      required: true,
                      message: type === 'business' ? 'Vui lòng nhập tên công ty' : 'Vui lòng nhập tên khách hàng',
                      whitespace: true,
                    },
                  ]}
                >
                  <Input
                    placeholder={type === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                    maxLength={type === 'business' ? 120 : 60}
                    disabled={isKHCT}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                      e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Mã khách hàng"
                  name="code"
                  required
                  // rules={[{ required: true, message: 'Vui lòng mã khách hàng' }]}
                >
                  <Input placeholder="Mã khách hàng" disabled maxLength={14} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {type === 'business' && (
                <Col span={12}>
                  <Form.Item
                    label="Tên người đại diện"
                    name={['personalInfo', 'name']}
                    rules={[{ required: true, message: 'Vui lòng nhập tên người đại diện', whitespace: true }]}
                  >
                    <Input
                      placeholder="Nhập tên người đại diện"
                      maxLength={60}
                      disabled={isKHCT}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item
                  label="Số điện thoại"
                  name={['personalInfo', 'phone']}
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số điện thoại',
                    },
                    {
                      pattern: REGEX_PHONE_VN,
                      message: 'Số điện thoại phải bắt đầu bằng số 0 và từ 9 đến 15 chữ số',
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập số điện thoại"
                    maxLength={15}
                    onKeyDown={handleKeyDownEnterNumber}
                    disabled={isKHCT}
                  />
                </Form.Item>
              </Col>
              {type === 'individual' && (
                <Col span={12}>
                  <Form.Item
                    label="Địa chỉ email"
                    name={['personalInfo', 'email']}
                    rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                  >
                    <Input placeholder="Nhập địa chỉ email" maxLength={25} />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={16}>
              {type === 'individual' && (
                <Col span={24}>
                  <Form.Item
                    name={['info', 'gender']}
                    label="Giới tính"
                    rules={[{ required: true, message: 'Vui lòng nhập giới tính' }]}
                    layout="horizontal"
                  >
                    <Radio.Group options={OPTIONS_GENDER} style={{ marginLeft: 30 }} />
                  </Form.Item>
                </Col>
              )}

              <Col span={24}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      layout="horizontal"
                      label="Ngày sinh"
                      name={['info', 'onlyYear']}
                      valuePropName="checked"
                    >
                      <Checkbox
                        checked={yearOnly}
                        style={{ marginLeft: 30 }}
                        onChange={e => setYearOnly(e.target.checked)}
                      >
                        Chỉ năm sinh
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    {yearOnly ? (
                      <Form.Item name={['info', 'birthdayYear']}>
                        <DatePicker picker="year" format="YYYY" placeholder="YYYY" />
                      </Form.Item>
                    ) : (
                      <Form.Item name={['info', 'birthday']}>
                        <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Col>
            </Row>
            {initialData?.employeeTakeCare && (
              <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
                <SingleSelectLazy
                  keysLabel={'name'}
                  placeholder="Chọn mã nhân viên chăm sóc"
                  defaultValues={
                    initialData?.employeeTakeCare
                      ? {
                          value: initialData?.employeeTakeCare?.id,
                          label: `${initialData?.employeeTakeCare?.code} - ${initialData?.employeeTakeCare?.name}`,
                        }
                      : undefined
                  }
                  disabled
                />
              </Form.Item>
            )}

            {/* Địa chỉ công ty */}
            {type === 'business' && (
              <>
                <Title level={5}>Địa chỉ công ty</Title>
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item label="Địa chỉ" name={'companyAddress'} className="input-address">
                      <SelectAddress
                        placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                        parentName={'companyAddress'}
                        address={form.getFieldValue('companyAddress')}
                        handleAddressChange={validateForm}
                        isDisable={isKHCT && isCompanyAdressNonNull}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24} className="address">
                    <Form.Item name={['companyAddress', 'address']}>
                      <Input
                        placeholder="Nhập địa chỉ cụ thể"
                        maxLength={155}
                        disabled={isKHCT && isCompanyAdressNonNull}
                      />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {/* Địa chỉ thường trú */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ thường trú người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ thường trú</Title>
            )}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['address']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={form.getFieldValue('address')}
                    handleAddressChange={validateForm}
                    isDisable={isKHCT && isAddressNonNull}
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['address', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled={isKHCT && isAddressNonNull} />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ liên lạc */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ liên lạc</Title>
            )}
            <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
              <Checkbox onChange={handleCloneAddress}>Sử dụng địa chỉ thường trú</Checkbox>
            </Form.Item>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['rootAddress']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName="rootAddress"
                    address={rootAddress}
                    handleAddressChange={validateForm}
                    isDisable={(isKHCT && isRootAddressNonNull) || cloneAddress}
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['rootAddress', 'address']}>
                  <Input
                    placeholder="Nhập địa chỉ cụ thể"
                    maxLength={155}
                    disabled={(isKHCT && isRootAddressNonNull) || cloneAddress}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Thông tin thanh toán */}
            <Title level={5}>Thông tin thanh toán</Title>
            <Form.Item label="Tài khoản giao dịch">
              <div
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: 20,
                  margin: 0,
                  marginBottom: 16,
                }}
              >
                <Form.List name="bankAccount">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Row align="middle" gutter={[8, 8]} key={key}>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, 'bankName']}
                              label="Ngân hàng"
                              rules={[{ required: false }]}
                            >
                              <Select
                                placeholder="Chọn ngân hàng"
                                allowClear
                                filterOption={(input, option) =>
                                  typeof option?.label === 'string'
                                    ? option.label.toLowerCase().includes(input.toLowerCase())
                                    : false
                                }
                                showSearch
                                loading={isLoadingBanks}
                                options={banks.map(item => ({
                                  value: item?.bankCode,
                                  label: item?.bankName,
                                }))}
                                onChange={(value, option) => {
                                  form.setFieldsValue({
                                    bankAccount: {
                                      [name]: {
                                        bankCode: value,
                                        bankName: Array.isArray(option) ? '' : option?.label || '',
                                        accountNumber: form.getFieldValue(['bankAccount', name, 'accountNumber']) || '',
                                        beneciary: form.getFieldValue(['bankAccount', name, 'beneciary']) || '',
                                      },
                                    },
                                  });
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              {...restField}
                              name={[name, 'accountNumber']}
                              label="Số tài khoản"
                              rules={[
                                {
                                  message: 'Vui lòng nhập số tài khoản',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập số tài khoản');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input placeholder="Nhập số tài khoản" maxLength={20} />
                            </Form.Item>
                          </Col>
                          <Col span={7}>
                            <Form.Item
                              {...restField}
                              name={[name, 'beneciary']}
                              label="Tên người thụ hưởng"
                              rules={[
                                {
                                  message: 'Vui lòng nhập tên người thụ hưởng',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập tên người thụ hưởng');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input placeholder="Nhập tên người thụ hưởng" maxLength={255} />
                            </Form.Item>
                          </Col>
                          <Col span={1}>
                            <CloseOutlined
                              style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                              onClick={() => {
                                remove(name);
                                handleRemoveBankAccount(name);
                              }}
                            />
                          </Col>
                        </Row>
                      ))}

                      {fields.length < 10 ? (
                        <Col span={23}>
                          <Button
                            type="dashed"
                            onClick={() => {
                              add({ bankCode: undefined, bankName: undefined, accountNumber: '', beneciary: '' });
                            }}
                            style={{ padding: 0 }}
                            block
                            icon={<PlusOutlined />}
                          >
                            Thêm tài khoản giao dịch
                          </Button>
                        </Col>
                      ) : null}
                    </>
                  )}
                </Form.List>
              </div>
            </Form.Item>
            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name="mainBankId"
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select
                  placeholder="Chọn tài khoản giao dịch chính"
                  options={defaultBankOptions}
                  onChange={handleSelectBankInfo}
                  showSearch
                  optionFilterProp="label"
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                />
              </Form.Item>
              {/* Trường ẩn để lưu trữ đối tượng MainBank */}
              <Form.Item name="mainBank" hidden>
                <Input type="hidden" />
              </Form.Item>
            </div>

            {/* Thông tin dự án */}
            <Title level={5}>Thông tin dự án</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Chương trình bán hàng" name="salesProgramId">
                  <SingleSelectLazy
                    apiQuery={getSalePrograms}
                    queryKey={['get-list-sale-programs']}
                    keysLabel={'name'}
                    placeholder="Chọn chương trình bán hàng"
                    handleSelect={handleSelectSaleProgram}
                    moreParams={{ projectId: projectId, allowBookingPriority: true }}
                    allowClear={true}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã sản phẩm" name="demandPropertyId">
                  <SingleSelectLazy
                    key={`product-select-${productSelectKey}`}
                    apiQuery={getProductUnits}
                    queryKey={['get-list-product-units']}
                    keysLabel={'code'}
                    placeholder="Chọn mã sản phẩm"
                    handleSelect={handleSelectProductCode}
                    moreParams={
                      propertyUnitParams.salesProgramIds || propertyUnitParams.projectId
                        ? propertyUnitParams
                        : undefined
                    }
                    allowClear={true}
                  />
                </Form.Item>
              </Col>
              {amountTemplateFiles && amountTemplateFiles.length > 0 && (
                <Col span={12}>
                  <Form.Item
                    label="Số tiền đăng ký"
                    name="demandCategory"
                    rules={[{ required: true, message: 'Vui lòng nhập số tiền đăng ký' }]}
                  >
                    <Select
                      placeholder="Chọn số tiền đăng ký"
                      allowClear
                      filterOption={(input, option) =>
                        typeof option?.label === 'string'
                          ? option.label.toLowerCase().includes(input.toLowerCase())
                          : false
                      }
                      showSearch
                      options={amountTemplateFiles.map(item => ({
                        value: item?.projectCustomFormId || '',
                        label: item?.amountLabel || '',
                      }))}
                      onChange={(_, option) => {
                        const selectedOption = Array.isArray(option) ? option[0] : option;
                        form.setFieldsValue({ demandCategory: selectedOption?.label || '' });
                      }}
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
          </Col>
          <Col span={8}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin khác</Title>
            <Form.Item label="Ghi chú" name="description">
              <TextArea placeholder="Nhập ghi chú" maxLength={250} rows={4} />
            </Form.Item>
            <Form.Item name="files">
              <UploadFileBookingTicket
                fileList={fileList}
                setFileList={setFileList}
                uploadPath="property/primarty-transaction"
                size={25}
              />
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </ModalComponent>
  );
};

export default CreateBookingRequestStep3;

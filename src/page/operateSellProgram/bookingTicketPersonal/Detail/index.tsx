import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Select,
  DatePicker,
  Checkbox,
  Row,
  Col,
  Radio,
  Typography,
  Button,
  UploadFile,
  Timeline,
} from 'antd';

// Interface cho response của createUrlPayment API

import {
  BOOKING_TICKET_STATUS_NAME,
  FORMAT_DATE,
  FORMAT_DATE_TIME,
  OPTIONS_GENDER,
} from '../../../../constants/common';
import { useCheckPermissions, useCreateField, useFetch, useUpdateField } from '../../../../hooks';
import {
  adminApproveCancelRequestTicket,
  adminApproveTicket,
  adminCancelRequestTicket,
  cancelRequestTicket,
  createUrlPayment,
  csApproveCancelRequestTicket,
  csApproveTicket,
  csCancelRequestTicket,
  getDetailBookingTicket,
  getSalePrograms,
  updateBookingTicket,
} from '../../../../service/bookingTicket';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import { BookingTicketInfo, SaleProgram, Reciept, CreateUrlPaymentResponse } from '../../../../types/bookingRequest';
import './styles.scss';
import dayjs from 'dayjs';
import SelectAddress from '../../../../components/selectAddress';
import UploadFileBookingTicket from '../components/UploadFileBookingTicket';
import FPTLogo from '../../../../assets/images/FPT_logo.png';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { PERMISSION_PROPERTY_TICKET } from '../../../../constants/permissions/propertyTicket';
import ConfirmApproveModal from '../components/ConfirmApproveModal';
import { MutationFunction } from '@tanstack/react-query';
import ConfirmActionModal from '../../../../components/modal/specials/ConfirmActionModal';

const { Title, Text } = Typography;
const { Option } = Select;
const { TextArea } = Input;
interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

const BookingTicketDetail: React.FC<{}> = () => {
  const { id, bookingTicketId } = useParams();
  const [form] = Form.useForm();
  const type = Form.useWatch(['type'], form);
  const [yearOnly, setYearOnly] = useState(false);
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [urlPayments, setUrlPayments] = useState<Record<string, string>>({});
  const [isOpenModalApprove, setIsOpenModalApprove] = useState(false);
  const [isOpenModalReject, setIsOpenModalReject] = useState(false);
  const [isOpenModalCancelConfirm, setIsOpenModalCancelConfirm] = useState(false);
  const [isOpenModalCancelReject, setIsOpenModalCancelReject] = useState(false);

  const { csApprovedTicket, adminApprovedTicket } = useCheckPermissions(PERMISSION_PROPERTY_TICKET);

  const { mutateAsync: _updateBookingTicket } = useUpdateField({
    keyOfDetailQuery: ['get-detail-booking-ticket', bookingTicketId],
    apiQuery: updateBookingTicket,
    isMessageError: false,
  });

  const { mutateAsync: _createUrlPayment } = useCreateField({
    apiQuery: createUrlPayment,
    keyOfDetailQuery: ['create-url-payment'],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });
  const { mutateAsync: _adminApproveTicket } = useCreateField({
    apiQuery: adminApproveTicket,
    keyOfDetailQuery: ['get-detail-booking-ticket', bookingTicketId],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const { mutateAsync: _csApproveTicket } = useCreateField({
    apiQuery: csApproveTicket,
    keyOfDetailQuery: ['get-detail-booking-ticket', bookingTicketId],
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const { data: bookingTicketData } = useFetch<BookingTicketInfo>({
    api: () => bookingTicketId && getDetailBookingTicket({ id: bookingTicketId }),
    queryKeyArr: ['get-detail-booking-ticket', bookingTicketId],
    enabled: !!bookingTicketId,
    cacheTime: 10,
  });

  const bookingTicket = bookingTicketData?.data?.data;

  const amountTemplateFiles = bookingTicket?.project?.setting?.templateFiles;

  useEffect(() => {
    if (bookingTicket) {
      const bankInfoDefault = `${bookingTicket?.customer?.bankInfo[0]?.name} - ${bookingTicket?.customer?.bankInfo[0]?.accountNumber} - ${bookingTicket?.customer?.bankInfo[0]?.beneciary}`;
      const transformedBookingTicket = {
        type: bookingTicket.customer?.type || 'business',
        customer: { code: bookingTicket?.customer?.code || '' },
        identityType: bookingTicket.customer?.identityType || '',
        taxCode: bookingTicket.customer?.taxCode || '',
        identityNumber: bookingTicket.customer?.identityNumber || '',
        identityDate: bookingTicket.customer?.identityDate
          ? dayjs(bookingTicket.customer.identityDate, FORMAT_DATE)
          : null,
        identityPlace: bookingTicket.customer?.identityPlace || '',
        company: {
          name: bookingTicket.customer?.company?.name || '',
        },
        personalInfo: {
          name: bookingTicket.customer?.personalInfo?.name || '',
          phone: bookingTicket.customer?.personalInfo?.phone || '',
          email: bookingTicket.customer?.personalInfo?.email || '',
        },
        info: {
          gender: bookingTicket.customer?.info?.gender,
          onlyYear: bookingTicket.customer?.info?.onlyYear || false,
          birthday: bookingTicket.customer?.info?.birthday
            ? dayjs(bookingTicket.customer?.info?.birthday, FORMAT_DATE)
            : null,
          birthdayYear: bookingTicket.customer?.info?.birthdayYear
            ? dayjs(bookingTicket.customer?.info?.birthdayYear)
            : null,
          cloneAddress: bookingTicket.customer?.info?.useResidentialAddress || false,
        },
        employeeTakeCareId: bookingTicket.employee?.name || '',
        companyAdress: bookingTicket.customer?.company?.address || '',
        address: bookingTicket.customer?.info?.address || '',
        rootAddress: bookingTicket.customer?.info?.rootAddress || '',
        bankInfo: bankInfoDefault || '',
        salesProgram: {
          id: bookingTicket?.salesProgram?.id || '',
          name: bookingTicket?.salesProgram?.name || '',
        },
        salesProgramId: bookingTicket?.salesProgramId || '',
        propertyUnit: { code: bookingTicket.propertyUnit?.code || '' },
        demandCategory: bookingTicket?.demandCategory,
        note: bookingTicket.note || '',
        files: bookingTicket.files || [],
      };
      // Cập nhật giá trị form
      form.setFieldsValue(transformedBookingTicket);
      setFileList((bookingTicket?.files || []) as ExtendedUploadFile[]);
      setYearOnly(bookingTicket.customer?.info?.onlyYear || false);
    }
  }, [bookingTicket, form]);

  const handleCreateUrlPayment = async (receiptId: string, amount: number) => {
    const payload = {
      amount: amount,
      transactionId: receiptId,
      bankCode: '',
    };
    const resp = (await _createUrlPayment(payload)) as CreateUrlPaymentResponse;
    const paymentUrl = resp?.data?.data?.link || '';

    // Cập nhật URL payment cho receipt cụ thể
    setUrlPayments(prev => ({
      ...prev,
      [receiptId]: paymentUrl,
    }));
  };

  const handleUpdateBookingTicket = async () => {
    const values = await form.validateFields();
    const payload = {
      id: bookingTicket?.id,
      salesProgramId: values?.salesProgram?.id,
      note: values?.note,
    };
    await _updateBookingTicket(payload);
  };

  const handleSelectSaleProgram = (values: SaleProgram) => {
    form.setFieldsValue({
      salesProgram: {
        id: values?.id,
        name: values?.name,
      },
      salesProgramId: values?.id,
    });
  };

  return (
    <div className="booking-ticket-detail">
      <BreadCrumbComponent
        customItems={[
          { label: 'Dự án và sản phẩm' },
          { label: 'Danh sách dự án', path: '/projects' },
          { label: `Thông tin dự án - ${bookingTicket?.project?.name || ''}`, path: `/projects/${id}` },
          { label: `Vận hành bán hàng dự án ${bookingTicket?.project?.name || ''}`, path: '#' },
        ]}
        titleBread={bookingTicket?.bookingTicketCode}
        noMenu={true}
      />
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>{`Phiếu đặt chỗ ${bookingTicket?.project?.name || ''}`}</Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{bookingTicket?.project?.name || ''}</span>
          </Text>
        </div>
        <div className="project-actions">
          <Button
            disabled={bookingTicket?.status === 'CS_APPROVED_TICKET' || bookingTicket?.status === 'BOOKING_APPROVED'}
            onClick={handleUpdateBookingTicket}
          >
            Lưu
          </Button>
          <Button>Gửi Email</Button>

          {csApprovedTicket && adminApprovedTicket && (
            <>
              {(bookingTicket?.status === 'ADMIN_APPROVED_TICKET' || bookingTicket?.status === 'CLOSE') && (
                <Button onClick={() => setIsOpenModalApprove(true)}>Xác nhận</Button>
              )}
              {bookingTicket?.status !== 'CS_APPROVED_TICKET' &&
                bookingTicket?.status !== 'CS_REJECTED_TICKET' &&
                bookingTicket?.status !== 'ADMIN_REJECTED_TICKET' &&
                bookingTicket?.status !== 'BOOKING_APPROVED' &&
                bookingTicket?.status !== 'CANCEL_REQUESTED' &&
                bookingTicket?.status !== 'ADMIN_APPROVED_CANCEL_REQUESTED' &&
                bookingTicket?.status !== 'CS_APPROVED_CANCEL_REQUESTED' && (
                  <Button onClick={() => setIsOpenModalReject(true)}>Từ chối</Button>
                )}
              {(bookingTicket?.status === 'CANCEL_REQUESTED' ||
                bookingTicket?.status === 'ADMIN_APPROVED_CANCEL_REQUESTED') && (
                <>
                  <Button onClick={() => setIsOpenModalCancelConfirm(true)}>Xác nhận đề nghị hủy</Button>
                  <Button onClick={() => setIsOpenModalCancelReject(true)}>Từ chối đề nghị hủy</Button>
                </>
              )}
            </>
          )}
          {bookingTicket?.status === 'BOOKING_APPROVED' && (
            <Button onClick={() => setIsOpenModalCancelConfirm(true)}>Đề nghị hủy</Button>
          )}

          <Button>Tải xuống</Button>
        </div>
        <div className="project-image">
          <img
            src={
              bookingTicket?.project?.imageUrl
                ? `${import.meta.env.VITE_S3_IMAGE_URL}/${bookingTicket?.project?.imageUrl}`
                : FPTLogo
            }
            alt="Project"
          />
        </div>
      </div>
      <Form form={form} layout="vertical" style={{ marginTop: 32 }}>
        <Row gutter={32}>
          <Col span={16}>
            {/* Thông tin khách hàng */}
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Loại khách hàng" name="type">
                  <Select placeholder="Chọn loại khách hàng" disabled>
                    <Option value="individual">Cá nhân</Option>
                    <Option value="business">Doanh nghiệp</Option>
                  </Select>
                </Form.Item>
              </Col>
            </Row>

            <p style={{ marginBottom: 8 }}>
              Giấy tờ xác minh <span style={{ color: 'red' }}>*</span>
            </p>
            <Row
              gutter={[8, 8]}
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Col span={6}>
                <Form.Item label="Loại giấy tờ" name="identityType">
                  <Select placeholder="Chọn loại giấy tờ" disabled>
                    {type === 'individual' ? (
                      <>
                        <Option value="CCCD">Căn cước công dân</Option>
                        <Option value="CMND">CMT</Option>
                        <Option value="PASSPORT">Hộ chiếu</Option>
                      </>
                    ) : (
                      <Option value="MST">Mã số thuế</Option>
                    )}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item
                  label="Số giấy tờ"
                  name="identityNumber"
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số giấy tờ',
                    },
                  ]}
                >
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Ngày cấp" name="identityDate">
                  <DatePicker placeholder="Chọn ngày cấp" format={FORMAT_DATE} disabled />
                </Form.Item>
              </Col>
              <Col span={6}>
                <Form.Item label="Nơi cấp" name="identityPlace">
                  <Input placeholder="Nhập số giấy tờ" maxLength={60} disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={type === 'business' ? 'Tên công ty' : 'Tên khách hàng'}
                  name={type === 'business' ? ['company', 'name'] : ['personalInfo', 'name']}
                >
                  <Input
                    placeholder={type === 'business' ? 'Nhập tên công ty' : 'Nhập tên khách hàng'}
                    maxLength={type === 'business' ? 120 : 60}
                    onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                      e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                    }}
                    disabled
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã khách hàng" name={['customer', 'code']}>
                  <Input placeholder="Mã khách hàng" disabled maxLength={14} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={16}>
              {type === 'business' && (
                <Col span={12}>
                  <Form.Item label="Tên người đại diện" name={['personalInfo', 'name']}>
                    <Input
                      placeholder="Nhập tên người đại diện"
                      maxLength={60}
                      onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                        e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                      }}
                      disabled
                    />
                  </Form.Item>
                </Col>
              )}

              <Col span={12}>
                <Form.Item label="Số điện thoại" name={['personalInfo', 'phone']}>
                  <Input placeholder="Nhập số điện thoại" maxLength={15} disabled />
                </Form.Item>
              </Col>
              {type === 'individual' && (
                <Col span={12}>
                  <Form.Item label="Địa chỉ email" name={['personalInfo', 'email']}>
                    <Input placeholder="Nhập địa chỉ email" maxLength={25} disabled />
                  </Form.Item>
                </Col>
              )}
            </Row>

            <Row gutter={16}>
              {type === 'individual' && (
                <Col span={24}>
                  <Form.Item name={['info', 'gender']} label="Giới tính" layout="horizontal">
                    <Radio.Group options={OPTIONS_GENDER} style={{ marginLeft: 30 }} disabled />
                  </Form.Item>
                </Col>
              )}

              <Col span={24}>
                <Row gutter={16}>
                  <Col span={12}>
                    <Form.Item
                      layout="horizontal"
                      label="Ngày sinh"
                      name={['info', 'onlyYear']}
                      valuePropName="checked"
                    >
                      <Checkbox
                        checked={yearOnly}
                        style={{ marginLeft: 30 }}
                        onChange={e => setYearOnly(e.target.checked)}
                        disabled
                      >
                        Chỉ năm sinh
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    {yearOnly ? (
                      <Form.Item name={['info', 'birthdayYear']}>
                        <DatePicker picker="year" format="YYYY" placeholder="YYYY" disabled />
                      </Form.Item>
                    ) : (
                      <Form.Item name={['info', 'birthday']}>
                        <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} disabled />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Col>
            </Row>

            <Form.Item label="Nhân viên chăm sóc" name="employeeTakeCareId">
              <Input placeholder="Chọn nhân viên chăm sóc" maxLength={155} disabled />
            </Form.Item>

            {/* Địa chỉ công ty */}
            {type === 'business' && (
              <>
                <Title level={5}>Địa chỉ công ty</Title>
                <Row gutter={16}>
                  <Col span={24}>
                    <Form.Item label="Địa chỉ" name={'companyAdress'} className="input-address">
                      <SelectAddress
                        placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                        parentName={'companyAdress'}
                        address={form.getFieldValue('companyAdress')}
                        isDisable
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24} className="address">
                    <Form.Item name={['companyAdress', 'address']}>
                      <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                    </Form.Item>
                  </Col>
                </Row>
              </>
            )}

            {/* Địa chỉ thường trú */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ thường trú người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ thường trú</Title>
            )}
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['info', 'address']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'address'}
                    address={form.getFieldValue('address')}
                    isDisable
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['address', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Địa chỉ liên lạc */}
            {type === 'business' ? (
              <Title level={5}>Địa chỉ liên lạc người đại diện</Title>
            ) : (
              <Title level={5}>Địa chỉ liên lạc</Title>
            )}
            <Form.Item name={['info', 'cloneAddress']} valuePropName="checked">
              <Checkbox disabled>Sử dụng địa chỉ thường trú</Checkbox>
            </Form.Item>

            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Địa chỉ" name={['info', 'rootAddress']} className="input-address">
                  <SelectAddress
                    placeHolder="Tỉnh/Thành phố, Quận/Huyện, Phường/Xã"
                    parentName={'rootAddress'}
                    address={form.getFieldValue('rootAddress')}
                    isDisable
                  />
                </Form.Item>
              </Col>
              <Col span={24} className="address">
                <Form.Item name={['rootAddress', 'address']}>
                  <Input placeholder="Nhập địa chỉ cụ thể" maxLength={155} disabled />
                </Form.Item>
              </Col>
            </Row>

            {/* Thông tin thanh toán */}
            <Title level={5}>Thông tin thanh toán</Title>

            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item name={'bankInfo'}>
                <Select placeholder="Chọn tài khoản giao dịch chính" disabled></Select>
              </Form.Item>
            </div>

            {/* Thông tin dự án */}
            <Title level={5}>Thông tin dự án</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Chương trình bán hàng" name={['salesProgram', 'id']}>
                  <SingleSelectLazy
                    enabled={!!bookingTicketId}
                    apiQuery={getSalePrograms}
                    queryKey={['get-list-sale-programs']}
                    keysLabel={'name'}
                    placeholder="Chọn chương trình bán hàng"
                    handleSelect={handleSelectSaleProgram}
                    moreParams={{
                      projectId: id,
                      allowBookingPriority: true,
                    }}
                    disabled
                    defaultValues={
                      bookingTicket?.salesProgram
                        ? {
                            value: bookingTicket.salesProgram.id,
                            label: bookingTicket.salesProgram.name,
                          }
                        : undefined
                    }
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã sản phẩm" name={['propertyUnit', 'code']}>
                  <Input placeholder="Nhập mã sản phẩm" maxLength={50} disabled />
                </Form.Item>
              </Col>
              {amountTemplateFiles && amountTemplateFiles.length > 0 && (
                <Col span={12}>
                  <Form.Item label="Số tiền đăng ký" name="demandCategory">
                    <Select placeholder="Chọn số tiền đăng ký" disabled />
                  </Form.Item>
                </Col>
              )}
            </Row>
            {/* Thông tin gửi email */}
            <Title level={5}>Thông tin gửi email</Title>
            <Text>Thời gian gửi email lần cuối: </Text>

            {/* Thông tin thanh toán online */}
            <Title level={5}>Thanh toán online</Title>
            {bookingTicket?.reciept && bookingTicket.reciept.length > 0 ? (
              bookingTicket.reciept.map((receipt: Reciept, index: number) => (
                <div
                  key={receipt.id || index}
                  style={{
                    marginBottom: 16,
                    padding: 16,
                    border: '1px solid #d9d9d9',
                    borderRadius: 6,
                    backgroundColor: '#fafafa',
                  }}
                >
                  <div style={{ marginBottom: 8 }}>
                    <Text>
                      <strong>Mã phiếu thu:</strong> <Text style={{ marginLeft: 8 }}>{receipt.code}</Text>
                    </Text>
                    <Button
                      type="default"
                      onClick={() => handleCreateUrlPayment(receipt.id!, receipt.amount)}
                      disabled={bookingTicket?.status === 'CS_APPROVED_TICKET'}
                      style={{ marginLeft: 30 }}
                    >
                      Tạo link thanh toán
                    </Button>
                  </div>

                  <div>
                    <Text>
                      <strong>Link thanh toán:</strong>
                    </Text>
                    <a
                      href={urlPayments[receipt.id!]}
                      target="_blank"
                      rel="noopener noreferrer"
                      style={{
                        overflowWrap: 'break-word',
                        wordBreak: 'break-all',
                        display: 'inline-block',
                        maxWidth: '100%',
                        color: '#1890ff',
                        fontSize: 12,
                      }}
                    >
                      {urlPayments[receipt.id!]}
                    </a>
                  </div>
                </div>
              ))
            ) : (
              <Text></Text>
            )}
          </Col>
          <Col span={8}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin khác</Title>
            <Form.Item label="Ghi chú" name="note">
              <TextArea
                placeholder="Nhập ghi chú"
                maxLength={250}
                rows={4}
                disabled={bookingTicket?.status === 'CS_APPROVED_TICKET'}
              />
            </Form.Item>
            <Form.Item name="files">
              <UploadFileBookingTicket
                fileList={fileList}
                setFileList={setFileList}
                uploadPath="property/primarty-transaction"
                size={25}
                isDetail
              />
            </Form.Item>

            <Title level={5}>Lịch sử</Title>
            <Timeline
              style={{ height: '500px', overflowY: 'auto', padding: '20px 0' }}
              items={bookingTicket?.historyStatus?.map(item => ({
                children: (
                  <div>
                    <div className="text-history-time">{dayjs(item?.date).format(FORMAT_DATE_TIME)}</div>
                    <div className="text-history">{item?.status ? BOOKING_TICKET_STATUS_NAME[item.status] : ''}</div>
                  </div>
                ),
                color: 'blue',
              }))}
            />
          </Col>
        </Row>
      </Form>

      <ConfirmApproveModal
        open={isOpenModalApprove}
        apiQuery={
          bookingTicket?.status === 'ADMIN_APPROVED_TICKET'
            ? (_csApproveTicket as MutationFunction<unknown, unknown>)
            : (_adminApproveTicket as MutationFunction<unknown, unknown>)
        }
        keyOfListQuery={['get-detail-booking-ticket', bookingTicketId]}
        onCancel={() => {
          setIsOpenModalApprove(false);
        }}
        title="Xác nhận duyệt phiếu"
        isTitlePlaceholder
        labelConfirm="Xác nhận"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        record={bookingTicket}
        actionType="approve"
      />

      <ConfirmApproveModal
        open={isOpenModalReject}
        apiQuery={
          bookingTicket?.status === 'ADMIN_APPROVED_TICKET'
            ? (_csApproveTicket as MutationFunction<unknown, unknown>)
            : (_adminApproveTicket as MutationFunction<unknown, unknown>)
        }
        keyOfListQuery={['get-detail-booking-ticket', bookingTicketId]}
        onCancel={() => {
          setIsOpenModalReject(false);
        }}
        title="Xác nhận từ chối duyệt phiếu"
        isTitlePlaceholder
        labelConfirm="Xác nhận"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        record={bookingTicket}
        actionType="reject"
      />

      <ConfirmActionModal
        open={isOpenModalCancelConfirm && bookingTicket?.status === 'BOOKING_APPROVED'}
        apiQuery={cancelRequestTicket as MutationFunction<unknown, unknown>}
        keyOfDetailQuery={['get-detail-booking-ticket', bookingTicketId]}
        onCancel={() => {
          setIsOpenModalCancelConfirm(false);
        }}
        title="Đề nghị hủy YCĐCHO"
        description="Vui lòng nhập lý do hủy yêu cầu này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Đồng ý"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        disable={true}
        payload={{ id: bookingTicketId }}
        showReasonField={true}
      />

      <ConfirmActionModal
        open={
          isOpenModalCancelConfirm &&
          (bookingTicket?.status === 'CANCEL_REQUESTED' || bookingTicket?.status === 'ADMIN_APPROVED_CANCEL_REQUESTED')
        }
        apiQuery={
          bookingTicket?.status === 'CANCEL_REQUESTED'
            ? (adminApproveCancelRequestTicket as MutationFunction<unknown, unknown>)
            : (csApproveCancelRequestTicket as MutationFunction<unknown, unknown>)
        }
        keyOfDetailQuery={['get-detail-booking-ticket', bookingTicketId]}
        onCancel={() => {
          setIsOpenModalCancelConfirm(false);
        }}
        title="Duyệt đề nghị hủy YCĐCHO"
        description="Vui lòng nhập lý do duyệt yêu cầu này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Đồng ý"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        disable={true}
        payload={{ id: bookingTicketId }}
        showReasonField={true}
      />

      <ConfirmActionModal
        open={
          isOpenModalCancelReject &&
          (bookingTicket?.status === 'CANCEL_REQUESTED' || bookingTicket?.status === 'ADMIN_APPROVED_CANCEL_REQUESTED')
        }
        apiQuery={
          bookingTicket?.status === 'CANCEL_REQUESTED'
            ? (adminCancelRequestTicket as MutationFunction<unknown, unknown>)
            : (csCancelRequestTicket as MutationFunction<unknown, unknown>)
        }
        keyOfDetailQuery={['get-detail-booking-ticket', bookingTicketId]}
        onCancel={() => setIsOpenModalCancelReject(false)}
        title="Từ chối đề nghị hủy YCĐCHO"
        description="Vui lòng nhập lý do từ chối yêu cầu này"
        isTitlePlaceholder
        labelCancel="Hủy"
        labelConfirm="Đồng ý"
        fieldNameReason="reason"
        isUpdate={true}
        maxLength={255}
        disable={true}
        payload={{ id: bookingTicketId }}
        showReasonField={true}
      />
    </div>
  );
};

export default BookingTicketDetail;

import { DatePicker, Form } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';

dayjs.extend(utc);
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { useLocation } from 'react-router-dom';
import useFilter from '../../../../hooks/filter';
import { DEFAULT_PARAMS, STATUS, TypeProposal } from '../../../../constants/common';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { getListProjectHistory } from '../../../../service/uploadHistory';
import MultiSelectStatic from '../../../../components/select/mutilSelectStatic';
import './styles.scss';
import { getListAccount, getListOrgchartExternal, getListOrgchartInternal } from '../../../../service/offer';
import MultilSelectDropdownLazy from '../../../../components/select/multilSelectDropdownLazy';

type TFilter = {
  types?: string[];
  statuses?: string[];
  createdByIds?: string[];
  projectIds?: string[];
  createdTo?: string | Dayjs | null;
  createdFrom?: string | Dayjs | null;
  receiptDateTo?: string | Dayjs | null;
  receiptDateFrom?: string | Dayjs | null;
  orgchartIds?: string[];
};
const { RangePicker } = DatePicker;

function FilterOfferMoney() {
  const { search } = useLocation();
  const [form] = Form.useForm();
  const params = useMemo(() => new URLSearchParams(search), [search]);
  const [filter, setFilter] = useFilter();
  const createRangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const receiveRangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const [createDates, setCreateDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);
  const [receiveDates, setReceiveDates] = useState<[Dayjs | null, Dayjs | null]>([null, null]);

  const [initialValues, setInitialValues] = useState<TFilter>();
  const [isOpenFilter, setIsOpenFilter] = useState<boolean>(false);

  useEffect(() => {
    if (params) {
      setInitialValues({
        types: params.get('types') ? params.get('types')?.split(',') : [],
        statuses: params.get('statuses') ? params.get('statuses')?.split(',') : [],
        createdByIds: params.get('createdByIds') ? params.get('createdByIds')?.split(',') : [],
        projectIds: params.get('projectIds') ? params.get('projectIds')?.split(',') : [],
        createdFrom: params.get('startDate') ? dayjs(params.get('startDate')) : '',
        createdTo: params.get('endDate') ? dayjs(params.get('endDate')) : '',
        receiptDateFrom: params.get('startDate') ? dayjs(params.get('startDate')) : '',
        receiptDateTo: params.get('endDate') ? dayjs(params.get('endDate')) : '',
      });
    }
  }, [params]);

  const handleSubmitFilter = (values: TFilter) => {
    const [startCreateDate, endCreateDate] = createDates;
    const [startReceiveDate, endReceiveDate] = receiveDates;

    const newImportFilter: Record<string, string> = {
      types: values?.types?.join(',') || '',
      statuses: values?.statuses?.join(',') || '',
      createdByIds: values?.createdByIds?.join(',') || '',
      projectIds: values?.projectIds?.join(',') || '',
      createdFrom: startCreateDate ? startCreateDate?.startOf('day').toISOString() : '',
      createdTo: endCreateDate ? endCreateDate?.endOf('day').toISOString() : '',
      receiptDateFrom: startReceiveDate ? startReceiveDate.startOf('day').toISOString() : '',
      receiptDateTo: endReceiveDate ? endReceiveDate.endOf('day').toISOString() : '',
      orgchartIds: values?.orgchartIds?.join(',') || '',
      page: '1',
    };

    setFilter({ ...filter, page: '1', ...newImportFilter });
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };
  const handleSelectTypeProposal = (values: unknown) => {
    const newTypesFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ types: newTypesFilter });
  };

  const handleSelectStatus = (values: unknown) => {
    const newStatusFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ statuses: newStatusFilter });
  };

  const handleSelectCreateByIds = (values: unknown) => {
    const newCreatedByFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ createdByIds: newCreatedByFilter });
  };

  const handleSelectProject = (values: unknown) => {
    const newProjectFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ projectIds: newProjectFilter });
  };

  const handleCreateCalendarChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (createDates[0] !== null || createDates[1] !== null) {
          setCreateDates([null, null]);
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(createDates[0], 'day') || (!newStart && !createDates[0])) &&
        (newEnd?.isSame(createDates[1], 'day') || (!newEnd && !createDates[1]))
      ) {
        return;
      }

      setCreateDates([newStart, newEnd]);
    },
    [createDates],
  );
  const handleCreateRangePickerChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        setCreateDates([null, null]);
        form.setFieldsValue({
          createdTo: undefined,
          createdFrom: undefined,
        });
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      setCreateDates([newStart, newEnd]);
      // Cập nhật form mỗi khi chọn giá trị
      form.setFieldsValue({
        createdTo: newStart,
        createdFrom: newEnd,
      });
    },
    [createDates, form],
  );

  const handleReceiveCalendarChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        if (receiveDates[0] !== null || receiveDates[1] !== null) {
          setReceiveDates([null, null]);
        }
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      if (
        (newStart?.isSame(receiveDates[0], 'day') || (!newStart && !receiveDates[0])) &&
        (newEnd?.isSame(receiveDates[1], 'day') || (!newEnd && !receiveDates[1]))
      ) {
        return;
      }

      setReceiveDates([newStart, newEnd]);
    },
    [receiveDates],
  );

  const handleReceiveRangePickerChange = useCallback(
    (value: [Dayjs | null, Dayjs | null] | null) => {
      if (!value) {
        setReceiveDates([null, null]);
        // Đảm bảo cập nhật giá trị trong form
        form.setFieldsValue({
          receiptDateTo: undefined,
          receiptDateFrom: undefined,
        });
        return;
      }

      const newStart = value[0];
      const newEnd = value[1];

      setReceiveDates([newStart, newEnd]);
      // Cập nhật form mỗi khi chọn giá trị
      form.setFieldsValue({
        receiptDateTo: newStart,
        receiptDateFrom: newEnd,
      });
    },
    [receiveDates, form],
  );

  const handleCreateBlur = useCallback(
    (e: FocusEvent, index: 0 | 1) => {
      const target = e.target as HTMLInputElement;
      const value = target.value;

      if (!value && createDates[index] !== null) {
        const newDates = [...createDates] as [Dayjs | null, Dayjs | null];
        newDates[index] = null;
        setCreateDates(newDates);
      }
    },
    [createDates],
  );

  const handleReceiveBlur = useCallback(
    (e: FocusEvent, index: 0 | 1) => {
      const target = e.target as HTMLInputElement;
      const value = target.value;

      if (!value && receiveDates[index] !== null) {
        const newDates = [...receiveDates] as [Dayjs | null, Dayjs | null];
        newDates[index] = null;
        setReceiveDates(newDates);
      }
    },
    [receiveDates],
  );

  useEffect(() => {
    const pickerNode = createRangePickerRef.current?.nativeElement as HTMLElement | null;
    const inputs = pickerNode?.querySelectorAll<HTMLInputElement>('input');

    if (inputs?.length === 2) {
      const input0 = inputs[0];
      const input1 = inputs[1];

      const blurHandler0 = (e: FocusEvent) => handleCreateBlur(e, 0);
      const blurHandler1 = (e: FocusEvent) => handleCreateBlur(e, 1);

      input0.addEventListener('blur', blurHandler0);
      input1.addEventListener('blur', blurHandler1);

      return () => {
        input0.removeEventListener('blur', blurHandler0);
        input1.removeEventListener('blur', blurHandler1);
      };
    }
  }, [handleCreateBlur]);

  useEffect(() => {
    const pickerNode = receiveRangePickerRef.current?.nativeElement as HTMLElement | null;
    const inputs = pickerNode?.querySelectorAll<HTMLInputElement>('input');

    if (inputs?.length === 2) {
      const input0 = inputs[0];
      const input1 = inputs[1];

      const blurHandler0 = (e: FocusEvent) => handleReceiveBlur(e, 0);
      const blurHandler1 = (e: FocusEvent) => handleReceiveBlur(e, 1);

      input0.addEventListener('blur', blurHandler0);
      input1.addEventListener('blur', blurHandler1);

      return () => {
        input0.removeEventListener('blur', blurHandler0);
        input1.removeEventListener('blur', blurHandler1);
      };
    }
  }, [handleReceiveBlur]);

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setCreateDates([null, null]);
    setReceiveDates([null, null]);
    handleSelectProject([]);
    handleSelectStatus([]);
    handleSelectCreateByIds([]);
    handleSelectTypeProposal([]);
    setFilter({ ...DEFAULT_PARAMS, search: params.get('search') || '' });
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleSelectOrgchart = (values: unknown) => {
    const newOrgChartFilter = (values as { value: string }[]).map(item => item.value);
    form.setFieldsValue({ orgchartIds: newOrgChartFilter });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="filter-offer-money"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm theo từ khóa"
        showParams={true}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Loại đề nghị" name="types">
              <MultiSelectStatic
                data={TypeProposal}
                handleListSelect={handleSelectTypeProposal}
                placeholder="Chọn loại đề nghị"
                keysTag={'label'}
              />
            </Form.Item>
            <Form.Item label="Trạng thái" name="statuses">
              <MultiSelectStatic
                data={STATUS}
                handleListSelect={handleSelectStatus}
                placeholder="Chọn trạng thái"
                keysTag={'label'}
              />
            </Form.Item>

            <Form.Item label="Người tạo" name="createdByIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-account']}
                apiQuery={getListAccount}
                keysLabel={['username', 'name']}
                keysTag={['username', 'name']}
                handleListSelect={handleSelectCreateByIds}
                placeholder="Chọn người tạo"
              />
            </Form.Item>
            <Form.Item label="Đơn vị bán hàng" name="orgchartIds">
              <MultilSelectDropdownLazy
                queryKey={['get-orgchart']}
                selectedType="option1"
                textOption1="Đơn vị"
                textOption2="Đối tác hợp tác"
                apiQuery1={getListOrgchartInternal}
                apiQuery2={getListOrgchartExternal}
                keysLabelOption1={['code', 'nameVN']}
                keysTagOption1={['code', 'nameVN']}
                keysLabelOption2={['partnershipCode', 'partnershipName']}
                keysTagOption2={['partnershipCode', 'partnershipName']}
                handleListSelect={handleSelectOrgchart}
                placeholder="Chọn đơn vị bán hàng"
              />
            </Form.Item>
            <Form.Item label="Dự án" name="projectIds">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['get-project']}
                apiQuery={getListProjectHistory}
                keysLabel={['code', 'name']}
                keysTag={['name']}
                handleListSelect={handleSelectProject}
                placeholder="Chọn dự án"
              />
            </Form.Item>
            <Form.Item label="Khoảng thời gian tạo">
              <RangePicker
                ref={createRangePickerRef}
                value={createDates}
                onCalendarChange={handleCreateCalendarChange}
                onChange={handleCreateRangePickerChange}
                allowClear
                placeholder={['Từ ngày', 'Đến ngày']}
                format="DD/MM/YYYY"
              />
            </Form.Item>

            <Form.Item label="Khoảng thời gian nhận tiền">
              <RangePicker
                ref={receiveRangePickerRef}
                value={receiveDates}
                onCalendarChange={handleReceiveCalendarChange}
                onChange={handleReceiveRangePickerChange}
                allowClear
                placeholder={['Từ ngày', 'Đến ngày']}
                format="DD/MM/YYYY"
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
}
export default FilterOfferMoney;

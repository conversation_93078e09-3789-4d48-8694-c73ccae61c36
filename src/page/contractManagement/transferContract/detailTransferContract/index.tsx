import { Form, Input, Row, Col, Typography, Select, Spin, DatePicker, Checkbox, InputNumber, Radio, Space } from 'antd';
import { useNavigate, useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../../components/breadCrumb';
import {
  getDetailContract,
  getDivision,
  getListBusinessArea,
  getListDistributionChannel,
  getListInterestCalculations,
} from '../../../../service/contract';
import { useCheckPermissions, useFetch } from '../../../../hooks';
import {
  ExtendedUploadFile,
  PrimaryTransaction,
  StaffItem,
  TTransferContract,
} from '../../../../types/contract/transferContract';
import { useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { formatNumber } from '../../../../utilities/regex';
import dayjs from 'dayjs';
import { TPos, TSalePolicy } from '../../../../types/commission';
import CommonFileUpload from '../../../../components/upload/CommonUploadFiles';
import { TTransferContractPayload, TTransferContractForm } from '../../../../types/contract/transferContract';
import { getListDiscountPolicy, getListPaymentPolicy, getListSalesPolicy } from '../../../../service/contract';
import { IPaymentPolicy } from '../../../../types/paymentPolicy';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { DiscountPolicy } from '../../../../types/discountPolicy';
import './styles.scss';
import { OptionTypeSelect } from '../../../../types/common/common';
import PaymentHistory from '../../components/PaymentHistory';
import InterestCalculationList from '../../components/InterestCalculationList';
import ButtonOfPageDetail from '../../../../components/button/buttonOfPageDetail';
import { TRANSFER_CONTRACT_MANAGEMENT } from '../../../../configs/path';
import CustomerForm from '../../components/CustomerForm';
import CompanyInformationForm from '../../components/CompanyInformationForm';
import { FORMAT_DATE, FORMAT_DATE_API, OPTIONS_IDENTITIES } from '../../../../constants/common';
import { TIdentities } from '../../../../types/customers';
import { DefaultOptionType } from 'antd/lib/select';
import { usePaymentHistory } from '../../hooks/usePaymentHistory';
import { PERMISSION_CONTRACT } from '../../../../constants/permissions/contract';
import HeaderContract from '../../components/HeaderContract';
import StaffListTable from '../../components/StaffListTable';
import { LISTPAYMENTS, TabType } from '../../../../constants/contract';
import { useTransferContractStore } from '../../depositContracts/store';
import { validateEndDate } from '../../../../utilities/shareFunc';
import { TInterestCalculation } from '../../../../types/contract/depositContract';
import LoanInfoForm from '../../components/LoanInfoForm';

const { Option } = Select;
const { Title } = Typography;

const DetailTransferContract = () => {
  const { initialValue, setDisabled, setInitialValue } = useTransferContractStore();
  const navigate = useNavigate();
  const { id } = useParams();

  const { data: dataDetail, isLoading } = useFetch<TTransferContract>({
    queryKeyArr: ['detail-transfer-contract', id],
    api: () => getDetailContract({ id }),
    moreParams: { id },
    withFilter: false,
    enabled: !!id,
    cacheTime: 10,
  });
  const dataDetailContract = dataDetail?.data?.data;

  // const { mutateAsync, isPending } = useUpdateField({
  //   apiQuery: updateTransferContract,
  //   keyOfListQuery: ['get-list-transfer-contract'],
  //   keyOfDetailQuery: ['detail-transfer-contract', id],
  //   checkDuplicate: true,
  //   isMessageError: false,
  // });

  // const { mutateAsync: print } = useUpdateField({
  //   keyOfListQuery: ['get-list-Transfer-contract'],
  //   keyOfDetailQuery: ['detail-Transfer-contract', id],
  //   apiQuery: printContract,
  //   isMessageError: false,
  //   messageSuccess: 'In hợp đồng thành công!',
  // });

  const [form] = Form.useForm();
  const isShowOwned = Form.useWatch('isShowOwned', form);
  const changeInstallment = Form.useWatch('changeInstallment', form);
  const isShowCompanyInformation = Form.useWatch('isShowCompanyInformation', form);
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [installments, setInstallments] = useState<{ label: string; value: string }[]>([]);
  const [staffList, setStaffList] = useState<StaffItem[]>([]);
  const [detailDataTicket, setDetailDataTicket] = useState<PrimaryTransaction>({});
  const [pos, setPos] = useState<TPos>({ id: '', code: '', name: '' });
  const [maintenanceFeeType, setMaintenanceFeeType] = useState<string>('percent'); // Loại chiết khấu
  const [isPrimaryTransaction, setIsPrimaryTransaction] = useState<boolean>(false);
  const [defaultDiscountPolicy, setDefaultDiscountPolicy] = useState<OptionTypeSelect[]>([]);
  const [tabSelect, setTabSelect] = useState('TAB1');
  const [defaultPaymentPolicy, setDefaultPaymentPolicy] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });

  const [defaultCustomer, setDefaultCustomer] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });

  const [defaultSalePolicy, setDefaultSalePolicy] = useState<OptionTypeSelect>({ value: undefined, label: undefined });
  const [resetUpload, setResetUpload] = useState<boolean>(false);
  const [priceType, setPriceType] = useState<string>('');

  const [defaultProductCategory, setDefaultProductCategory] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });

  const [defaultBusinessArea, setDefaultBusinessArea] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });

  const [defaultDistributionChannel, setDefaultDistributionChannel] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });
  const loanType = Form.useWatch('loanType', form);

  const { paymentHistoryData, productPrice, totalTransfered } = usePaymentHistory(
    dataDetailContract?.policyPayment?.schedule?.installments,
  );

  const { data: dataListInterestCalculations } = useFetch<TInterestCalculation>({
    queryKeyArr: ['list-interest-calculations', id],
    api: getListInterestCalculations,
    moreParams: { id },
    // withFilter: false,
    enabled: !!id,
  });

  const listInterestCalculations = dataListInterestCalculations?.data?.data?.rows as unknown as TInterestCalculation[];

  //kiểm tra quyền hợp đồng
  const { hasApproveContract } = useCheckPermissions(PERMISSION_CONTRACT);

  useEffect(() => {
    if (dataDetailContract?.staffsInvolved) {
      const newStaffList = dataDetailContract.staffsInvolved.map(item => ({
        ...((item ?? {}) as { id: string; percent: number }),
        key: uuidv4(),
      }));
      setStaffList(newStaffList);

      // Cập nhật giá trị biểu mẫu
      const staffFormValues = newStaffList.reduce(
        (acc, item) => ({
          ...acc,
          [item.key]: {
            employee: item?.id,
            percent: item?.percent,
          },
        }),
        {},
      );
      form.setFieldsValue({ staff: staffFormValues });
    }
  }, [dataDetailContract?.staffsInvolved, form]);

  useEffect(() => {
    let priceType = '';
    if (dataDetailContract) {
      setDetailDataTicket(dataDetailContract?.primaryTransaction);
      setIsPrimaryTransaction(true);
      setDefaultPaymentPolicy({
        label: dataDetailContract?.policyPayment?.name || undefined,
        value: dataDetailContract?.policyPayment?.id || undefined,
      });
      setDefaultSalePolicy({
        label: dataDetailContract?.salesPolicy?.name || undefined,
        value: dataDetailContract?.salesPolicy?.id || undefined,
      });
      setDefaultCustomer({
        label: dataDetailContract?.primaryTransaction?.customer2?.code,
        value: dataDetailContract?.primaryTransaction?.customer2?.id,
      });
      if (dataDetailContract?.calcContractPrice) {
        priceType = 'contract';
      } else {
        dataDetailContract?.calcPriceVat ? (priceType = 'vat') : (priceType = 'non-vat');
      }
      setPriceType(priceType);

      setFileList(dataDetailContract?.files);

      setMaintenanceFeeType(dataDetailContract?.maintenanceFee?.type);
      const defaultDiscount = dataDetailContract?.policyDiscounts.map(item => ({
        label: item?.name,
        value: item?.id,
        name: item?.name,
        option: item as { [key: string]: unknown },
      }));
      setDefaultDiscountPolicy(defaultDiscount);

      setPos(dataDetailContract?.primaryTransaction?.employee?.pos as TPos);

      setDefaultProductCategory({
        label: dataDetailContract?.productCategory?.name || undefined,
        value: dataDetailContract?.productCategory?.id || undefined,
      });

      setDefaultBusinessArea({
        label: dataDetailContract?.businessArea?.name || undefined,
        value: dataDetailContract?.businessArea?.id || undefined,
      });

      setDefaultDistributionChannel({
        label: dataDetailContract?.distributionChannel?.name || undefined,
        value: dataDetailContract?.distributionChannel?.id || undefined,
      });

      // const calculatedPrice = calculateProductPrice(dataDetailContract.primaryTransaction, priceType);
      const formatData = {
        ...dataDetailContract,
        companyInformation: {
          ...dataDetailContract?.companyInformation,
          dateOfIssue: dataDetailContract?.companyInformation?.dateOfIssue
            ? dayjs(dataDetailContract?.companyInformation?.dateOfIssue, FORMAT_DATE)
            : null,
        },
        info: {
          ...dataDetailContract?.primaryTransaction?.customer2?.info,
          birthday: dataDetailContract?.primaryTransaction?.customer2?.info?.birthday
            ? dayjs(dataDetailContract?.primaryTransaction?.customer2.info.birthday, FORMAT_DATE)
            : null,
          birthdayYear: dataDetailContract?.primaryTransaction?.customer2?.info?.birthdayYear
            ? dayjs(dataDetailContract?.primaryTransaction?.customer2.info.birthdayYear, 'YYYY')
            : null,
          onlyYear: dataDetailContract?.primaryTransaction?.customer2?.info?.onlyYear,
          address: {
            ...dataDetailContract?.primaryTransaction?.customer2?.info?.address,
          },
          rootAddress: {
            ...dataDetailContract?.primaryTransaction?.customer2?.info?.rootAddress,
          },
        },
        personalInfo: {
          ...dataDetailContract?.primaryTransaction?.customer2?.personalInfo,
          identities: dataDetailContract?.primaryTransaction?.customer2?.personalInfo?.identities?.map(
            (item: TIdentities) => {
              const getIdentityByValue = (value: string): DefaultOptionType => {
                return OPTIONS_IDENTITIES
                  ? OPTIONS_IDENTITIES.find(option => option.value === value) || { label: 'Unknown', value: '' }
                  : {};
              };

              const newTypeObject: DefaultOptionType = getIdentityByValue(item.type as string);
              return {
                ...item,
                typeObject: newTypeObject,
                date: item?.date ? dayjs(item?.date, FORMAT_DATE) : undefined,
              };
            },
          ),
        },
        address: {
          ...dataDetailContract?.primaryTransaction?.customer2?.info?.address,
        },
        rootAddress: {
          ...dataDetailContract?.primaryTransaction?.customer2?.info?.rootAddress,
        },
        customerSelect: {
          id: dataDetailContract?.primaryTransaction?.customer2?.id,
          code: dataDetailContract?.primaryTransaction?.customer2?.code,
        },
        primaryTransactionId: dataDetailContract?.primaryTransaction?.id,
        maintenanceFeeType: dataDetailContract?.maintenanceFee?.type,
        maintenanceFeeValue: dataDetailContract?.maintenanceFee?.value,
        calcCurrencyFirst: dataDetailContract?.calcCurrencyFirst,
        changeInstallment: dataDetailContract?.changeInstallment,
        nameContract: dataDetailContract?.name,
        codeContract: dataDetailContract?.code,
        oldContractCode: dataDetailContract?.oldContract?.code,
        priceType: priceType,
        policyPayment: dataDetailContract?.policyPayment?.id,
        policyDiscountIds: dataDetailContract?.policyDiscounts.map(item => item.id),
        productPrice: productPrice,
        projectName: dataDetailContract?.primaryTransaction?.project?.name || '',
        propertyUnitCode: dataDetailContract?.primaryTransaction?.propertyUnit?.code || '',
        price: dataDetailContract?.primaryTransaction?.propertyUnit?.price || 0,
        priceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.priceVat || 0,
        landPrice: dataDetailContract?.primaryTransaction?.propertyUnit?.landPrice || 0,
        landPriceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.landPriceVat || 0,
        housePrice: dataDetailContract?.primaryTransaction?.propertyUnit?.housePrice || 0,
        housePriceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.housePriceVat || 0,
        customerName: dataDetailContract?.primaryTransaction?.customer?.personalInfo?.name || '',
        posName: dataDetailContract?.primaryTransaction?.employee?.pos?.name || '',
        contractPriceForMaintenanceFee:
          dataDetailContract?.primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0,
        contractPrice: dataDetailContract?.primaryTransaction?.propertyUnit?.contractPrice || 0,
        contractDuration: [
          dataDetailContract?.startDate ? dayjs(dataDetailContract?.startDate) : undefined,
          dataDetailContract?.expiredDate ? dayjs(dataDetailContract?.expiredDate) : undefined,
        ],
        releaseDate: [
          dataDetailContract?.releaseStartDate ? dayjs(dataDetailContract?.releaseStartDate) : undefined,
          dataDetailContract?.releaseEndDate ? dayjs(dataDetailContract?.releaseEndDate) : undefined,
        ],
        signedDate: dayjs(dataDetailContract?.signedDate),
        isShowOwned: dataDetailContract?.primaryTransaction?.customer2?.name ? true : false,
        isShowCompanyInformation: dataDetailContract?.companyInformation ? true : false,
        loanType: dataDetailContract?.loanType || 'no',
        sapCode: dataDetailContract?.sapCode || '',
        poNumber: dataDetailContract?.poNumber || '',
        discountValue: dataDetailContract?.discountValue || '',
        issuedPrice: dataDetailContract?.issuedPrice || '',
        businessArea: dataDetailContract?.businessArea || null,
        distributionChannel: dataDetailContract?.distributionChannel || null,
        productCategory: dataDetailContract?.productCategory || null,
        loanBankCode: dataDetailContract?.loanBankInfo?.bankCode,
      } as unknown;
      form.setFieldsValue(formatData);
      setInitialValue(formatData as TTransferContractForm);
    }
  }, [dataDetailContract, form, priceType, productPrice, setDisabled, setInitialValue]);

  const handleMaintenanceFeeTypeChange = (value: string) => {
    setMaintenanceFeeType(value);
    form.setFieldsValue({ maintenanceFeeValue: 0 }); // Đặt maintenanceFeeValue về 0
  };

  useEffect(() => {
    if (resetUpload) {
      setFileList([]);
      setResetUpload?.(false);
    }
  }, [resetUpload, setResetUpload]);

  useEffect(() => {
    const installments = dataDetailContract?.policyPayment?.schedule?.installments;

    if (!installments) return;

    const options = installments.map(item => ({
      label: item.name,
      value: item.name,
    }));

    setInstallments(options);
  }, [dataDetailContract]);

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    navigate(TRANSFER_CONTRACT_MANAGEMENT);
  };

  const handleSelectSalePolicy = (value: TSalePolicy) => {
    form.setFieldsValue({ salesPolicy: value });
  };

  const handleSelectSDiscountPolicy = (values: DiscountPolicy[]) => {
    form.setFieldsValue({ policyDiscountIds: values?.map(item => item.id) });
  };

  const handleSelectPaymentPolicy = (value: IPaymentPolicy) => {
    let options = [];
    options = value?.schedule?.installments.map(item => ({
      label: item.name,
      value: item.name,
    }));
    setInstallments(options);
    form.setFieldsValue({ maintenanceFee: null });
    form.setFieldsValue({ policyPayment: value?.id, stagePayment: null });
  };

  // Action Lưu
  const handleFinish = async (values: TTransferContractForm) => {
    let calcPriceVat = false;
    let calcContractPrice = false;
    if (values?.priceType === 'contract') {
      calcContractPrice = true;
    } else {
      calcPriceVat = values?.priceType === 'vat';
    }

    const payload: TTransferContractPayload = {
      id: dataDetailContract?.id,
      files: fileList,
      primaryTransaction: dataDetailContract?.primaryTransaction,
      primaryTransactionId: values?.primaryTransactionId || '',
      policyPaymentId: values?.policyPayment?.id || '',
      policyDiscountIds: values?.policyDiscountIds || [],
      calcCurrencyFirst: values?.calcCurrencyFirst || false,
      calcPriceVat: calcPriceVat,
      calcContractPrice: calcContractPrice,
      maintenanceFee: {
        ...values?.maintenanceFee,
        type: values?.maintenanceFeeType || '',
        value: values?.maintenanceFeeValue || 0,
      },
      startDate: values?.contractDuration ? dayjs(values?.contractDuration[0]).format(FORMAT_DATE_API) : '',
      expiredDate: values?.contractDuration ? dayjs(values?.contractDuration[1]).format(FORMAT_DATE_API) : '',
      signedDate: values?.signedDate,
      transferType: values?.transferType,
      companyInformation: {
        ...values?.companyInformation,
        dateOfIssue:
          typeof values?.companyInformation?.dateOfIssue !== 'string'
            ? values?.companyInformation?.dateOfIssue?.format(FORMAT_DATE)
            : null,
      },
      customer2: {
        id: values?.customerSelect?.id,
        code: values?.customerSelect?.code,
        name: values?.personalInfo?.name,
        phone: values?.personalInfo?.phone,
        email: values?.personalInfo?.email,
        info: {
          ...values?.info,
          rootAddress: values?.rootAddress,
          address: values?.address,
          birthdayYear:
            typeof values?.info?.birthdayYear !== 'string' && values?.info?.birthdayYear
              ? values?.info?.birthdayYear?.format('YYYY')
              : null,
          birthday:
            typeof values?.info?.birthday !== 'string' && !values?.info?.birthdayYear
              ? values?.info?.birthday?.format(FORMAT_DATE)
              : null,
        },
        personalInfo: {
          ...values?.personalInfo,
          identities: values?.personalInfo?.identities?.map(item => ({
            ...item,
            type: typeof item?.typeObject !== 'string' ? item?.typeObject?.value : item?.type,
            date: typeof item?.date !== 'string' ? item?.date?.format(FORMAT_DATE) : null,
          })),
        },
      },
      isDebtRemind: values?.isDebtRemind,
      changeInstallment: values?.changeInstallment,
      releaseStartDate: values?.releaseDate ? dayjs(values?.releaseDate[0]).format(FORMAT_DATE_API) : '',
      releaseEndDate: values?.releaseDate ? dayjs(values?.releaseDate[1]).format(FORMAT_DATE_API) : '',
      salesPolicy: {
        id: values?.salesPolicy?.id || '',
        name: values?.salesPolicy?.name || '',
        code: values?.salesPolicy?.code || '',
      },
      staffsInvolved: staffList
        .filter(item => item.id && item.percent !== undefined)
        .map(item => ({
          id: item.id as string,
          code: item.code || '',
          name: item.name || '',
          percent: item.percent || 0,
        })),
      businessArea: {
        id: values?.businessArea?.id || '',
        name: values?.businessArea?.nameVN || '',
        code: values?.businessArea?.code || '',
      },
      distributionChannel: {
        id: values?.distributionChannel?.id || '',
        name: values?.distributionChannel?.name || '',
        code: values?.distributionChannel?.code || '',
      },
      productCategory: {
        id: values?.productCategory?.id || '',
        name: values?.productCategory?.name || '',
        code: values?.productCategory?.code || '',
      },
      sapCode: values?.sapCode || '',
      poNumber: values?.poNumber || '',
      discountValue: values?.discountValue || '',
      issuedPrice: values?.issuedPrice || '',
    };
    try {
      console.log('payload', payload);
      // await mutateAsync(payload);
    } catch (error) {
      console.error('Error creating commission policy:', error);
    }
  };

  return (
    <div className="detail-transfer">
      <Spin spinning={isLoading}>
        <BreadCrumbComponent titleBread={dataDetailContract?.name} />
        <HeaderContract
          dataDetailContract={dataDetailContract}
          keyOfDetailQuery={['detail-transfer-contract', dataDetailContract?.id]}
          type={'HD_COC'}
          isCreatePurchaseContract={true}
        />
        <>
          <Form
            className="form-detail"
            form={form}
            initialValues={initialValue}
            onFinish={handleFinish}
            layout="vertical"
          >
            <Row gutter={64}>
              <Col span={12}>
                <Title level={5} style={{ marginBottom: 16 }}>
                  Thông tin hợp đồng
                </Title>
                <Row gutter={24}>
                  <Col span={14}>
                    <Form.Item name="nameContract" label="Tên hợp đồng">
                      <Input placeholder="Tên hợp đồng" disabled />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item name="codeContract" label="Mã hợp đồng">
                      <Input placeholder="Mã hợp đồng" disabled />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={14}>
                    <Form.Item label="Mã hợp đồng cũ" name="oldContractCode">
                      <Input placeholder="Tên khách hàng" disabled />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item name="sapCode" label="Mã hợp đồng SAP">
                      <Input placeholder="" disabled />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Tên khách hàng" name="customerName">
                      <Input placeholder="Tên khách hàng" disabled />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label="Số hợp đồng giấy"
                      name="poNumber"
                      rules={[{ required: true, message: 'Vui lòng nhập số hợp đồng giấy' }]}
                    >
                      <Input placeholder="Số hợp đồng giấy" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="Mã công ty/ chi nhánh"
                      name="businessArea"
                      rules={[{ required: true, message: 'Vui lòng chọn mã công ty/ chi nhánh' }]}
                    >
                      <SingleSelectLazy
                        queryKey={['get-business-area']}
                        apiQuery={getListBusinessArea}
                        placeholder="Chọn mã công ty/ chi nhánh"
                        keysLabel={['code', 'nameVN']}
                        handleSelect={value => {
                          form.setFieldsValue({ businessArea: value });
                        }}
                        defaultValues={defaultBusinessArea}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label="Kênh phân phối"
                      name="distributionChannel"
                      rules={[{ required: true, message: 'Vui lòng chọn kênh phân phối' }]}
                    >
                      <SingleSelectLazy
                        queryKey={['get-distribution-channel']}
                        apiQuery={getListDistributionChannel}
                        placeholder="Chọn kênh phân phối"
                        keysLabel={['name']}
                        handleSelect={value => {
                          form.setFieldsValue({ distributionChannel: value });
                        }}
                        defaultValues={defaultDistributionChannel}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="Ngành hàng"
                      name="productCategory"
                      rules={[{ required: true, message: 'Vui lòng chọn ngành hàng' }]}
                    >
                      <SingleSelectLazy
                        queryKey={['get-division']}
                        apiQuery={getDivision}
                        placeholder="Chọn ngành hàng"
                        keysLabel={['name']}
                        handleSelect={value => {
                          form.setFieldsValue({ productCategory: value });
                        }}
                        defaultValues={defaultProductCategory}
                      />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      label="Ngày hiệu lực"
                      name="startDate"
                      rules={[{ required: true, message: 'Vui lòng chọn ngày hiệu lực' }]}
                    >
                      <DatePicker disabled placeholder="" format="DD/MM/YYYY" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item label="Ngày kết thúc hiệu lực" name="expiredDate">
                      <DatePicker disabled placeholder="" format="DD/MM/YYYY" />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item label="Ngày phát hành" name="releaseStartDate">
                      <DatePicker placeholder="Chọn ngày phát hành" format="DD/MM/YYYY" />
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      label="Ngày kết thúc"
                      name="releaseEndDate"
                      dependencies={['releaseStartDate']}
                      rules={[
                        {
                          validator: validateEndDate(
                            form,
                            'startDate',
                            'Ngày kết thúc lớn hơn hoặc bằng ngày phát hành',
                          ),
                        },
                      ]}
                    >
                      <DatePicker placeholder="Chọn ngày kết thúc" format="DD/MM/YYYY" />
                    </Form.Item>
                  </Col>
                </Row>

                <Form.Item
                  label="Ngày ký kết"
                  name="signedDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày ký kết' }]}
                >
                  <DatePicker placeholder="Chọn ngày ký kết" format="DD/MM/YYYY" />
                </Form.Item>

                <Form.Item label="Hình thức thanh toán" name="transferType">
                  <Select placeholder="Chọn hình thức thanh toán" allowClear options={LISTPAYMENTS} />
                </Form.Item>

                <Form.Item name="isDebtRemind" valuePropName="checked">
                  <Checkbox>Nhắc nợ</Checkbox>
                </Form.Item>
                <Title level={5} style={{ marginBottom: 16 }}>
                  Thông tin đồng sở hữu
                </Title>
                <Form.Item name="isShowOwned" valuePropName="checked">
                  <Checkbox disabled>Thêm thông tin đồng sở hữu</Checkbox>
                </Form.Item>
                {isShowOwned && <CustomerForm disabledForm={true} defaultCustomer={defaultCustomer} form={form} />}
                <Title level={5} style={{ marginBottom: 16 }}>
                  Thông tin công ty
                </Title>
                <Form.Item name="isShowCompanyInformation" valuePropName="checked">
                  <Checkbox disabled>Thêm thông tin công ty</Checkbox>
                </Form.Item>
                {isShowCompanyInformation && <CompanyInformationForm disabledForm={true} form={form} />}

                <Title level={5} style={{ marginBottom: 16 }}>
                  Thông tin bán hàng
                </Title>
                <Form.Item label="Đơn vị bán hàng" name="posName">
                  <Input placeholder="Đơn vị sở hữu" disabled />
                </Form.Item>

                <Form.Item
                  label="Chính sách phí - hoa hồng"
                  required
                  name="salesPolicy"
                  rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
                >
                  <SingleSelectLazy
                    apiQuery={getListSalesPolicy}
                    queryKey={['sales-policy']}
                    keysLabel={['code', 'name']}
                    placeholder="Chọn chính sách phí - hoa hồng"
                    handleSelect={handleSelectSalePolicy}
                    moreParams={{ projectID: detailDataTicket?.project?.id, isActive: true }}
                    disabled={!isPrimaryTransaction}
                    defaultValues={defaultSalePolicy}
                  />
                </Form.Item>

                <Title level={5} style={{ marginBottom: 16 }}>
                  Thông tin đính kèm
                </Title>

                <Form.Item name="files">
                  <CommonFileUpload
                    fileList={fileList}
                    setFileList={setFileList}
                    uploadPath={'contract-transfer'}
                    // allowedMimeTypes={allowedMimeTypes}
                    // allowedExtensions={ALLOWED_EXTENSIONS}
                    maxFileSize={10}
                    maxTotalSize={1}
                    maxFiles={10}
                    buttonText="Upload"
                    buttonWidth="100px"
                  />
                </Form.Item>
                <Title level={5}>Danh sách nhân viên tham gia bán hàng</Title>
                <Row gutter={{ md: 24, lg: 40 }}>
                  <Col xs={24} md={24}>
                    <StaffListTable
                      staffList={staffList}
                      onStaffListChange={setStaffList}
                      isPrimaryTransaction={isPrimaryTransaction}
                      detailDataTicket={detailDataTicket}
                      pos={pos}
                      form={form}
                    />
                  </Col>
                </Row>
                <Title level={5} style={{ marginBottom: 16, marginTop: 16 }}>
                  Thông tin vay
                </Title>
                <Form.Item style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
                  <span style={{ minWidth: 120 }}>Vay ngân hàng</span>
                  <Form.Item name="loanType" noStyle>
                    <Radio.Group style={{ marginLeft: 16 }}>
                      <Radio value="yes">Có</Radio>
                      <Radio value="no">Không</Radio>
                    </Radio.Group>
                  </Form.Item>
                </Form.Item>
                {loanType === 'yes' && <LoanInfoForm form={form} />}
              </Col>
              <Col span={12}>
                <Title level={5} style={{ marginBottom: 16 }}>
                  Thông tin dự án
                </Title>
                <Row gutter={24}>
                  <Col span={14}>
                    <Form.Item label="Tên dự án" name="projectName">
                      <Input placeholder="Tên dự án" disabled />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item label="Mã sản phẩm" name="propertyUnitCode">
                      <Input placeholder="Mã sản phẩm" disabled />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Giá trị sản phẩm trên hợp đồng" name="productPrice">
                      <InputNumber placeholder="Giá sản phẩm" disabled formatter={formatNumber} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={14}>
                    <Form.Item label="Giá bán chưa VAT" name="price">
                      <InputNumber placeholder="Giá bán chưa VAT" disabled formatter={formatNumber} />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item label="Giá bán có VAT" name="priceVat">
                      <InputNumber placeholder="Giá bán có VAT" disabled formatter={formatNumber} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={14}>
                    <Form.Item label="Giá nhà chưa VAT" name="housePrice">
                      <InputNumber placeholder="Giá nhà chưa VAT" disabled formatter={formatNumber} />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item label="Giá nhà có VAT" name="housePriceVat">
                      <InputNumber placeholder="Giá nhà có VAT" disabled formatter={formatNumber} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={14}>
                    <Form.Item label="Giá đất chưa VAT" name="landPrice">
                      <InputNumber placeholder="Giá đất chưa VAT" disabled formatter={formatNumber} />
                    </Form.Item>
                  </Col>
                  <Col span={10}>
                    <Form.Item label="Giá đất có VAT" name="landPriceVat">
                      <InputNumber placeholder="Giá đất có VAT" disabled formatter={formatNumber} />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Tổng giá trị CH sau CK gồm VAT, chưa bao gồm PBT" name="contractPrice">
                      <Input placeholder="Giá trị CH sau CK gồm VAT, chưa bao gồm PBT" disabled />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Giá trị CH tính phí bảo trì" name="contractPriceForMaintenanceFee">
                      <Input placeholder="Giá trị CH tính phí bảo trì" disabled />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Giá trị chiết khấu" name="discountValue">
                      <Input placeholder="" disabled />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Giá ban hành" name="issuedPrice">
                      <Input placeholder="" disabled />
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={24}>
                    <div style={{ display: 'flex', marginBottom: 16 }}>
                      <div style={{ width: 120 }}>Tính giá trị:</div>
                      <Form.Item name="priceType" style={{ marginBottom: 0 }}>
                        <Radio.Group>
                          <Space direction="vertical">
                            <Radio value="vat">Giá có VAT</Radio>
                            <Radio value="non-vat">Giá không có VAT</Radio>
                            <Radio
                              disabled={
                                form.getFieldValue('contractPrice') ? form.getFieldValue('contractPrice') <= 0 : true
                              }
                              value="contract"
                            >
                              Tổng giá trị CH sau CK gồm VAT, chưa gồm PBT
                            </Radio>
                          </Space>
                        </Radio.Group>
                      </Form.Item>
                    </div>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item name="changeInstallment" valuePropName="checked">
                      <Checkbox>Không cho phép thay đổi đợt thanh toán</Checkbox>
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item
                      label="Chính sách thanh toán"
                      required
                      name="policyPayment"
                      rules={[{ required: true, message: 'Vui lòng chọn chính sách thanh toán' }]}
                    >
                      <SingleSelectLazy
                        disabled={!isPrimaryTransaction || changeInstallment}
                        apiQuery={getListPaymentPolicy}
                        queryKey={['payment-policy']}
                        keysLabel={['code', 'name']}
                        placeholder="Chọn chính sách thanh toán"
                        handleSelect={handleSelectPaymentPolicy}
                        defaultValues={defaultPaymentPolicy}
                        moreParams={{ active: true, projectIds: detailDataTicket?.project?.id }}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item name="calcCurrencyFirst" valuePropName="checked">
                      <Checkbox>Tính chiết khấu tiền mặt trước</Checkbox>
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Chính sách chiết khấu" name="policyDiscountIds">
                      <MultiSelectLazy
                        apiQuery={getListDiscountPolicy}
                        queryKey={['discount-policy']}
                        keysLabel={['name']}
                        keysTag={'name'}
                        placeholder="Chọn chính sách chiết khấu"
                        disabled={!isPrimaryTransaction}
                        handleListSelect={handleSelectSDiscountPolicy}
                        defaultValues={
                          defaultDiscountPolicy as {
                            label: string | JSX.Element;
                            value: string;
                            [key: string]: string | JSX.Element;
                          }[]
                        }
                        moreParams={{ active: true, projectIds: detailDataTicket?.project?.id }}
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Phí bảo trì" name="maintenanceFeeValue">
                      <InputNumber
                        maxLength={maintenanceFeeType === 'percent' ? 5 : 15}
                        style={{ width: '100%' }}
                        placeholder="Nhập giá trị chiết khấu"
                        min={0}
                        max={maintenanceFeeType === 'percent' ? 100 : undefined}
                        step={maintenanceFeeType === 'percent' ? 0.01 : 1}
                        precision={2}
                        formatter={(value: number | string | undefined) =>
                          maintenanceFeeType === 'currency' && value !== undefined
                            ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                            : `${value}`
                        }
                        parser={(value: string | undefined) =>
                          maintenanceFeeType === 'currency' && value ? parseFloat(value.replace(/,/g, '')) : value || ''
                        }
                        addonAfter={
                          <Form.Item name="maintenanceFeeType" noStyle>
                            <Select style={{ width: 91 }} onChange={handleMaintenanceFeeTypeChange}>
                              <Option value="percent">%</Option>
                              <Option value="currency">VND</Option>
                            </Select>
                          </Form.Item>
                        }
                      />
                    </Form.Item>
                  </Col>
                </Row>

                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item label="Đợt thu phí bảo trì" name={['maintenanceFee', 'stagePayment']}>
                      <Select placeholder="Chọn đợt thu phí" options={installments} />
                    </Form.Item>
                  </Col>
                </Row>
              </Col>
            </Row>
            <Title level={5} style={{ marginTop: 16, marginBottom: 16 }}>
              Thông tin thanh toán
            </Title>
            <Space className="payment-info-radio-group">
              <Radio.Group
                value={tabSelect}
                onChange={e => setTabSelect(e.target.value)}
                optionType="button"
                className="rounded-radio-group"
              >
                <Radio.Button value={TabType.TAB1}>Lịch sử thanh toán</Radio.Button>
                <Radio.Button value={TabType.TAB2}>Danh sách phiếu tính lãi</Radio.Button>
                <Radio.Button value={TabType.TAB3}>Lịch sử chuyển nhượng</Radio.Button>
              </Radio.Group>
            </Space>
            {tabSelect === TabType.TAB1 && (
              <div style={{ marginBottom: 60 }} className="mb-3">
                <PaymentHistory
                  dataPaymentHistory={paymentHistoryData}
                  productPrice={productPrice}
                  totalTransfered={totalTransfered}
                />
              </div>
            )}
            {tabSelect === TabType.TAB2 && (
              <div style={{ marginBottom: 60 }} className="mb-3">
                <InterestCalculationList interestCalculationList={listInterestCalculations} />
              </div>
            )}
          </Form>
        </>
        {hasApproveContract && (dataDetailContract?.status === 'init' || dataDetailContract?.status === 'rejected') && (
          <>
            <ButtonOfPageDetail
              handleSubmit={() => form.submit()}
              handleCancel={handleCancel}
              // loadingSubmit={isPending}
            />
          </>
        )}
      </Spin>
    </div>
  );
};

export default DetailTransferContract;

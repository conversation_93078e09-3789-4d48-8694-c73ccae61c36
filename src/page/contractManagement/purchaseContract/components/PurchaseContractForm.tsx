import React, { useEffect, useState } from 'react';
import {
  Form,
  Input,
  Button,
  Row,
  Col,
  Typography,
  FormInstance,
  Select,
  Spin,
  DatePicker,
  Checkbox,
  InputNumber,
  Table,
  Radio,
} from 'antd';
import { v4 as uuidv4 } from 'uuid';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import { BookingTicket } from '../../../../types/bookingRequest';
import { formatNumber } from '../../../../utilities/regex';
import dayjs from 'dayjs';
import customParseFormat from 'dayjs/plugin/customParseFormat';
import advancedFormat from 'dayjs/plugin/advancedFormat';
import localeData from 'dayjs/plugin/localeData';
import weekday from 'dayjs/plugin/weekday';
import weekOfYear from 'dayjs/plugin/weekOfYear';
import weekYear from 'dayjs/plugin/weekYear';

dayjs.extend(customParseFormat);
dayjs.extend(advancedFormat);
dayjs.extend(weekday);
dayjs.extend(localeData);
dayjs.extend(weekOfYear);
dayjs.extend(weekYear);
import { TPos, TSalePolicy } from '../../../../types/commission';
import CommonFileUpload from '../../../../components/upload/CommonUploadFiles';
import {
  ExtendedUploadFile,
  TDepositContractPayload,
  TDepositContractForm,
  TDepositContract,
  PolicyDiscounts,
  StaffItem,
  StaffFormValues,
  TInterestCalculation,
} from '../../../../types/contract/depositContract';
import {
  getListDiscountPolicy,
  getListPaymentPolicy,
  getListSalesPolicy,
  getListEmployeeByOrgchartId,
  getListDepositContractSelect,
  getDetailDepositContractSelect,
  getListInterestCalculations,
  getListDistributionChannel,
  getDivision,
  getListBusinessArea,
} from '../../../../service/contract';
import { IPaymentPolicy } from '../../../../types/paymentPolicy';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import './styles.scss';
import { OptionTypeSelect } from '../../../../types/common/common';

import { Space } from 'antd/lib';
import PaymentHistory from './../../components/PaymentHistory';
import InterestCalculationList from './../../components/InterestCalculationList';
import CustomerForm from '../../components/CustomerForm';
import { TIdentities } from '../../../../types/customers';
import { DefaultOptionType } from 'antd/es/select';
import { FORMAT_DATE, OPTIONS_IDENTITIES } from '../../../../constants/common';
import { calculateProductPrice } from '../../utils/calculateProductPrice';
import { usePaymentHistory } from '../../hooks/usePaymentHistory';
import { LISTPAYMENTS, TabType } from '../../../../constants/contract';
import { validateEndDate } from '../../../../utilities/shareFunc';
import { useFetch } from '../../../../hooks';
import LoanInfoForm from '../../components/LoanInfoForm';

const { Title } = Typography;
const { Option } = Select;

interface PurchaseContractFromProps {
  form?: FormInstance;
  onFinish?: (values: TDepositContractPayload) => void;
  resetUpload?: boolean;
  setResetUpload?: (value: boolean) => void;
  loading: boolean;
  initialValues?: TDepositContract;
}

// Định nghĩa interface cho staff item
const PurchaseContractFrom: React.FC<PurchaseContractFromProps> = ({
  form: parentForm,
  onFinish,
  setResetUpload,
  resetUpload,
  loading,
  initialValues: dataDetailContract = null,
}) => {
  const [internalForm] = Form.useForm();
  const form = parentForm || internalForm;
  const [defaultCustomer, setDefaultCustomer] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [installments, setInstallments] = useState<{ label: string; value: string }[]>([]);
  const [staffList, setStaffList] = useState<StaffItem[]>([]);
  const [detailDataTicket, setDetailDataTicket] = useState<TDepositContract>({} as TDepositContract);
  const [pos, setPos] = useState<TPos>({ id: '', code: '', name: '' });
  const [maintenanceFeeType, setMaintenanceFeeType] = useState<string>('percent'); // Loại chiết khấu
  const [isPrimaryTransaction, setIsPrimaryTransaction] = useState<boolean>(false);
  const [defaultDiscountPolicy, setDefaultDiscountPolicy] = useState<OptionTypeSelect[]>([]);
  const [tabSelect, setTabSelect] = useState('TAB1');
  const [defaultPaymentPolicy, setDefaultPaymentPolicy] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });
  const [defaultDepositContract, setDefaultDepositContract] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });

  const [defaultSalePolicy, setDefaultSalePolicy] = useState<OptionTypeSelect>({ value: undefined, label: undefined });

  const isShowOwned = Form.useWatch('isShowOwned', form);
  const changeInstallment = Form.useWatch('changeInstallment', form);

  const { paymentHistoryData, productPrice, totalTransfered } = usePaymentHistory(
    detailDataTicket?.policyPayment?.schedule?.installments,
  );

  const [defaultProductCategory, setDefaultProductCategory] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });

  const [defaultBusinessArea, setDefaultBusinessArea] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });

  const [defaultDistributionChannel, setDefaultDistributionChannel] = useState<OptionTypeSelect>({
    value: undefined,
    label: undefined,
  });
  const loanType = Form.useWatch('loanType', form);

  const { data: dataListInterestCalculations } = useFetch<TInterestCalculation>({
    queryKeyArr: ['list-interest-calculations', dataDetailContract?.id],
    api: getListInterestCalculations,
    moreParams: { id: dataDetailContract?.id },
    // withFilter: false,
    enabled: !!dataDetailContract?.id,
  });

  const listInterestCalculations = dataListInterestCalculations?.data?.data?.rows as unknown as TInterestCalculation[];

  const handleMaintenanceFeeTypeChange = (value: string) => {
    setMaintenanceFeeType(value);
    form.setFieldsValue({ maintenanceFeeValue: 0 }); // Đặt maintenanceFeeValue về 0
  };

  // const handlePriceTypeChange = (e: RadioChangeEvent) => {
  //   const priceType = e.target.value;
  //   const calculatedPrice = calculateProductPrice(detailDataTicket?.primaryTransaction, priceType);
  //   form.setFieldsValue({
  //     productPrice: calculatedPrice,
  //   });
  // };

  useEffect(() => {
    if (dataDetailContract) {
      setDetailDataTicket(dataDetailContract);
      setIsPrimaryTransaction(true);
      setDefaultDepositContract({
        label: dataDetailContract?.name,
        value: dataDetailContract?.id,
      });
      setDefaultPaymentPolicy({
        label: dataDetailContract?.policyPayment?.name || undefined,
        value: dataDetailContract?.policyPayment?.id || undefined,
      });
      setDefaultSalePolicy({
        label: dataDetailContract?.salesPolicy?.name || undefined,
        value: dataDetailContract?.salesPolicy?.id || undefined,
      });
      let priceType = '';
      if (dataDetailContract?.calcContractPrice) {
        priceType = 'contract';
      } else {
        dataDetailContract?.calcPriceVat ? (priceType = 'vat') : (priceType = 'non-vat');
      }
      setFileList(dataDetailContract?.files);
      setMaintenanceFeeType(dataDetailContract?.maintenanceFee?.type);
      const defaultDiscount = dataDetailContract?.policyDiscounts.map((item: PolicyDiscounts) => ({
        id: item?.id,
        label: item?.name,
        value: item?.id,
        name: item?.name,
        option: item as unknown as { [key: string]: unknown },
      }));
      setDefaultDiscountPolicy(defaultDiscount);

      setPos(dataDetailContract?.primaryTransaction?.employee?.pos as TPos);

      if (dataDetailContract?.staffsInvolved) {
        const newStaffList: StaffItem[] = dataDetailContract.staffsInvolved.map((item: StaffItem) => ({
          ...item,
          key: uuidv4(),
        }));
        setStaffList(newStaffList);

        // Cập nhật giá trị biểu mẫu
        const staffFormValues = newStaffList.reduce<StaffFormValues>(
          (acc, item) => ({
            ...acc,
            [item.key]: {
              employee: item.id ?? '', // Xử lý id undefined
              percent: item.percent ?? 0, // Xử lý percent undefined
            },
          }),
          {},
        );
        form.setFieldsValue({ staff: staffFormValues });
      }
      setDefaultCustomer({
        label: dataDetailContract?.primaryTransaction?.customer2?.code,
        value: dataDetailContract?.primaryTransaction?.customer2?.id,
      });

      setDefaultProductCategory({
        label: dataDetailContract?.productCategory?.name || undefined,
        value: dataDetailContract?.productCategory?.id || undefined,
      });

      setDefaultBusinessArea({
        label: dataDetailContract?.businessArea?.name || undefined,
        value: dataDetailContract?.businessArea?.id || undefined,
      });

      setDefaultDistributionChannel({
        label: dataDetailContract?.distributionChannel?.name || undefined,
        value: dataDetailContract?.distributionChannel?.id || undefined,
      });
      // const calculatedPrice = calculateProductPrice(dataDetailContract.primaryTransaction, priceType);
      const formatData = {
        ...dataDetailContract,
        contractId: dataDetailContract.id,
        info: {
          ...dataDetailContract?.primaryTransaction?.customer2?.info,
          birthday: dataDetailContract?.primaryTransaction?.customer2?.info?.birthday
            ? dayjs(dataDetailContract?.primaryTransaction?.customer2.info.birthday, FORMAT_DATE)
            : null,
          birthdayYear: dataDetailContract?.primaryTransaction?.customer2?.info?.birthdayYear
            ? dayjs(dataDetailContract?.primaryTransaction?.customer2.info.birthdayYear, 'YYYY')
            : null,
          onlyYear: dataDetailContract?.primaryTransaction?.customer2?.info?.onlyYear,
          address: {
            ...dataDetailContract?.primaryTransaction?.customer2?.info?.address,
          },
          rootAddress: {
            ...dataDetailContract?.primaryTransaction?.customer2?.info?.rootAddress,
          },
        },
        personalInfo: {
          ...dataDetailContract?.primaryTransaction?.customer2?.personalInfo,
          identities: dataDetailContract?.primaryTransaction?.customer2?.personalInfo?.identities?.map(
            (item: TIdentities) => {
              const getIdentityByValue = (value: string): DefaultOptionType => {
                return OPTIONS_IDENTITIES
                  ? OPTIONS_IDENTITIES.find(option => option.value === value) || { label: 'Unknown', value: '' }
                  : {};
              };

              const newTypeObject: DefaultOptionType = getIdentityByValue(item.type as string);
              return {
                ...item,
                typeObject: newTypeObject,
                date: item?.date ? dayjs(item?.date, FORMAT_DATE) : undefined,
              };
            },
          ),
        },
        address: {
          ...dataDetailContract?.primaryTransaction?.customer2?.info?.address,
        },
        rootAddress: {
          ...dataDetailContract?.primaryTransaction?.customer2?.info?.rootAddress,
        },
        customerSelect: {
          id: dataDetailContract?.primaryTransaction?.customer2?.id,
          code: dataDetailContract?.primaryTransaction?.customer2?.code,
        },
        primaryTransactionId: dataDetailContract?.primaryTransaction?.id,
        maintenanceFeeType: dataDetailContract?.maintenanceFee?.type,
        maintenanceFeeValue: dataDetailContract?.maintenanceFee?.value,
        calcCurrencyFirst: dataDetailContract?.calcCurrencyFirst,
        changeInstallment: dataDetailContract?.changeInstallment,
        nameContract: dataDetailContract?.name,
        codeContract: dataDetailContract?.code,
        priceType: priceType,
        policyPayment: dataDetailContract?.policyPayment?.id,
        policyDiscountIds: dataDetailContract?.policyDiscounts.map((item: PolicyDiscounts) => item.id),
        productPrice: productPrice,
        projectName: dataDetailContract?.primaryTransaction?.project?.name || '',
        propertyUnitCode: dataDetailContract?.primaryTransaction?.propertyUnit?.code || '',
        price: dataDetailContract?.primaryTransaction?.propertyUnit?.price || 0,
        priceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.priceVat || 0,
        landPrice: dataDetailContract?.primaryTransaction?.propertyUnit?.landPrice || 0,
        landPriceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.landPriceVat || 0,
        housePrice: dataDetailContract?.primaryTransaction?.propertyUnit?.housePrice || 0,
        housePriceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.housePriceVat || 0,
        customerName: dataDetailContract?.primaryTransaction?.customer?.personalInfo?.name || '',
        posName: dataDetailContract?.primaryTransaction?.employee?.pos?.name || '',
        contractPriceForMaintenanceFee:
          dataDetailContract?.primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0,
        contractPrice: dataDetailContract?.primaryTransaction?.propertyUnit?.contractPrice || 0,
        startDate: dataDetailContract?.startDate ? dayjs(dataDetailContract?.startDate) : undefined,
        expiredDate: dataDetailContract?.expiredDate ? dayjs(dataDetailContract?.expiredDate) : undefined,
        releaseStartDate: dataDetailContract?.releaseStartDate
          ? dayjs(dataDetailContract?.releaseStartDate)
          : undefined,
        releaseEndDate: dataDetailContract?.releaseEndDate ? dayjs(dataDetailContract?.releaseEndDate) : undefined,
        signedDate: dataDetailContract?.signedDate ? dayjs(dataDetailContract?.signedDate) : undefined,
        isShowOwned: dataDetailContract?.primaryTransaction?.customer2?.name ? true : false,
        loanType: dataDetailContract?.loanType || 'no',
        sapCode: dataDetailContract?.sapCode || '',
        poNumber: dataDetailContract?.poNumber || '',
        discountValue: dataDetailContract?.discountValue || '',
        issuedPrice: dataDetailContract?.issuedPrice || '',
        businessArea: dataDetailContract?.businessArea || null,
        distributionChannel: dataDetailContract?.distributionChannel || null,
        productCategory: dataDetailContract?.productCategory || null,
        loanBankCode: dataDetailContract?.loanBankInfo?.bankCode,
      } as unknown;
      form.setFieldsValue(formatData);
    }
  }, [dataDetailContract, form, productPrice]);

  useEffect(() => {
    if (detailDataTicket) {
      const installments = detailDataTicket?.policyPayment?.schedule?.installments;

      if (!installments) return;

      const options = installments.map(item => ({
        label: item.name,
        value: item.name,
      }));

      setInstallments(options);
    }
  }, [detailDataTicket]);

  useEffect(() => {
    if (resetUpload) {
      setFileList([]);
      setResetUpload?.(false);
    }
  }, [resetUpload, setResetUpload]);

  const handleSelectSalePolicy = (value: TSalePolicy) => {
    form.setFieldsValue({ salesPolicy: value });
  };

  const handleSelectSDiscountPolicy = (values: PolicyDiscounts[]) => {
    form.setFieldsValue({ policyDiscountIds: values?.map(item => item?.id) });
  };

  const handleSelectPaymentPolicy = (value: IPaymentPolicy) => {
    const options = value?.schedule?.installments.map(item => ({
      label: item.name,
      value: item.name,
    }));
    setInstallments(options);
    form.setFieldsValue({ policyPayment: value, stagePaymentSelect: null });
  };

  const handleSelectDepositContract = async (value: BookingTicket) => {
    setInstallments([]);
    setStaffList([]);
    setDefaultDiscountPolicy([]);
    setDefaultPaymentPolicy({ value: undefined, label: undefined });
    setDefaultSalePolicy({ value: undefined, label: undefined });
    setDefaultBusinessArea({ value: undefined, label: undefined });
    setDefaultDistributionChannel({ value: undefined, label: undefined });
    setDefaultProductCategory({ value: undefined, label: undefined });
    setDetailDataTicket({} as TDepositContract);
    if (!value) {
      form.resetFields();
      setIsPrimaryTransaction(false);
    } else {
      setIsPrimaryTransaction(true);
      // Gọi API DepositContract để lấy thông tin chi tiết
      try {
        const response = await getDetailDepositContractSelect({ id: value.id });
        const dataDetailContract = response?.data?.data;
        setDetailDataTicket(dataDetailContract);
        setIsPrimaryTransaction(true);
        setDefaultDepositContract({
          label: dataDetailContract?.deposit?.name,
          value: dataDetailContract?.deposit?.id,
        });
        setDefaultPaymentPolicy({
          label: dataDetailContract?.policyPayment?.name || undefined,
          value: dataDetailContract?.policyPayment?.id || undefined,
        });
        setDefaultSalePolicy({
          label: dataDetailContract?.salesPolicy?.name || undefined,
          value: dataDetailContract?.salesPolicy?.id || undefined,
        });
        let priceType = '';
        if (dataDetailContract?.calcContractPrice) {
          priceType = 'contract';
        } else {
          dataDetailContract?.calcPriceVat ? (priceType = 'vat') : (priceType = 'non-vat');
        }
        setFileList(dataDetailContract?.files);
        setMaintenanceFeeType(dataDetailContract?.maintenanceFee?.type);
        const defaultDiscount = dataDetailContract?.policyDiscounts.map((item: PolicyDiscounts) => ({
          id: item?.id,
          label: item?.name,
          value: item?.id,
          name: item?.name,
          option: item as unknown as { [key: string]: unknown },
        }));
        setDefaultDiscountPolicy(defaultDiscount);

        setPos(dataDetailContract?.primaryTransaction?.employee?.pos as TPos);

        if (dataDetailContract?.staffsInvolved) {
          const newStaffList = dataDetailContract.staffsInvolved.map((item: StaffItem) => ({
            ...((item ?? {}) as { id: string; percent: number }),
            key: uuidv4(),
          }));
          setStaffList(newStaffList);

          // Cập nhật giá trị biểu mẫu
          const staffFormValues = newStaffList.reduce(
            (acc: StaffItem, item: StaffItem) => ({
              ...acc,
              [item.key]: {
                employee: item?.id,
                percent: item?.percent,
              },
            }),
            {},
          );
          form.setFieldsValue({ staff: staffFormValues });
        }
        setDefaultCustomer({
          label: dataDetailContract?.primaryTransaction?.customer2?.code,
          value: dataDetailContract?.primaryTransaction?.customer2?.id,
        });
        const calculatedPrice = calculateProductPrice(dataDetailContract.primaryTransaction, priceType);

        setDefaultProductCategory({
          label: dataDetailContract?.productCategory?.name || undefined,
          value: dataDetailContract?.productCategory?.id || undefined,
        });

        setDefaultBusinessArea({
          label: dataDetailContract?.businessArea?.name || undefined,
          value: dataDetailContract?.businessArea?.id || undefined,
        });

        setDefaultDistributionChannel({
          label: dataDetailContract?.distributionChannel?.name || undefined,
          value: dataDetailContract?.distributionChannel?.id || undefined,
        });
        const formatData = {
          ...dataDetailContract,
          info: {
            ...dataDetailContract?.primaryTransaction?.customer2?.info,
            birthday: dataDetailContract?.primaryTransaction?.customer2?.info?.birthday
              ? dayjs(dataDetailContract?.primaryTransaction?.customer2.info.birthday, FORMAT_DATE)
              : null,
            birthdayYear: dataDetailContract?.primaryTransaction?.customer2?.info?.birthdayYear
              ? dayjs(dataDetailContract?.primaryTransaction?.customer2.info.birthdayYear, 'YYYY')
              : null,
            onlyYear: dataDetailContract?.primaryTransaction?.customer2?.info?.onlyYear,
            address: {
              ...dataDetailContract?.primaryTransaction?.customer2?.info?.address,
            },
            rootAddress: {
              ...dataDetailContract?.primaryTransaction?.customer2?.info?.rootAddress,
            },
          },
          personalInfo: {
            ...dataDetailContract?.primaryTransaction?.customer2?.personalInfo,
            identities: dataDetailContract?.primaryTransaction?.customer2?.personalInfo?.identities?.map(
              (item: TIdentities) => {
                const getIdentityByValue = (value: string): DefaultOptionType => {
                  return OPTIONS_IDENTITIES
                    ? OPTIONS_IDENTITIES.find(option => option.value === value) || { label: 'Unknown', value: '' }
                    : {};
                };

                const newTypeObject: DefaultOptionType = getIdentityByValue(item.type as string);
                return {
                  ...item,
                  typeObject: newTypeObject,
                  date: item?.date ? dayjs(item?.date, FORMAT_DATE) : undefined,
                };
              },
            ),
          },
          address: {
            ...dataDetailContract?.primaryTransaction?.customer2?.info?.address,
          },
          rootAddress: {
            ...dataDetailContract?.primaryTransaction?.customer2?.info?.rootAddress,
          },
          customerSelect: {
            id: dataDetailContract?.primaryTransaction?.customer2?.id,
            code: dataDetailContract?.primaryTransaction?.customer2?.code,
          },
          primaryTransactionId: dataDetailContract?.primaryTransaction?.id,
          contractId: dataDetailContract?.id,
          maintenanceFeeType: dataDetailContract?.maintenanceFee?.type,
          maintenanceFeeValue: dataDetailContract?.maintenanceFee?.value,
          calcCurrencyFirst: dataDetailContract?.calcCurrencyFirst,
          changeInstallment: dataDetailContract?.changeInstallment,
          nameContract: dataDetailContract?.name,
          codeContract: dataDetailContract?.code,
          priceType: priceType,
          policyPayment: dataDetailContract?.policyPayment?.id,
          policyDiscountIds: dataDetailContract?.policyDiscounts.map((item: PolicyDiscounts) => item.id),
          productPrice: calculatedPrice,
          projectName: dataDetailContract?.primaryTransaction?.project?.name || '',
          propertyUnitCode: dataDetailContract?.primaryTransaction?.propertyUnit?.code || '',
          price: dataDetailContract?.primaryTransaction?.propertyUnit?.price || 0,
          priceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.priceVat || 0,
          landPrice: dataDetailContract?.primaryTransaction?.propertyUnit?.landPrice || 0,
          landPriceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.landPriceVat || 0,
          housePrice: dataDetailContract?.primaryTransaction?.propertyUnit?.housePrice || 0,
          housePriceVat: dataDetailContract?.primaryTransaction?.propertyUnit?.housePriceVat || 0,
          customerName: dataDetailContract?.primaryTransaction?.customer?.personalInfo?.name || '',
          posName: dataDetailContract?.primaryTransaction?.employee?.pos?.name || '',
          contractPriceForMaintenanceFee:
            dataDetailContract?.primaryTransaction?.propertyUnit?.contractPriceForMaintenanceFee || 0,
          contractPrice: dataDetailContract?.primaryTransaction?.propertyUnit?.contractPrice || 0,
          startDate: dataDetailContract?.startDate ? dayjs(dataDetailContract?.startDate) : undefined,
          expiredDate: dataDetailContract?.expiredDate ? dayjs(dataDetailContract?.expiredDate) : undefined,
          releaseStartDate: dataDetailContract?.releaseStartDate
            ? dayjs(dataDetailContract?.releaseStartDate)
            : undefined,
          releaseEndDate: dataDetailContract?.releaseEndDate ? dayjs(dataDetailContract?.releaseEndDate) : undefined,
          signedDate: dataDetailContract?.signedDate ? dayjs(dataDetailContract?.signedDate) : undefined,
          isShowOwned: dataDetailContract?.primaryTransaction?.customer2?.name ? true : false,
          loanType: dataDetailContract?.loanType || 'no',
          sapCode: dataDetailContract?.sapCode || '',
          poNumber: dataDetailContract?.poNumber || '',
          discountValue: dataDetailContract?.discountValue || '',
          issuedPrice: dataDetailContract?.issuedPrice || '',
          businessArea: dataDetailContract?.businessArea || null,
          distributionChannel: dataDetailContract?.distributionChannel || null,
          productCategory: dataDetailContract?.productCategory || null,
          loanBankCode: dataDetailContract?.loanBankInfo?.bankCode,
        } as unknown;
        form.setFieldsValue(formatData);
      } catch (error) {
        console.error('Error fetching deposit contract details:', error);
      }
    }
  };

  const handleFinish = (values: TDepositContractForm) => {
    let calcPriceVat = false;
    let calcContractPrice = false;
    if (values?.priceType === 'contract') {
      calcContractPrice = true;
    } else {
      calcPriceVat = values?.priceType === 'vat';
    }
    const payload: TDepositContractPayload = {
      ...detailDataTicket,
      files: fileList,
      primaryTransactionId: values?.primaryTransactionId || '',
      policyPaymentId: values?.policyPayment?.id || '',
      policyDiscountIds: values?.policyDiscountIds || [],
      calcCurrencyFirst: values?.calcCurrencyFirst || false,
      calcPriceVat: calcPriceVat,
      calcContractPrice: calcContractPrice,

      maintenanceFee: {
        ...values?.maintenanceFee,
        type: values?.maintenanceFeeType || '',
        value: values?.maintenanceFeeValue || 0,
      },
      type: 'rent',
      startDate: values?.startDate ? dayjs(values?.startDate).format('YYYY-MM-DD') : '',
      expiredDate: values?.expiredDate ? dayjs(values?.expiredDate).format('YYYY-MM-DD') : '',
      signedDate: values?.signedDate,
      transferType: values?.transferType,
      isDebtRemind: values?.isDebtRemind,
      changeInstallment: values?.changeInstallment,
      releaseStartDate: values?.releaseStartDate ? dayjs(values?.releaseStartDate).format('YYYY-MM-DD') : '',
      releaseEndDate: values?.releaseEndDate ? dayjs(values?.releaseEndDate).format('YYYY-MM-DD') : '',
      salesPolicy: {
        id: values?.salesPolicy?.id || '',
        name: values?.salesPolicy?.name || '',
        code: values?.salesPolicy?.code || '',
      },
      staffsInvolved: staffList
        .filter(item => item.id && item.percent !== undefined)
        .map(item => ({
          id: item.id as string,
          code: item.code || '',
          name: item.name || '',
          percent: item.percent || 0,
        })),
      businessArea: {
        id: values?.businessArea?.id || '',
        name: values?.businessArea?.nameVN || '',
        code: values?.businessArea?.code || '',
      },
      distributionChannel: {
        id: values?.distributionChannel?.id || '',
        name: values?.distributionChannel?.name || '',
        code: values?.distributionChannel?.code || '',
      },
      productCategory: {
        id: values?.productCategory?.id || '',
        name: values?.productCategory?.name || '',
        code: values?.productCategory?.code || '',
      },
      poNumber: values?.poNumber || '',
      loanBankInfo: values?.loanBankInfo,
      loanType: values?.loanType,
      loanTermYear: values?.loanTermYear,
      loanAmount: values?.loanAmount,
    };
    onFinish?.(payload);
  };

  // Cột cho bảng nhân viên
  const staffColumns = [
    {
      title: 'Nhân viên',
      dataIndex: 'employee',
      key: 'employee',
      width: '60%',
      render: (_: string, record: StaffItem) => (
        <Form.Item
          style={{ margin: 0 }}
          name={['staff', record.key, 'employee']}
          rules={[{ required: true, message: 'Vui lòng chọn nhân viên' }]}
        >
          <SingleSelectLazy
            apiQuery={getListEmployeeByOrgchartId}
            queryKey={['employee-dropdown']}
            keysLabel={['name', 'email']}
            placeholder="Chọn nhân viên"
            disabled
            defaultValues={record.id ? { value: record.id, label: record.name || record.id } : undefined}
            moreParams={{ projectId: detailDataTicket?.primaryTransaction?.project?.id, id: pos?.id || '' }}
          />
        </Form.Item>
      ),
    },
    {
      title: 'Tỉ lệ tham gia (%)',
      dataIndex: 'percent',
      key: 'percent',
      width: '30%',
      render: (_: string, record: StaffItem) => (
        <Form.Item
          style={{ margin: 0 }}
          name={['staff', record.key, 'percent']}
          rules={[{ required: true, message: 'Vui lòng nhập tỉ lệ' }]}
        >
          <InputNumber
            placeholder="Nhập tỉ lệ (%)"
            min={0}
            max={100}
            step={0.01}
            precision={2}
            disabled
            formatter={(value: number | undefined) => (value !== undefined ? `${value}` : '')}
            value={record.percent}
            style={{ width: '100%' }}
            maxLength={5}
          />
        </Form.Item>
      ),
    },
  ];

  return (
    <Spin spinning={loading}>
      <Form className="form-detail" form={form} onFinish={handleFinish} layout="vertical">
        <Row gutter={64}>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin hợp đồng
            </Title>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item name="nameContract" label="Tên hợp đồng">
                  <Input placeholder="Tên hợp đồng" disabled />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item name="codeContract" label="Mã hợp đồng">
                  <Input placeholder="Mã hợp đồng" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={14}>
                <Form.Item
                  label="Hợp đồng cọc"
                  name="contractId"
                  rules={[{ required: true, message: 'Vui lòng chọn hợp đồng cọc' }]}
                >
                  <SingleSelectLazy
                    queryKey={['get-deposit-contract-dropdown']}
                    disabled={dataDetailContract ? true : false}
                    apiQuery={getListDepositContractSelect}
                    placeholder="Chọn mã hợp đồng cọc"
                    keysLabel={'name'}
                    handleSelect={handleSelectDepositContract}
                    defaultValues={defaultDepositContract}
                    moreParams={{
                      havePurchase: false,
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item name="sapCode" label="Mã hợp đồng SAP">
                  <Input placeholder="" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Tên khách hàng" name="customerName">
                  <Input placeholder="Tên khách hàng" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Số hợp đồng giấy"
                  name="poNumber"
                  rules={[{ required: true, message: 'Vui lòng nhập số hợp đồng giấy' }]}
                >
                  <Input maxLength={35} placeholder="Số hợp đồng giấy" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Mã công ty/ chi nhánh"
                  name="businessArea"
                  rules={[{ required: true, message: 'Vui lòng chọn mã công ty/ chi nhánh' }]}
                >
                  <SingleSelectLazy
                    queryKey={['get-business-area']}
                    apiQuery={getListBusinessArea}
                    placeholder="Chọn mã công ty/ chi nhánh"
                    keysLabel={['code', 'nameVN']}
                    handleSelect={value => {
                      form.setFieldsValue({ businessArea: value });
                    }}
                    defaultValues={defaultBusinessArea}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Kênh phân phối"
                  name="distributionChannel"
                  rules={[{ required: true, message: 'Vui lòng chọn kênh phân phối' }]}
                >
                  <SingleSelectLazy
                    queryKey={['get-distribution-channel']}
                    apiQuery={getListDistributionChannel}
                    placeholder="Chọn kênh phân phối"
                    keysLabel={['name']}
                    handleSelect={value => {
                      form.setFieldsValue({ distributionChannel: value });
                    }}
                    defaultValues={defaultDistributionChannel}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngành hàng"
                  name="productCategory"
                  rules={[{ required: true, message: 'Vui lòng chọn ngành hàng' }]}
                >
                  <SingleSelectLazy
                    queryKey={['get-division']}
                    apiQuery={getDivision}
                    placeholder="Chọn ngành hàng"
                    keysLabel={['name']}
                    handleSelect={value => {
                      form.setFieldsValue({ productCategory: value });
                    }}
                    defaultValues={defaultProductCategory}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item
                  label="Ngày hiệu lực"
                  name="startDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày hiệu lực' }]}
                >
                  <DatePicker disabled placeholder="" format="DD/MM/YYYY" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Ngày kết thúc hiệu lực" name="expiredDate">
                  <DatePicker disabled placeholder="" format="DD/MM/YYYY" />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={12}>
                <Form.Item label="Ngày phát hành" name="releaseStartDate">
                  <DatePicker placeholder="Chọn ngày phát hành" format="DD/MM/YYYY" />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngày kết thúc"
                  name="releaseEndDate"
                  dependencies={['releaseStartDate']}
                  rules={[
                    {
                      validator: validateEndDate(form, 'startDate', 'Ngày kết thúc lớn hơn hoặc bằng ngày phát hành'),
                    },
                  ]}
                >
                  <DatePicker placeholder="Chọn ngày kết thúc" format="DD/MM/YYYY" />
                </Form.Item>
              </Col>
            </Row>

            <Form.Item
              label="Ngày ký kết"
              name="signedDate"
              rules={[{ required: true, message: 'Vui lòng chọn ngày ký kết' }]}
            >
              <DatePicker placeholder="Chọn ngày ký kết" format="DD/MM/YYYY" />
            </Form.Item>

            <Form.Item label="Hình thức thanh toán" name="transferType">
              <Select placeholder="Chọn hình thức thanh toán" allowClear options={LISTPAYMENTS} />
            </Form.Item>

            <Form.Item name="isDebtRemind" valuePropName="checked">
              <Checkbox>Nhắc nợ</Checkbox>
            </Form.Item>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin đồng sở hữu
            </Title>
            <Form.Item label="" name="isShowOwned" valuePropName="checked">
              <Checkbox disabled>Thêm thông tin đồng sở hữu</Checkbox>
            </Form.Item>

            {isShowOwned && <CustomerForm disabledForm={true} defaultCustomer={defaultCustomer} form={form} />}

            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin bán hàng
            </Title>
            <Form.Item label="Đơn vị bán hàng" name="posName">
              <Input placeholder="Đơn vị sở hữu" disabled />
            </Form.Item>

            <Form.Item
              label="Chính sách phí - hoa hồng"
              required
              name="salesPolicy"
              rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
            >
              <SingleSelectLazy
                apiQuery={getListSalesPolicy}
                queryKey={['sales-policy']}
                keysLabel={['code', 'name']}
                placeholder="Chọn chính sách phí - hoa hồng"
                handleSelect={handleSelectSalePolicy}
                moreParams={{ projectID: detailDataTicket?.primaryTransaction?.project?.id, isActive: true }}
                disabled={!isPrimaryTransaction}
                defaultValues={defaultSalePolicy}
              />
            </Form.Item>

            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin đính kèm
            </Title>

            <Form.Item name="files">
              <CommonFileUpload
                fileList={fileList}
                setFileList={setFileList}
                uploadPath={'contract-purchase'}
                // allowedMimeTypes={allowedMimeTypes}
                // allowedExtensions={ALLOWED_EXTENSIONS}
                maxFileSize={10}
                maxTotalSize={1}
                maxFiles={10}
                buttonText="Upload"
                buttonWidth="100px"
              />
            </Form.Item>
            <Title level={5}>Danh sách nhân viên tham gia bán hàng</Title>
            <Row gutter={{ md: 24, lg: 40 }}>
              <Col xs={24} md={24}>
                <Form.Item name="staffsInvolved" hidden>
                  <Input />
                </Form.Item>
                <Table
                  columns={staffColumns}
                  dataSource={staffList}
                  pagination={false}
                  rowKey="key"
                  sticky={{ offsetHeader: 0 }}
                />
              </Col>
            </Row>
            <Title level={5} style={{ marginBottom: 16, marginTop: 16 }}>
              Thông tin vay
            </Title>
            <Form.Item style={{ marginBottom: 16, display: 'flex', alignItems: 'center' }}>
              <span style={{ minWidth: 120 }}>Vay ngân hàng</span>
              <Form.Item name="loanType" noStyle>
                <Radio.Group style={{ marginLeft: 16 }}>
                  <Radio value="yes">Có</Radio>
                  <Radio value="no">Không</Radio>
                </Radio.Group>
              </Form.Item>
            </Form.Item>
            {loanType === 'yes' && <LoanInfoForm form={form} />}
          </Col>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin dự án
            </Title>
            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Tên dự án" name="projectName">
                  <Input placeholder="Tên dự án" disabled />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Mã sản phẩm" name="propertyUnitCode">
                  <Input placeholder="Mã sản phẩm" disabled />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Giá trị sản phẩm trên hợp đồng" name="productPrice">
                  <InputNumber placeholder="Giá sản phẩm" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Giá bán chưa VAT" name="price">
                  <InputNumber placeholder="Giá bán chưa VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Giá bán có VAT" name="priceVat">
                  <InputNumber placeholder="Giá bán có VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Giá nhà chưa VAT" name="housePrice">
                  <InputNumber placeholder="Giá nhà chưa VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Giá nhà có VAT" name="housePriceVat">
                  <InputNumber placeholder="Giá nhà có VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={14}>
                <Form.Item label="Giá đất chưa VAT" name="landPrice">
                  <InputNumber placeholder="Giá đất chưa VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
              <Col span={10}>
                <Form.Item label="Giá đất có VAT" name="landPriceVat">
                  <InputNumber placeholder="Giá đất có VAT" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Tổng giá trị CH sau CK gồm VAT, chưa bao gồm PBT" name="contractPrice">
                  <InputNumber
                    placeholder="Giá trị CH sau CK gồm VAT, chưa bao gồm PBT"
                    disabled
                    formatter={formatNumber}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Giá trị CH tính phí bảo trì" name="contractPriceForMaintenanceFee">
                  <InputNumber placeholder="Giá trị CH tính phí bảo trì" disabled formatter={formatNumber} />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Giá trị chiết khấu" name="discountValue">
                  <InputNumber
                    placeholder=""
                    disabled
                    formatter={formatNumber}
                    suffix="VNĐ"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Giá ban hành" name="issuedPrice">
                  <InputNumber
                    placeholder=""
                    disabled
                    formatter={formatNumber}
                    suffix="VNĐ"
                    style={{ width: '100%' }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <div style={{ display: 'flex', marginBottom: 16 }}>
                  <div style={{ width: 120 }}>Tính giá trị:</div>
                  <Form.Item name="priceType" style={{ marginBottom: 0 }}>
                    <Radio.Group>
                      <Space direction="vertical">
                        <Radio value="vat">Giá có VAT</Radio>
                        <Radio value="non-vat">Giá không có VAT</Radio>
                        <Radio
                          disabled={
                            form.getFieldValue('contractPrice') ? form.getFieldValue('contractPrice') <= 0 : true
                          }
                          value="contract"
                        >
                          Tổng giá trị CH sau CK gồm VAT, chưa gồm PBT
                        </Radio>
                      </Space>
                    </Radio.Group>
                  </Form.Item>
                </div>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="changeInstallment" valuePropName="checked">
                  <Checkbox disabled>Không cho phép thay đổi đợt thanh toán</Checkbox>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label="Chính sách thanh toán"
                  required
                  name="policyPayment"
                  rules={[{ required: true, message: 'Vui lòng chọn chính sách thanh toán' }]}
                >
                  <SingleSelectLazy
                    disabled={!isPrimaryTransaction || changeInstallment}
                    apiQuery={getListPaymentPolicy}
                    queryKey={['payment-policy']}
                    keysLabel={['code', 'name']}
                    placeholder="Chọn chính sách thanh toán"
                    handleSelect={handleSelectPaymentPolicy}
                    defaultValues={defaultPaymentPolicy}
                    moreParams={{ active: true, projectIds: detailDataTicket?.primaryTransaction?.project?.id }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item name="calcCurrencyFirst" valuePropName="checked">
                  <Checkbox>Tính chiết khấu tiền mặt trước</Checkbox>
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Chính sách chiết khấu" name="policyDiscountIds">
                  <MultiSelectLazy
                    apiQuery={getListDiscountPolicy}
                    queryKey={['discount-policy']}
                    keysLabel={['name']}
                    keysTag={'name'}
                    placeholder="Chọn chính sách chiết khấu"
                    disabled={!isPrimaryTransaction}
                    handleListSelect={handleSelectSDiscountPolicy}
                    defaultValues={
                      defaultDiscountPolicy as {
                        label: string | JSX.Element;
                        value: string;
                        [key: string]: string | JSX.Element;
                      }[]
                    }
                    moreParams={{ active: true, projectIds: detailDataTicket?.primaryTransaction?.project?.id }}
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Phí bảo trì" name="maintenanceFeeValue">
                  <InputNumber
                    maxLength={maintenanceFeeType === 'percent' ? 5 : 15}
                    style={{ width: '100%' }}
                    placeholder="Nhập giá trị chiết khấu"
                    min={0}
                    max={maintenanceFeeType === 'percent' ? 100 : undefined}
                    step={maintenanceFeeType === 'percent' ? 0.01 : 1}
                    precision={2}
                    formatter={(value: number | string | undefined) =>
                      maintenanceFeeType === 'currency' && value !== undefined
                        ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                        : `${value}`
                    }
                    parser={(value: string | undefined) =>
                      maintenanceFeeType === 'currency' && value ? parseFloat(value.replace(/,/g, '')) : value || ''
                    }
                    addonAfter={
                      <Form.Item name="maintenanceFeeType" noStyle>
                        <Select style={{ width: 91 }} onChange={handleMaintenanceFeeTypeChange}>
                          <Option value="percent">%</Option>
                          <Option value="currency">VND</Option>
                        </Select>
                      </Form.Item>
                    }
                  />
                </Form.Item>
              </Col>
            </Row>

            <Row gutter={24}>
              <Col span={24}>
                <Form.Item label="Đợt thu phí bảo trì" name={['maintenanceFee', 'stagePayment']}>
                  <Select placeholder="Chọn đợt thu phí" options={installments} />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
        <Title level={5} style={{ marginTop: 16, marginBottom: 16 }}>
          Thông tin thanh toán
        </Title>
        <Space className="payment-info-radio-group">
          <Radio.Group
            value={tabSelect}
            onChange={e => setTabSelect(e.target.value)}
            optionType="button"
            className="rounded-radio-group"
          >
            <Radio.Button value={TabType.TAB1}>Lịch sử thanh toán</Radio.Button>
            <Radio.Button value={TabType.TAB2}>Danh sách phiếu tính lãi</Radio.Button>
            <Radio.Button value={TabType.TAB3}>Lịch sử chuyển nhượng</Radio.Button>
          </Radio.Group>
        </Space>
        {tabSelect === TabType.TAB1 && (
          <div style={{ marginBottom: 60 }} className="mb-3">
            <PaymentHistory
              dataPaymentHistory={paymentHistoryData}
              productPrice={productPrice}
              totalTransfered={totalTransfered}
            />
          </div>
        )}
        {tabSelect === TabType.TAB2 && (
          <div style={{ marginBottom: 60 }} className="mb-3">
            <InterestCalculationList interestCalculationList={listInterestCalculations} />
          </div>
        )}
        <div className="create-footer">
          <div className="button-create">
            <Button type="primary" htmlType="submit" loading={loading} size="small">
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </Spin>
  );
};

export default PurchaseContractFrom;

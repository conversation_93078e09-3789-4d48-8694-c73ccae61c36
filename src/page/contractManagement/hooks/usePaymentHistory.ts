import { useMemo } from 'react';
import dayjs from 'dayjs';
import { Installment, PaymentHistoryData, Receipt } from '../../../types/contract/depositContract';

const SalePolicyConstant = { INSTALLMENT_TYPE_CURRENCY: 'currency' };

interface PaymentHistoryResult {
  paymentHistoryData: PaymentHistoryData[];
  productPrice: number;
  totalTransfered: number;
}

/**
 * hook để tính toán lịch sử thanh toán, danh sách đợt thanh toán, giá trị sản phẩm và tổng số tiền đã chuyển.
 * @param installments - Danh sách các đợt thanh toán từ policyPayment.schedule.installments.
 * @returns  paymentHistoryData, productPrice, và totalTransfered.
 */
export const usePaymentHistory = (installments: Installment[] | undefined): PaymentHistoryResult => {
  return useMemo(() => {
    if (!installments) {
      return {
        paymentHistoryData: [],
        productPrice: 0,
        totalTransfered: 0,
      };
    }

    let productPrice = 0;
    let totalTransfered = 0;
    const paymentHistoryData: PaymentHistoryData[] = [];

    const getValue = (item: Installment): string =>
      item?.value2
        ? `${item.type === SalePolicyConstant.INSTALLMENT_TYPE_CURRENCY ? '- GTN' : `${item.value}% GTN`}/${
            item.type2 === SalePolicyConstant.INSTALLMENT_TYPE_CURRENCY ? '- GTĐ' : `${item.value2}% GTĐ`
          }`
        : item?.type === SalePolicyConstant.INSTALLMENT_TYPE_CURRENCY
          ? '-'
          : `${item?.value}%`;

    installments.forEach((item, index) => {
      productPrice += item.totalAmount;
      totalTransfered += item.totalTransfered || 0;

      const receipts = item?.receipts?.filter((r: { status: string }) => r.status === 'TRANSFERED') || [];
      const baseData = {
        isToContract: item?.isToContract,
        transactionSuccessful: item?.transactionSuccessful,
        name: item?.name,
        totalAmount: item?.totalAmount,
        descriptionProgress: item?.descriptionProgress,
        paymentDueDate: item?.paymentDueDate ? dayjs(item?.paymentDueDate).format('DD/MM/YYYY') : '',
      };

      if (!receipts.length) {
        paymentHistoryData.push({
          ...baseData,
          stt: index + 1,
          totalTransfered: 0,
          totalRemaining: item?.totalAmount,
          value: getValue(item),
          code: '-',
          date: '-',
        });
      } else if (receipts.length === 1) {
        paymentHistoryData.push({
          ...baseData,
          stt: index + 1,
          totalTransfered: receipts[0]?.amount,
          totalRemaining: item?.totalAmount - receipts[0]?.amount || 0,
          value: getValue(item),
          code: receipts[0]?.code,
          date: receipts[0]?.receiptDate ? dayjs(receipts[0]?.receiptDate).format('DD/MM/YYYY') : '',
        });
      } else {
        let total = item?.totalAmount;
        receipts.forEach((r: Receipt, r_idx: number) => {
          paymentHistoryData.push({
            ...baseData,
            stt: r_idx === 0 ? index + 1 : '',
            isToContract: r_idx === 0 ? item?.isToContract : false,
            name: r_idx === 0 ? item?.name : '',
            totalAmount: r_idx === 0 ? item?.totalAmount : 0,
            totalTransfered: r?.amount,
            totalRemaining: r_idx === receipts?.length - 1 ? total - r?.amount : '',
            value: r_idx === 0 ? getValue(item) : '',
            code: r?.code,
            date: dayjs(r.receiptDate).format('DD/MM/YYYY'),
            paymentDueDate: r_idx === 0 ? baseData?.paymentDueDate : '',
          });
          total -= r?.amount;
        });
      }
    });

    let remainMoney = 0;
    paymentHistoryData.forEach((e, i) => {
      if (typeof e?.totalRemaining === 'number' && e?.totalRemaining < 0 && i < paymentHistoryData?.length - 1) {
        remainMoney += e?.totalRemaining;
        e.totalRemaining = 0;
      } else if (
        typeof e?.totalRemaining === 'number' &&
        e?.totalRemaining > 0 &&
        remainMoney < 0 &&
        i < paymentHistoryData.length - 1
      ) {
        if (e?.totalRemaining + remainMoney > 0) {
          e.totalRemaining += remainMoney;
          remainMoney = 0;
        } else {
          remainMoney += e?.totalRemaining;
          e.totalRemaining = 0;
        }
      } else if (i === paymentHistoryData.length - 1 && typeof e?.totalRemaining === 'number') {
        e.totalRemaining = e?.totalRemaining + remainMoney > 0 ? e?.totalRemaining + remainMoney : 0;
      }
    });

    return {
      paymentHistoryData,
      productPrice,
      totalTransfered,
    };
  }, [installments]);
};

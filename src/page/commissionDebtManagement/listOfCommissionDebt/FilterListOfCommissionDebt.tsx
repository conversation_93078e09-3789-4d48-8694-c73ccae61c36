import { Col, DatePicker, Form, Row } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useState } from 'react';
import DropdownFilterPeriod from '../../../components/dropdown/dropdownFilterPeriod';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../components/select/mutilSelectLazy';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { DEFAULT_PARAMS, FORMAT_DATE, FORMAT_DATE_API } from '../../../constants/common';
import { getListAllEmployeeDropdown } from '../../../service/employee';
import { sendGetListOfDropdownProjects } from '../../../service/salesPolicy';
import { TFilterCommissionDebt } from '../../../types/commissionDebt';
import { TEmployeeAll } from '../../../types/customers';

const FilterListOfCommissionDebt = ({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<TFilterCommissionDebt>>;
}) => {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const handleSubmitFilter = (values: TFilterCommissionDebt) => {
    const newFilter: TFilterCommissionDebt = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : undefined,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : undefined,
      createdBy: values?.createdBy ? String(values.createdBy) : undefined,
      periodName: values?.periodName ? values.periodName : undefined,
      year: values?.year ? values.year : undefined,
      project: values?.project ? String(values.project) : undefined,
    };
    setFilterParams(prev => ({ ...prev, ...DEFAULT_PARAMS, ...newFilter }));
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setIsOpenFilter(false);
      setFilterParams(prev => ({ search: prev?.search }));
    }, 100);
  };

  const handleSelectEmployee = (values: { option: TEmployeeAll }[]) => {
    form.setFieldsValue({
      createdBy: values
        ?.map(item => item?.option?.id)
        .filter(item => item !== undefined)
        .join(','),
    });
  };

  const handleSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, ...DEFAULT_PARAMS, search: search }));
  };
  const handleSelectProject = (value: { name: string; id: string }) => {
    form.setFieldsValue({ project: value?.id });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearch}
        extraFormItems={
          <>
            <Form.Item label="Chọn dự án" name="project">
              <SingleSelectLazy
                gcTime={1000 * 60 * 15} // 15 phút
                apiQuery={sendGetListOfDropdownProjects}
                queryKey={['list-source']}
                enabled={isOpenFilter}
                placeholder="Chọn dự án"
                keysLabel={['name']}
                handleSelect={handleSelectProject}
              />
            </Form.Item>
            <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                gcTime={1000 * 60 * 15} // 15 phút
                enabled={isOpenFilter}
                apiQuery={getListAllEmployeeDropdown}
                queryKey={['employee-dropdown']}
                keysLabel={['username', 'name']}
                handleListSelect={handleSelectEmployee}
                placeholder="Chọn nhân viên"
                keysTag={'username'}
              />
            </Form.Item>
            <DropdownFilterPeriod label="Kỳ tính hoa hồng" fieldName="periodName" />

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endCreatedDate = form.getFieldValue('endCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endCreatedDate && current > dayjs(endCreatedDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startCreatedDate = form.getFieldValue('startCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startCreatedDate && current < dayjs(startCreatedDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterListOfCommissionDebt;

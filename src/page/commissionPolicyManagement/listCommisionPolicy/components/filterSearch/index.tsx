import { Col, Form, Row, Select } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { Dispatch, SetStateAction, useState } from 'react';
import DatePickerFilter from '../../../../../components/datePicker/DatePickerFilter';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import {
  DEFAULT_PARAMS,
  FORMAT_DATE_API,
  OPTIONS_STATUS_KPI,
  OPTIONS_TYPE_COMMISSION_POLICY,
} from '../../../../../constants/common';
import useFilter from '../../../../../hooks/filter';
import { getListPos } from '../../../../../service/commissionPolicy';
import { TEmployeeAll } from '../../../../../types/customers';
import { CommissionPolicyType } from '../../../utils';
import './styles.scss';

export type TFilterCommissionPolicy = {
  isActive?: number | null;
  type?: string | null;
  posId?: string;
  startDate?: string | Dayjs | null;
  endDate?: string | Dayjs | null;
  status?: string;
  createdBy?: string;
  share?: string;
  search?: string;
};

function FilterSearch({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<TFilterCommissionPolicy | undefined>>;
}) {
  const [isOpenFilter, setIsOpenFilter] = useState(false);
  const [, setFilter] = useFilter();
  const [form] = Form.useForm();

  const handleSubmitFilter = (values: TFilterCommissionPolicy) => {
    const newFilter: Record<string, unknown> = {
      type:
        values?.type === CommissionPolicyType.MANAGER
          ? 'Quản lý'
          : values?.type === CommissionPolicyType.PERSONAL
            ? 'Cá nhân'
            : '',
      posId: values?.posId ? values?.posId : null,
      isActive: values?.isActive ? values?.isActive : null,
      startDate: values?.startDate ? dayjs(values?.startDate).format(FORMAT_DATE_API) : null,
      endDate: values?.endDate ? dayjs(values?.endDate).format(FORMAT_DATE_API) : null,
      // createdBy: values?.createdBy ? values.createdBy : null,
    };
    setFilterParams(prev => ({ ...prev, ...newFilter }));
    setFilter(DEFAULT_PARAMS);
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  // const handleSelectEmployee = (values: TEmployeeAll[]) => {
  //   form.setFieldsValue({
  //     createdBy: values
  //       .map(item => item.accountId)
  //       .filter(item => item !== undefined)
  //       .join(','),
  //   });
  // };

  const handleSelectPos = (values: TEmployeeAll[]) => {
    form.setFieldsValue({ posId: values.map(item => item.id).join(',') });
  };

  const onChangeSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, search: search }));
    setFilter(DEFAULT_PARAMS);
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setIsOpenFilter(false);
      setFilter({});
      setFilterParams(prev => ({ search: prev?.search }));
    }, 100);
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-customers"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        onChangeSearch={onChangeSearch}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        keySearch="searchText"
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Row gutter={16}>
              <Col span={24}>
                <Form.Item label="Loại chỉ tiêu" name="type">
                  <Select placeholder="Chọn loại chỉ tiêu" allowClear options={OPTIONS_TYPE_COMMISSION_POLICY} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Đơn vị bán hàng" name="posId">
                  <MultiSelectLazy
                    enabled={isOpenFilter}
                    apiQuery={getListPos}
                    queryKey={['pos-dropdown']}
                    keysLabel={['name']}
                    handleListSelect={handleSelectPos}
                    placeholder="Chọn đơn vị bán hàng"
                    keysTag={'name'}
                  />
                </Form.Item>{' '}
              </Col>
              <Col span={24}>
                {/* <Form.Item label="Người tạo" name="createdBy">
                  <MultiSelectLazy
                    enabled={isOpenFilter}
                    apiQuery={getListAllEmployeeDropdown}
                    queryKey={['employee-dropdown']}
                    keysLabel={['username', 'name']}
                    handleListSelect={handleSelectEmployee}
                    defaultValues={defaultEmployee}
                    placeholder="Chọn nhân viên"
                    keysTag={'username'}
                  />
                </Form.Item> */}
              </Col>
              <Col span={24}>
                <Form.Item label="Trạng thái" name="isActive">
                  <Select placeholder="Chọn trạng thái" allowClear options={OPTIONS_STATUS_KPI} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <DatePickerFilter startDate="startDate" endDate="endDate" />
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
}

export default FilterSearch;

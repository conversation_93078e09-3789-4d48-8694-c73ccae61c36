import { App, Button, Flex, notification, TableColumnsType } from 'antd';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { columns } from './columns';
import './styles.scss';
import { COMMISSION_POLICY } from '../../../configs/path';
import FilterSearch, { TFilterCommissionPolicy } from './components/filterSearch';
import React from 'react';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { ICommissionPolicy } from '../../../types/commissionPolicy';
import {
  changeStatusCommission,
  deleteCommissionPolicy,
  getListCommissionPolicy,
} from '../../../service/commissionPolicy';
import CreateModalCommissionPolicy from '../createCommissionPolicy';
import { useCommissionPolicyStore } from '../store';

function ListCommissionPolicies() {
  const { modal } = App.useApp();
  const [filterParams, setFilterParams] = useState<TFilterCommissionPolicy>();

  const { setActionModal } = useCommissionPolicyStore();

  const {
    create: checkCreate,
    getById: checkGetDetail,
    delete: checkDelete,
  } = useCheckPermissions(PERMISSION_COMMISSION);

  const {
    data: listCommissionPolicies,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ICommissionPolicy[]>({
    queryKeyArrWithFilter: ['get-list-commission-policies', filterParams].filter(Boolean),
    api: getListCommissionPolicy,
    moreParams: { ...filterParams },
  });

  const { mutateAsync: updateActive } = useUpdateField({
    apiQuery: changeStatusCommission,
    keyOfListQuery: ['get-list-commission-policies'],
    isShowMessage: false,
    isMessageSuccess: false,
  });

  const [currentRecord, setCurrentRecord] = React.useState<ICommissionPolicy>();
  const [isOpenModalDelete, setIsOpenModalDelete] = React.useState<boolean>(false);

  const columnActions: TableColumnsType<ICommissionPolicy> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_: unknown, record: ICommissionPolicy) => {
          const openViewDetail = () => {
            window.open(`${COMMISSION_POLICY}/${record?.id}`, '_blank', 'noopener noreferrer');
          };
          const handleDeleteUnitPartner = () => {
            setIsOpenModalDelete(!isOpenModalDelete);
            setCurrentRecord(record);
          };

          const textModalConfirmActive = record?.isActive === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActiveCommissionPolicy = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} bộ chỉ tiêu`,
              content: `Bạn có muốn ${textModalConfirmActive} bộ chỉ tiêu KPI này không?`,
              handleConfirm: async () => {
                const res = await updateActive({
                  id: record?.id ?? '',
                  isActive: record?.isActive === 1 ? 2 : 1,
                });
                if (res?.data?.statusCode === '0') {
                  notification.success({
                    message: record?.isActive === 1 ? 'Vô hiệu hóa thành công' : 'Kích hoạt thành công',
                  });
                  return Promise.resolve();
                } else {
                  notification.error({
                    message: record?.isActive === 1 ? 'Vô hiệu hóa không thành công' : 'Kích hoạt không thành công',
                  });
                  return Promise.reject();
                }
              },
            });
          };

          const handleCloneCommissionPolicy = async () => {
            setActionModal({ isOpen: true, type: 'clone', id: record?.id });
          };

          return (
            <ActionsColumns
              handleViewDetail={checkGetDetail ? openViewDetail : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={handleActiveCommissionPolicy}
              handleDelete={checkDelete ? handleDeleteUnitPartner : undefined}
              handleCloneRole={handleCloneCommissionPolicy}
            />
          );
        },
      },
    ];
  }, [checkDelete, checkGetDetail, isOpenModalDelete, modal, setActionModal, updateActive]);

  const handleOpenModalCreate = () => {
    setActionModal({ isOpen: true, type: 'create' });
  };

  return (
    <div className="wrapper-list-commission-policies">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch setFilterParams={setFilterParams} />
        </Flex>
        {checkCreate && (
          <Flex gap={10}>
            <Button type="primary" onClick={handleOpenModalCreate}>
              Thêm mới
            </Button>
          </Flex>
        )}
      </div>
      <div className="table-business-commission-policies">
        <TableComponent
          queryKeyArr={['get-list-commission-policies']}
          columns={columnActions}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listCommissionPolicies?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ConfirmDeleteModal
        label="bộ chỉ tiêu KPI"
        open={isOpenModalDelete}
        apiQuery={deleteCommissionPolicy as MutationFunction<unknown, unknown>}
        keyOfListQuery={['get-list-commission-policies']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá bộ chỉ tiêu KPI"
        description="Vui lòng nhập lý do muốn xoá bộ chỉ tiêu KPI"
      />
      <CreateModalCommissionPolicy />
    </div>
  );
}

export default ListCommissionPolicies;

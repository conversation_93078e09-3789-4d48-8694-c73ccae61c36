import { Button, Checkbox, Col, Form, Input, Row, Select, Spin, Switch, Typography } from 'antd';
import dayjs from 'dayjs';
import { useCallback, useEffect, useState } from 'react';
import ModalComponent from '../../../components/modal';
import { useCreateField, useFetch } from '../../../hooks';
import { cloneCommissionPolicy, createCommissionPolicy, getListPos } from '../../../service/commissionPolicy';
import { ICommissionPolicy, TCreateCommissionPolicy, TPos, TRate } from '../../../types/commissionPolicy';
import ListRate from '../components/ListRate';
import { useCommissionPolicyStore } from '../store';
import './styles.scss';

import { PlusOutlined } from '@ant-design/icons';
import { v4 as uuid } from 'uuid';
import FormPeriod from '../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { OPTIONS_STATUS_KPI, OPTIONS_TYPE_COMMISSION_POLICY } from '../../../constants/common';
import { columnRate } from '../components/columns';
import { ARRAY_FIELD_RATE, CommissionPolicyType, formatListRateForDisplay, formatListRateForSubmit } from '../utils';

const { Title, Text } = Typography;
const { Item } = Form;

const CreateModalCommissionPolicy = () => {
  const [form] = Form.useForm();
  const { actionModal, setActionModal, setInitialValue } = useCommissionPolicyStore();

  const policyType = Form.useWatch('policyType', form);
  const listPos = Form.useWatch('listPos', form);

  const [actived, setActived] = useState(OPTIONS_STATUS_KPI[0]?.value);

  const [dataSource, setDataSource] = useState<TRate[]>([
    { key: uuid(), calculateType: 'VND', isNew: true, policyType: 'PERSONAL' },
  ]);

  const { data, isLoading } = useFetch<TCreateCommissionPolicy>({
    api: () => cloneCommissionPolicy({ id: actionModal?.id as string }),
    queryKeyArr: ['get-clone-commission-policy', actionModal?.id],
    enabled: !!actionModal?.id,
  });
  const dataClonePolicy = data?.data?.data;

  const { mutateAsync: createSalePolicy, isPending } = useCreateField<TCreateCommissionPolicy>({
    apiQuery: createCommissionPolicy,
    keyOfListQuery: ['get-list-commission-policies'],
    isMessageError: false,
  });

  useEffect(() => {
    if (dataClonePolicy && actionModal?.type === 'clone') {
      const formatListRate = formatListRateForDisplay(dataClonePolicy?.listRate as TRate[]) as TRate[];

      const policyTypeValue = OPTIONS_TYPE_COMMISSION_POLICY.find(
        option => option.label === dataClonePolicy?.policyType,
      )?.value;

      const formatData = {
        ...dataClonePolicy,
        periodObj: {
          periodFrom: dataClonePolicy?.periodFrom,
          periodTo: dataClonePolicy?.periodTo,
          periodName: dataClonePolicy?.periodName,
        },
        policyType: policyTypeValue,
        code: `${dataClonePolicy?.code}-COPY`,
        isActive: Number(dataClonePolicy?.isActive),
        listPos: {
          ...dataClonePolicy?.listPos,
          label: dataClonePolicy?.listPos?.name,
          value: dataClonePolicy?.listPos?.id,
        },
        date: [dayjs(dataClonePolicy.startDate || undefined), dayjs(dataClonePolicy.endDate || undefined)],
        listRate: formatListRate as unknown as TRate[],
        commissionRateManageNVKD: dataClonePolicy?.commissionRateManage?.userNVKD,
        commissionRateManageTPBH: dataClonePolicy?.commissionRateManage?.userTPBH,
        commissionRateManageGDBH: dataClonePolicy?.commissionRateManage?.userGDBH,
        commissionRateManageGDKD: dataClonePolicy?.commissionRateManage?.userGDKD,
        commissionRateRevenueNVKD: dataClonePolicy?.commissionRateRevenue?.userNVKD,
        commissionRateRevenueTPBH: dataClonePolicy?.commissionRateRevenue?.userTPBH,
        commissionRateRevenueGDBH: dataClonePolicy?.commissionRateRevenue?.userGDBH,
        commissionRateRevenueGDKD: dataClonePolicy?.commissionRateRevenue?.userGDKD,
      };

      form.setFieldsValue(formatData);
      setActived(Number(dataClonePolicy?.isActive));
      setDataSource(formatListRate);
      setInitialValue(formatData as ICommissionPolicy);
    }
  }, [
    actionModal?.type,
    dataClonePolicy?.commissionRateManage?.userGDBH,
    dataClonePolicy?.commissionRateManage?.userGDKD,
    dataClonePolicy?.commissionRateManage?.userNVKD,
    dataClonePolicy?.commissionRateManage?.userTPBH,
    dataClonePolicy?.commissionRateRevenue?.userGDBH,
    dataClonePolicy?.commissionRateRevenue?.userGDKD,
    dataClonePolicy?.commissionRateRevenue?.userNVKD,
    dataClonePolicy?.commissionRateRevenue?.userTPBH,
    dataClonePolicy,
    form,
    setInitialValue,
  ]);

  const handleSubmit = async () => {
    const values = await form.validateFields();
    const listRate = form?.getFieldValue('listRate');
    const periodObj = form.getFieldValue('periodObj');

    const policyTypeSubmit = OPTIONS_TYPE_COMMISSION_POLICY.find(option => option.value === values?.policyType)?.label;

    const submitListRate = formatListRateForSubmit(listRate);

    const newData = {
      ...values,
      ...periodObj,
      policyType: policyTypeSubmit,
      year: Number(values?.year),
      name: values?.name?.trim() || '',
      code: values?.code?.trim() || '',
      isActive: actived,
      date: undefined,
      userNVTV: undefined,
      userNVKD: undefined,
      userTPBH: undefined,
      userGDBH: undefined,
      userGDKD: undefined,
      listRate: submitListRate,
      commissionRateManageNVKD: undefined,
      commissionRateManageTPBH: undefined,
      commissionRateManageGDBH: undefined,
      commissionRateManageGDKD: undefined,
      commissionRateRevenueNVKD: undefined,
      commissionRateRevenueTPBH: undefined,
      commissionRateRevenueGDBH: undefined,
      commissionRateRevenueGDKD: undefined,
      commissionRateManage:
        values?.policyType === CommissionPolicyType.MANAGER
          ? {
              userNVKD: values?.commissionRateManageNVKD | 0,
              userTPBH: values?.commissionRateManageTPBH | 0,
              userGDBH: values?.commissionRateManageGDBH | 0,
              userGDKD: values?.commissionRateManageGDKD | 0,
            }
          : undefined,
      commissionRateRevenue:
        values?.policyType === CommissionPolicyType.MANAGER
          ? {
              userNVKD: values?.commissionRateRevenueNVKD | 0,
              userTPBH: values?.commissionRateRevenueTPBH | 0,
              userGDBH: values?.commissionRateRevenueGDBH | 0,
              userGDKD: values?.commissionRateRevenueGDKD | 0,
            }
          : undefined,
    };

    const res = await createSalePolicy(newData);
    if (res?.data?.statusCode === '0') {
      form.resetFields();
      setActionModal({ isOpen: false, type: undefined, id: undefined });
      setInitialValue(undefined);
      setDataSource([{ key: uuid(), calculateType: 'VND', isNew: true, policyType: 'PERSONAL' }]);
    }
  };

  const handelChangeStatus = useCallback((checked: boolean) => {
    setActived(checked ? 1 : 2);
  }, []);

  const handleAddRate = () => {
    const newData = {
      ...ARRAY_FIELD_RATE.reduce((acc, field) => ({ ...acc, [field]: '' }), {}),
      key: uuid(),
      isNew: true,
      bottomPrice: dataSource[dataSource?.length - 1]?.topPrice ? dataSource[dataSource?.length - 1]?.topPrice : 0,
      calculateType: dataSource?.length
        ? dataSource[dataSource?.length - 1]?.calculateType
        : form?.getFieldValue('policyType') === 'PERSONAL'
          ? 'VND'
          : '%',
    } as unknown as TRate;
    setDataSource([...dataSource, newData]);
  };

  const handleChangePolicyType = (value: string) => {
    const newData = {
      ...ARRAY_FIELD_RATE.reduce((acc, field) => ({ ...acc, [field]: '' }), {}),
      key: uuid(),
      isNew: true,
      calculateType: value === CommissionPolicyType.MANAGER ? '%' : 'VND',
      policyType: value,
    } as unknown as TRate;
    setDataSource([newData]);
    form?.setFieldValue('listRate', [newData]);
  };

  const handleSelectListPos = (value: TPos) => {
    form.setFieldsValue({ listPos: { id: value?.id, name: value?.name, code: value?.code } });
  };

  return (
    <Spin spinning={isLoading}>
      <ModalComponent
        rootClassName="wrapper-create-sale-policy"
        title="Tạo mới bộ chỉ tiêu KPI"
        open={actionModal?.isOpen}
        onCancel={() => {
          setActionModal({ isOpen: false, type: undefined, id: undefined });
          form.resetFields();
          setInitialValue(undefined);
          setActived(1);
          setDataSource([{ key: uuid(), calculateType: 'VND', isNew: true, policyType: 'PERSONAL' }]);
        }}
        destroyOnClose
        footer={[
          <Button key="submit" type="primary" onClick={handleSubmit} loading={!!isPending}>
            Lưu
          </Button>,
        ]}
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          initialValues={{
            isActive: 1,
            policyType: 'PERSONAL',
            listRate: dataSource,
          }}
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}>
              <Title level={5}>Thông tin chung</Title>
            </Col>
            <Col xs={24} md={12}>
              <Row gutter={24}>
                <Col xs={24} md={12}>
                  <Item label="Mã bộ chỉ tiêu KPI" name="code" required>
                    <Input placeholder="Hệ thống tự động hiển thị" disabled />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item
                    label="Tên bộ chỉ tiêu KPI"
                    name="name"
                    required
                    rules={[
                      {
                        required: true,
                        validator: (_, value) => {
                          if (!value || value.trim() === '') {
                            return Promise.reject(new Error('Vui lòng nhập tên bộ chỉ tiêu'));
                          }
                          return Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder="Nhập tên bộ chỉ tiêu" maxLength={255} />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item label="Loại chỉ tiêu" name="policyType" required rules={[{ required: true }]}>
                    <Select
                      placeholder="Chọn loại chỉ tiêu"
                      allowClear
                      options={OPTIONS_TYPE_COMMISSION_POLICY}
                      onChange={handleChangePolicyType}
                    />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item label="Đơn vị bán hàng" name="listPos">
                    <SingleSelectLazy
                      apiQuery={getListPos}
                      queryKey={['get-list-pos']}
                      keysLabel={'name'}
                      placeholder="Chọn đơn vị bán hàng"
                      handleSelect={handleSelectListPos}
                      defaultValues={{
                        value: listPos?.id,
                        label: listPos?.name,
                      }}
                    />
                  </Item>
                </Col>

                <Col xs={24}>
                  <FormPeriod
                    messageValidate="Vui lòng chọn kỳ thiết lập"
                    required
                    label="Kỳ thiết lập"
                    fieldPos="listPos"
                  />
                </Col>

                <Col xs={24} md={4}>
                  <Item label="Trạng thái"></Item>
                </Col>
                <Col xs={24} md={3}>
                  <Switch value={actived === 1} onChange={handelChangeStatus} />
                </Col>
                <Col xs={24} md={6}>
                  <Text style={{ color: actived === 1 ? '#389E0D' : '#CF1322' }}>
                    {actived === 1 ? OPTIONS_STATUS_KPI[0]?.label : OPTIONS_STATUS_KPI[1]?.label}
                  </Text>
                </Col>
              </Row>
            </Col>

            {/* <Row gutter={{ md: 24, lg: 40 }}> */}
            <Col xs={24} md={24}>
              <Row gutter={{ md: 24, lg: 40 }} align={'middle'}>
                <Col span={24}>
                  <Title level={5}>Tỷ lệ hoa hồng</Title>
                </Col>
                <Col span={4}>
                  <Item name="isProgressive" valuePropName="checked" className="custom-checkbox-form">
                    <Checkbox
                      disabled={(() => {
                        const policyType = form?.getFieldValue('policyType');
                        return (
                          policyType === CommissionPolicyType.MANAGER ||
                          (dataSource[0]?.calculateType === '%' && policyType === 'PERSONAL')
                        );
                      })()}
                    >
                      Tính lũy tiến
                    </Checkbox>
                  </Item>
                </Col>
                <Col span={6}>
                  <Item name="isVAT" valuePropName="checked" className="custom-checkbox-form">
                    <Checkbox>VAT </Checkbox>
                  </Item>
                </Col>
                <Col
                  span={14}
                  style={{
                    display: 'flex',
                    justifyContent: 'right',
                    alignItems: 'center',
                  }}
                >
                  <Button onClick={() => handleAddRate()}>
                    <PlusOutlined />
                    Thêm tỷ lệ hoa hồng
                  </Button>
                </Col>
              </Row>
            </Col>
            {/* </Row> */}
            <Col xs={24} md={24} style={{ marginTop: '8px' }}>
              <div style={{ overflowX: 'auto' }}>
                <ListRate<TRate>
                  nameField="listRate"
                  columns={
                    form?.getFieldValue('policyType') === CommissionPolicyType.MANAGER
                      ? columnRate?.filter(column => column.key !== 'userNVTV')
                      : columnRate
                  }
                  dataSource={dataSource?.map(o => ({ ...o, policyType: policyType }))}
                  arrayFieldTable={ARRAY_FIELD_RATE}
                  setDataSource={setDataSource}
                />
              </div>
            </Col>
          </Row>
        </Form>
      </ModalComponent>
    </Spin>
  );
};

export default CreateModalCommissionPolicy;

import { notification, Select, Table, Typography } from 'antd';

import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row } from 'antd';
import { useEffect, useState } from 'react';
import { OPTIONS_POSITION_ACCOUNT } from '../../../../../constants/common';
import { useFetch, useUpdateField } from '../../../../../hooks';
import { getDetailAccount, putDataRolesAccount } from '../../../../../service/account';
import { DropdownRole } from './components/dropdownRole/DropdownRole';
import ModalDataRoles from './components/modalDataRoles/ModalInternal';

import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import './styles.scss';
import { Account, AccountTabProps, GroupedDataRole, Role, selectObject, DataRole } from './type';
import { checkDataRoles, groupByType, transformRoles } from './utilities';

const { Text } = Typography;

export const AccountTab = (props: AccountTabProps) => {
  const { id } = props;
  const [form] = Form.useForm();
  const [roles, setRoles] = useState<Role[]>([]);
  const [isModified, setIsModified] = useState(false);
  const [isModalOpenDataRoles, setIsModalOpenDataRoles] = useState(false);
  const [selectedDefaultRoles, setSelectedDefaultRoles] = useState<selectObject>();
  const [selectedRole, setSelectedRole] = useState<GroupedDataRole | null>(null);
  const [typeModal] = useState<'create' | 'update'>('create');
  const [confirmBackData, setConfirmBackData] = useState(false);

  const { data: dataAccount } = useFetch<Account>({
    queryKeyArr: ['tab-account', id],
    cacheTime: 100,
    api: () => id && getDetailAccount(id),
  });
  const initialValues = dataAccount?.data?.data;

  const updateDataRole = useUpdateField<Account>({
    apiQuery: putDataRolesAccount,
    keyOfListQuery: ['tab-account'],
    keyOfDetailQuery: ['tab-account', id],
  });

  // Không cần useEffect cho expandedKeys nữa

  useEffect(() => {
    const roles = initialValues?.roles;
    form.setFieldsValue(initialValues);
    if (roles) {
      setRoles(transformRoles(roles));
    }
  }, [form, initialValues, confirmBackData]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  // xử lý modal chọn vùng dữ liệu
  const handleCancelModalDataRoles = () => {
    setIsModalOpenDataRoles(false);
  };

  const handleClickRole = (role: { label: string; key: string }, index: number) => {
    if (role) {
      setRoles(
        roles.map((item, i) => (i === index ? { ...item, name: '', nameRole: role.label, code: role.key } : item)),
      );
      setIsModified(true);
    }
  };

  const handleSubmitUpdateModalDataRoles = (values: selectObject) => {
    // Nếu đã có role với type tương ứng thì cập nhật, nếu chưa có thì thêm mới
    setRoles(prevData => {
      const type = selectedRole?.type;
      const found = prevData.find(item => (item as Role & { type?: string }).type === type);
      if (found) {
        // Cập nhật dataRoles cho role này
        return prevData.map(item => {
          if ((item as Role & { type?: string }).type === type) {
            return {
              ...item,
              dataRoles: values.listCheck,
            };
          }
          return item;
        });
      } else {
        // Thêm mới role cho vùng dữ liệu này
        return [
          ...prevData,
          {
            type,
            dataRoles: values.listCheck,
            key: Math.random().toString(),
            isDelete: true,
          },
        ];
      }
    });
    setSelectedDefaultRoles(undefined);
    setSelectedRole(null);
    setIsModalOpenDataRoles(false);
    setIsModified(true);
  };

  const handleSubmitCreateModalDataRoles = (values: selectObject) => {
    setRoles(prevData =>
      prevData.map(item => {
        if (item.key === selectedRole?.key) {
          const filterData = item?.dataRoles?.filter(newItem => newItem.type !== selectedRole?.type);
          const mergedResult = [...values.listCheck, ...(filterData ? filterData : [])];
          return {
            ...item,
            dataRoles: mergedResult,
            children: groupByType(mergedResult, selectedRole?.key).sort((a, b) => a.type.localeCompare(b.type)),
          };
        } else {
          return item;
        }
      }),
    );
    setSelectedDefaultRoles(undefined);
    setSelectedRole(null);
    setIsModalOpenDataRoles(false);
    setIsModified(true);
  };

  // thêm role mới
  const handleAddNewRole = () => {
    const newData = [...roles];
    newData.push({
      name: '',
      code: '',
      dataRoles: [],
      key: Math.random().toString(),
      isDelete: true,
    });
    setRoles(newData);
    setIsModified(true);
  };

  // Lấy vùng dữ liệu từ state roles (luôn cập nhật mới nhất)
  const getAreaValue = (type: string) => {
    if (!roles) return '';
    // Tìm role có type tương ứng và lấy vùng dữ liệu
    const found = roles.find((role: unknown) => (role as { type?: string }).type === type) as
      | (Role & { dataRoles?: DataRole[] })
      | undefined;
    if (found && Array.isArray(found.dataRoles)) {
      return found.dataRoles
        .map((item: DataRole) => ('labelDataRole' in item ? (item as { labelDataRole?: string }).labelDataRole : ''))
        .join(' , ');
    }
    return '';
  };
  const dataArea: Array<{
    key: string;
    type: string;
    value: string;
    onDelete: () => void;
  }> = [
    {
      key: 'orgchart',
      type: 'Sơ đồ tổ chức',
      value: getAreaValue('INTERNAL_ORGCHART'),
      onDelete: () => {
        /* Xử lý xoá vùng dữ liệu sơ đồ tổ chức */
      },
    },
    {
      key: 'htkd',
      type: 'Đơn vị HTKD',
      value: getAreaValue('EXTERNAL_ORGCHART'),
      onDelete: () => {
        /* Xử lý xoá vùng dữ liệu HTKD */
      },
    },
    {
      key: 'project',
      type: 'Dự án',
      value: getAreaValue('PROJECT'),
      onDelete: () => {
        /* Xử lý xoá vùng dữ liệu dự án */
      },
    },
  ];

  // Hàm mở modal chọn vùng dữ liệu cho từng loại
  const handleOpenAreaModal = (type: string) => {
    // Tìm role tương ứng với type
    let foundRole = null;
    if (initialValues?.roles) {
      foundRole = initialValues.roles.find((role: unknown) => (role as { type?: string }).type === type);
    }
    // Nếu chưa có role, tạo tạm object rỗng
    const record: Role | { type: string; dataRoles: DataRole[]; area?: DataRole[]; parentCode?: string } =
      foundRole || { type, dataRoles: [] };
    // Lấy listCheck là area hoặc dataRoles nếu có, nếu không thì []
    const r = record as Role & { area?: DataRole[] };
    const listCheck: DataRole[] = Array.isArray(r.area) ? r.area : Array.isArray(r.dataRoles) ? r.dataRoles : [];
    setSelectedDefaultRoles({ type, listCheck });
    setSelectedRole(record as GroupedDataRole);
    setIsModalOpenDataRoles(true);
    // typeModal luôn là 'update' khi sửa vùng dữ liệu
  };

  // --- Columns cho bảng vùng dữ liệu ---
  const columnsArea = [
    {
      title: 'Loại vùng dữ liệu',
      dataIndex: 'type',
      key: 'type',
      width: '30%',
    },
    {
      title: 'Vùng dữ liệu',
      dataIndex: 'value',
      key: 'value',
      render: (value: string, record: { key: string }) => {
        // Xác định type tương ứng để truyền vào handleOpenAreaModal
        let type = '';
        if (record.key === 'orgchart') type = 'INTERNAL_ORGCHART';
        else if (record.key === 'htkd') type = 'EXTERNAL_ORGCHART';
        else if (record.key === 'project') type = 'PROJECT';
        return value ? (
          <span style={{ color: '#1677ff', cursor: 'pointer' }} onClick={() => handleOpenAreaModal(type)}>
            {value}
          </span>
        ) : (
          <span style={{ color: '#1677ff', cursor: 'pointer' }} onClick={() => handleOpenAreaModal(type)}>
            Chọn vùng dữ liệu
          </span>
        );
      },
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_: unknown, record: { onDelete: () => void }) => (
        <Button type="text" danger onClick={record.onDelete}>
          Xoá
        </Button>
      ),
    },
  ];

  // --- Columns cho bảng vai trò ---
  const columnsRoles = [
    {
      title: 'Tên vai trò',
      dataIndex: 'name',
      key: 'name',
      render: (_: unknown, record: Role, index: number) => {
        if (!record.isAdmSource) {
          return 'name' in record ? (
            <DropdownRole onClick={handleClickRole} index={index} roles={roles} defaultValue={record?.name} />
          ) : (
            ''
          );
        } else {
          return record.name;
        }
      },
    },
    {
      title: 'Mã vai trò',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_: unknown, record: Role) => (
        <Button
          type="text"
          danger
          onClick={() => {
            const newData = roles.filter(item => JSON.stringify(item) !== JSON.stringify(record));
            setRoles(newData);
            setIsModified(true);
          }}
        >
          Xoá
        </Button>
      ),
    },
  ];

  const handleSubmitAccount = async () => {
    const dataSubmit = {
      id: form.getFieldValue('id'),
      roles: roles.map(item => ({
        name: item.name || item.nameRole,
        code: item.code,
        dataRoles: item.dataRoles,
      })),
    };
    if (checkDataRoles(dataSubmit)) {
      updateDataRole.mutate(dataSubmit);
      setIsModified(false);
    } else {
      notification.error({ message: 'Vui lòng chọn vùng dữ liệu cho tất cả các vai trò' });
    }
  };
  const handleCancel = () => {
    form.setFieldsValue(initialValues);
    setConfirmBackData(prev => !prev);
    setIsModified(false);
  };

  return (
    <div className="wrapper-account-tab">
      <Form className="account-internal" layout="vertical" form={form} onFinish={handleSubmitAccount}>
        <h3 className="title-info">Thông tin tài khoản</h3>
        <Row gutter={48}>
          <Col md={12} xs={24}>
            <Row
              gutter={{
                xs: 8,
                md: 12,
              }}
            >
              <Col md={12} xs={20}>
                <Form.Item label="Loại người dùng" name="isAdmin">
                  <Select options={OPTIONS_POSITION_ACCOUNT} disabled />
                </Form.Item>
              </Col>
              <Col md={12} xs={20}>
                <Form.Item label="Tên tài khoản" name="username">
                  <Input disabled />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
        <Form.Item
          className="status-account-internal"
          label="Trạng thái"
          layout="horizontal"
          labelCol={{ span: 2 }}
          labelAlign="left"
          shouldUpdate
        >
          {() => {
            const isActive = form.getFieldValue('isActive');
            return <Text type={isActive ? 'success' : 'danger'}>{isActive ? 'Hoạt động' : 'Vô hiệu'}</Text>;
          }}
        </Form.Item>

        {/* Bảng vùng dữ liệu */}
        <div className="table-area">
          <h3 className="title-info">Vùng dữ liệu</h3>
          <Table columns={columnsArea} dataSource={dataArea} pagination={false} rowKey="key" />
        </div>

        {/* Bảng vai trò */}
        <div className="table-roles">
          <h3 className="title-info">Vai trò</h3>
          <Button
            type="dashed"
            className="btn-add-roles"
            icon={<PlusOutlined />}
            onClick={handleAddNewRole}
            style={{ marginBottom: 12 }}
          >
            Thêm vai trò
          </Button>
          <Table columns={columnsRoles} dataSource={roles} pagination={false} rowKey="key" />
        </div>
      </Form>
      <ModalDataRoles
        handleCancel={handleCancelModalDataRoles}
        isOpen={isModalOpenDataRoles}
        handleOk={typeModal === 'create' ? handleSubmitCreateModalDataRoles : handleSubmitUpdateModalDataRoles}
        selectedDefaultRoles={selectedDefaultRoles}
        typeModal={typeModal}
      />
      {isModified && <ButtonOfPageDetail handleSubmit={handleSubmitAccount} handleCancel={handleCancel} />}
    </div>
  );
};

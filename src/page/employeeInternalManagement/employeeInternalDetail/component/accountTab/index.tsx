import { notification, Select, Table, Typography } from 'antd';

import { PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, Row } from 'antd';
import { useEffect, useState } from 'react';
import { OPTIONS_POSITION_ACCOUNT } from '../../../../../constants/common';
import { useFetch, useUpdateField } from '../../../../../hooks';
import {
  getDataRolesAccount,
  getDetailAccount,
  putFuncRolesAccount,
  addDataRoles,
} from '../../../../../service/account';
import { DropdownRole } from './components/dropdownRole/DropdownRole';
import ModalDataRoles from './components/modalDataRoles/ModalInternal';

import ButtonOfPageDetail from '../../../../../components/button/buttonOfPageDetail';
import './styles.scss';
import { Account, AccountTabProps, GroupedDataRole, Role, selectObject, DataRole, DataRoleItem } from './type';
import { groupByType, transformRoles } from './utilities';
import { v4 as uuid } from 'uuid';
const { Text } = Typography;

export const AccountTab = (props: AccountTabProps) => {
  const { id } = props;
  const [form] = Form.useForm();
  const [roles, setRoles] = useState<Role[]>([]);
  const [isModified, setIsModified] = useState(false);
  const [isModalOpenDataRoles, setIsModalOpenDataRoles] = useState(false);
  const [selectedDefaultRoles, setSelectedDefaultRoles] = useState<selectObject>();
  const [selectedRole, setSelectedRole] = useState<GroupedDataRole | null>(null);
  const [typeModal, setTypeModal] = useState<'create' | 'update'>('create');
  const [confirmBackData, setConfirmBackData] = useState(false);
  // State để lưu trữ các thay đổi tạm thời cho data roles
  const [tempDataRoles, setTempDataRoles] = useState<DataRoleItem[]>([]);

  const { data: dataAccount } = useFetch<Account>({
    queryKeyArr: ['tab-account', id],
    cacheTime: 100,
    api: () => id && getDetailAccount(id),
  });

  const { data: dataRoles } = useFetch<Account>({
    queryKeyArr: ['data-roles-account', id],
    cacheTime: 100,
    api: () => id && getDataRolesAccount(id),
  });
  const initialValues = dataAccount?.data?.data;
  const initialDataRoles = dataRoles?.data?.data;
  const updateFuncRole = useUpdateField<Account>({
    apiQuery: putFuncRolesAccount,
    keyOfListQuery: ['tab-account'],
    keyOfDetailQuery: ['tab-account', id],
  });

  const updateDataRoles = useUpdateField<unknown>({
    apiQuery: addDataRoles,
    keyOfListQuery: ['data-roles-account'],
    keyOfDetailQuery: ['data-roles-account', id],
  });

  // Không cần useEffect cho expandedKeys nữa

  useEffect(() => {
    const roles = initialValues?.roles;
    form.setFieldsValue(initialValues);
    if (roles) {
      setRoles(transformRoles(roles));
    }
  }, [form, initialValues, confirmBackData]);

  // Khởi tạo tempDataRoles từ initialDataRoles
  useEffect(() => {
    if (initialDataRoles && Array.isArray(initialDataRoles)) {
      setTempDataRoles(initialDataRoles as DataRoleItem[]);
    }
  }, [initialDataRoles]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  // xử lý modal chọn vùng dữ liệu
  const handleCancelModalDataRoles = () => {
    setIsModalOpenDataRoles(false);
  };

  const handleClickRole = (role: { label: string; key: string }, index: number) => {
    if (role) {
      setRoles(
        roles.map((item, i) => (i === index ? { ...item, name: '', nameRole: role.label, code: role.key } : item)),
      );
      setIsModified(true);
    }
  };

  const handleSubmitUpdateModalDataRoles = (values: selectObject) => {
    const type = selectedRole?.type;
    if (!type) return;

    // Tạo dữ liệu để gửi lên API theo cấu trúc yêu cầu
    const newDataRoles = values.listCheck.map(dataRole => ({
      account: id || '',
      companyCode: dataRole.code || '',
      companyName: dataRole.name || '',
      dataType: type,
    }));

    // Lấy dữ liệu hiện tại và cập nhật
    const currentDataRoles = tempDataRoles.filter(item => item.dataType !== type);
    const updatedDataRoles = [
      ...currentDataRoles.map(item => ({
        account: item.account,
        companyCode: item.companyCode,
        companyName: item.companyName,
        dataType: item.dataType,
      })),
      ...newDataRoles,
    ];

    // Gọi API để cập nhật
    updateDataRoles.mutate(updatedDataRoles, {
      onSuccess: () => {
        // Cập nhật tempDataRoles với dữ liệu mới
        setTempDataRoles(prevData => {
          const filteredData = prevData.filter(item => item.dataType !== type);
          const newItems: DataRoleItem[] = values.listCheck.map(dataRole => ({
            // chartNodeId: null,
            dataType: type,
            // active: true,
            // softDelete: false,
            // _id: Math.random().toString(),
            // id: Math.random().toString(),
            account: id || '',
            companyCode: dataRole.code || '',
            companyName: dataRole.name || '',
            // createdDate: new Date().toISOString(),
            // modifiedDate: new Date().toISOString(),
            __v: 0,
          }));
          return [...filteredData, ...newItems];
        });

        setSelectedDefaultRoles(undefined);
        setSelectedRole(null);
        setIsModalOpenDataRoles(false);
        setIsModified(false); // Đã save thành công nên không còn modified
      },
      onError: error => {
        console.error('Error updating data roles:', error);
        notification.error({ message: 'Có lỗi xảy ra khi cập nhật vùng dữ liệu' });
      },
    });
  };

  const handleSubmitCreateModalDataRoles = (values: selectObject) => {
    setRoles(prevData =>
      prevData.map(item => {
        if (item.key === selectedRole?.key) {
          const filterData = item?.dataRoles?.filter(newItem => newItem.type !== selectedRole?.type);
          const mergedResult = [...values.listCheck, ...(filterData ? filterData : [])];
          return {
            ...item,
            dataRoles: mergedResult,
            children: groupByType(mergedResult, selectedRole?.key).sort((a, b) => a.type.localeCompare(b.type)),
          };
        } else {
          return item;
        }
      }),
    );
    setSelectedDefaultRoles(undefined);
    setSelectedRole(null);
    setIsModalOpenDataRoles(false);
    setIsModified(true);
  };

  // thêm role mới
  const handleAddNewRole = () => {
    const newData = [...roles];
    newData.push({
      name: '',
      code: '',
      key: uuid(),
      isDelete: true,
    });
    setRoles(newData);
    setIsModified(true);
  };

  // Lấy vùng dữ liệu từ tempDataRoles (bao gồm cả thay đổi tạm thời)
  const getAreaValue = (type: string) => {
    if (!tempDataRoles || !Array.isArray(tempDataRoles)) return '';

    // Lọc các item có dataType tương ứng
    const matchingItems = tempDataRoles.filter((item: DataRoleItem) => item.dataType === type);

    if (matchingItems.length > 0) {
      return matchingItems
        .map((item: DataRoleItem) => {
          // Tạo label từ companyCode và companyName
          if (item.companyCode && item.companyName) {
            return `${item.companyCode} - ${item.companyName}`;
          }
          // Fallback nếu thiếu thông tin
          return item.companyName || item.companyCode || '';
        })
        .filter(label => label.trim() !== '') // Lọc bỏ label rỗng
        .join(', ');
    }
    return '';
  };

  // Hàm helper để xóa vùng dữ liệu và gọi API
  const handleDeleteAreaData = (dataType: string) => {
    // Lọc bỏ dữ liệu có dataType cần xóa
    const updatedDataRoles = tempDataRoles
      .filter(item => item.dataType !== dataType)
      .map(item => ({
        account: item.account,
        companyCode: item.companyCode,
        companyName: item.companyName,
        dataType: item.dataType,
      }));

    // Gọi API để cập nhật
    updateDataRoles.mutate(updatedDataRoles, {
      onSuccess: () => {
        // Cập nhật tempDataRoles
        setTempDataRoles(prevData => prevData.filter(item => item.dataType !== dataType));
        setIsModified(false); // Đã save thành công
      },
      onError: error => {
        console.error('Error deleting data roles:', error);
        notification.error({ message: 'Có lỗi xảy ra khi xóa vùng dữ liệu' });
      },
    });
  };

  const dataArea: Array<{
    key: string;
    type: string;
    value: string;
    onDelete: () => void;
  }> = [
    {
      key: 'orgchart',
      type: 'Sơ đồ tổ chức',
      value: getAreaValue('INTERNAL_ORGCHART'),
      onDelete: () => {
        handleDeleteAreaData('INTERNAL_ORGCHART');
      },
    },
    {
      key: 'htkd',
      type: 'Đơn vị HTKD',
      value: getAreaValue('EXTERNAL_ORGCHART'),
      onDelete: () => {
        handleDeleteAreaData('EXTERNAL_ORGCHART');
      },
    },
    {
      key: 'project',
      type: 'Dự án',
      value: getAreaValue('PROJECT'),
      onDelete: () => {
        handleDeleteAreaData('PROJECT');
      },
    },
  ];

  // Hàm mở modal chọn vùng dữ liệu cho từng loại
  const handleOpenAreaModal = (type: string) => {
    // Lấy dữ liệu từ tempDataRoles có dataType tương ứng
    const currentDataRoles: DataRole[] = [];

    if (tempDataRoles && Array.isArray(tempDataRoles)) {
      const matchingItems = tempDataRoles.filter((item: DataRoleItem) => item.dataType === type);

      // Chuyển đổi từ DataRoleItem sang DataRole format
      matchingItems.forEach(item => {
        currentDataRoles.push({
          code: item.companyCode,
          name: item.companyName,
          type: item.dataType,
          isDelete: true, // Có thể xóa được
        });
      });
    }

    // Tạo object record để truyền vào modal
    const record: GroupedDataRole = {
      type,
      parentCode: '', // Không cần parentCode cho trường hợp này
      isChildren: false,
      area: currentDataRoles.map(dataRole => ({
        labelDataRole: `${dataRole.code} - ${dataRole.name}`,
        name: dataRole.name || '',
        code: dataRole.code || '',
        type: dataRole.type || type,
        isDelete: dataRole.isDelete,
      })),
    };

    setSelectedDefaultRoles({ type, listCheck: currentDataRoles });
    setSelectedRole(record);
    setIsModalOpenDataRoles(true);
    setTypeModal('update'); // Luôn là 'update' khi chọn vùng dữ liệu từ bảng
  };

  // --- Columns cho bảng vùng dữ liệu ---
  const columnsArea = [
    {
      title: 'Loại vùng dữ liệu',
      dataIndex: 'type',
      key: 'type',
      width: '30%',
    },
    {
      title: 'Vùng dữ liệu',
      dataIndex: 'value',
      key: 'value',
      render: (value: string, record: { key: string }) => {
        // Xác định type tương ứng để truyền vào handleOpenAreaModal
        let type = '';
        if (record.key === 'orgchart') type = 'INTERNAL_ORGCHART';
        else if (record.key === 'htkd') type = 'EXTERNAL_ORGCHART';
        else if (record.key === 'project') type = 'PROJECT';
        return value ? (
          <span style={{ color: '#1677ff', cursor: 'pointer' }} onClick={() => handleOpenAreaModal(type)}>
            {value}
          </span>
        ) : (
          <span style={{ color: '#1677ff', cursor: 'pointer' }} onClick={() => handleOpenAreaModal(type)}>
            Chọn vùng dữ liệu
          </span>
        );
      },
    },
  ];

  // --- Columns cho bảng vai trò ---
  const columnsRoles = [
    {
      title: 'Tên vai trò',
      dataIndex: 'name',
      key: 'name',
      width: '30%',
      render: (_: unknown, record: Role, index: number) => {
        if (!record.isAdmSource) {
          return 'name' in record ? (
            <DropdownRole onClick={handleClickRole} index={index} roles={roles} defaultValue={record?.name} />
          ) : (
            ''
          );
        } else {
          return record.name;
        }
      },
    },
    {
      title: 'Mã vai trò',
      dataIndex: 'code',
      key: 'code',
    },
    {
      title: '',
      key: 'action',
      width: '10%',
      render: (_: unknown, record: Role) => (
        <Button
          type="text"
          danger
          onClick={() => {
            const newData = roles.filter(item => JSON.stringify(item) !== JSON.stringify(record));
            setRoles(newData);
            setIsModified(true);
          }}
        >
          Xoá
        </Button>
      ),
    },
  ];

  const handleSubmitAccount = async () => {
    const dataSubmit = {
      id: form.getFieldValue('id'),
      roles: roles.map(item => ({
        name: item.name || item.nameRole,
        code: item.code,
      })),
    };

    updateFuncRole.mutate(dataSubmit);
    setIsModified(false);
  };
  const handleCancel = () => {
    form.setFieldsValue(initialValues);
    setConfirmBackData(prev => !prev);
    // Reset tempDataRoles về trạng thái ban đầu
    if (initialDataRoles && Array.isArray(initialDataRoles)) {
      setTempDataRoles(initialDataRoles as DataRoleItem[]);
    }
    setIsModified(false);
  };

  return (
    <div className="wrapper-account-tab">
      <Form className="account-internal" layout="vertical" form={form} onFinish={handleSubmitAccount}>
        <h3 className="title-info">Thông tin tài khoản</h3>
        <Row gutter={48}>
          <Col md={12} xs={24}>
            <Row
              gutter={{
                xs: 8,
                md: 12,
              }}
            >
              <Col md={12} xs={20}>
                <Form.Item label="Loại người dùng" name="isAdmin">
                  <Select options={OPTIONS_POSITION_ACCOUNT} disabled />
                </Form.Item>
              </Col>
              <Col md={12} xs={20}>
                <Form.Item label="Tên tài khoản" name="username">
                  <Input disabled />
                </Form.Item>
              </Col>
            </Row>
          </Col>
        </Row>
        <Form.Item
          className="status-account-internal"
          label="Trạng thái"
          layout="horizontal"
          labelCol={{ span: 2 }}
          labelAlign="left"
          shouldUpdate
        >
          {() => {
            const isActive = form.getFieldValue('isActive');
            return <Text type={isActive ? 'success' : 'danger'}>{isActive ? 'Hoạt động' : 'Vô hiệu'}</Text>;
          }}
        </Form.Item>

        {/* Bảng vùng dữ liệu */}
        <div className="table-area">
          <h3 className="title-info">Vùng dữ liệu</h3>
          <Table columns={columnsArea} dataSource={dataArea} pagination={false} rowKey="key" />
        </div>

        {/* Bảng vai trò */}
        <div className="table-roles">
          <h3 className="title-info">Vai trò</h3>
          <Button
            type="dashed"
            className="btn-add-roles"
            icon={<PlusOutlined />}
            onClick={handleAddNewRole}
            style={{ marginBottom: 12 }}
          >
            Thêm vai trò
          </Button>
          <Table columns={columnsRoles} dataSource={roles} pagination={false} rowKey="key" />
        </div>
      </Form>
      <ModalDataRoles
        handleCancel={handleCancelModalDataRoles}
        isOpen={isModalOpenDataRoles}
        handleOk={typeModal === 'create' ? handleSubmitCreateModalDataRoles : handleSubmitUpdateModalDataRoles}
        selectedDefaultRoles={selectedDefaultRoles}
        typeModal={typeModal}
      />
      {isModified && <ButtonOfPageDetail handleSubmit={handleSubmitAccount} handleCancel={handleCancel} />}
    </div>
  );
};

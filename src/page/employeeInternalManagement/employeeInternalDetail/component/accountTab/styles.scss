.account-internal {
  .title-info {
    margin: 8px 0 16px 0;
  }
  .status {
    display: flex;
    gap: 30px;
    align-items: center;
  }
  .color-status {
    color: #389e0d;
    font-size: 14px;
  }

  .rolesSection {
    h3 {
      margin-bottom: 20px;
      font-size: 16px;
      font-weight: 600;
    }
  }
  .table-roles {
    .ant-table-wrapper {
      margin-bottom: 32px;
    }
    .ant-table-summary {
      .ant-table-cell {
        padding: 8px 12px;
      }
    }
    .ant-table-row-level-0 {
      .ant-table-cell-row-hover {
        background: none !important;
      }
    }
    .btn-add-roles {
      width: 100%;
    }
  }
  .status-account-internal {
    .ant-form-item-label {
      flex: 0 0 30% !important;
    }
    &.ant-form-item {
      margin-bottom: 0;
    }
  }
}

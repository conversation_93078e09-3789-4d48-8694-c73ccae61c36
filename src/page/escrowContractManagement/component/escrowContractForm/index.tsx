import { Button, Col, Form, Input, Modal, Row, Select, Typography, UploadFile } from 'antd';
import React, { useCallback, useEffect, useMemo, useState } from 'react';
import UploadFileDepositContract from '../UploadFileDepositContract';
import FPTLogo from '../../../../assets/images/FPT_logo.png';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { useFetch } from '../../../../hooks';
import { Bank, BankOption } from '../../../../types/bookingRequest';
import { getBanks } from '../../../../service/bank';
import dayjs, { Dayjs } from 'dayjs';
import './styles.scss';
import { EscrowContract, Installment } from '../../../../types/depositContract';
import { formatNumber, handleKeyDownEnterNumber } from '../../../../utilities/regex';
import { ORGCHART_TYPE } from '../../../../constants/common';
import {
  getListOrgchartExternal,
  getListOrgchartInternal,
  getListOrgchartPartner,
  getListSalesPolicy,
} from '../../../../service/depositContract';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import InfoEscrow from './InfoEscrow';
import EscrowPaymentPlan from './EscrowPaymentPlan';
import EscrowProduct from './EscrowProduct';
import { useNavigate } from 'react-router-dom';
import { ESCROW_CONTRACT } from '../../../../configs/path';

const { Title, Text } = Typography;
const { Option } = Select;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

interface EscrowContractFormProps {
  form?: any;
  initialValues?: EscrowContract;
  onFinish?: (values: EscrowContract) => void;
  loading?: boolean;
}

const EscrowContractForm: React.FC<EscrowContractFormProps> = ({ form, initialValues, onFinish }) => {
  const navigate = useNavigate();
  const orgchartType = Form.useWatch(['orgchart', 'type'], form);
  const depositAmount = Form.useWatch('depositAmount', form) || 0;
  const bankInfoOptions = Form.useWatch('bankAccount', form) || [];

  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [depositDates, setDepositDates] = useState<[Dayjs | null, Dayjs | null]>([dayjs(), null]);

  const [totalPaymentError, setTotalPaymentError] = useState<string | null>(null);
  const [installments, setInstallments] = useState<Installment[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [selectedPropertyUnitIds, setSelectedPropertyUnitIds] = useState<string[]>([]);
  const [removedProductInfo, setRemovedProductInfo] = useState<{ salesProgramId: string; timestamp: number } | null>(
    null,
  );

  const [orgchartId, setOrgchartId] = useState<string | undefined>('');

  const { data: dataBanks, isLoading: isLoadingBanks } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
  });
  const banks = dataBanks?.data?.data || [];

  useEffect(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;

    // Tính toán distributionChannel mặc định dựa trên loại đơn vị
    const defaultDistributionChannelCode = currentType === ORGCHART_TYPE.INTERNAL ? 10 : 13;

    form.setFieldsValue({
      orgchart: {
        type: currentType,
        id: undefined,
        name: undefined,
        code: undefined,
        bpID: undefined,
        taxCode: undefined,
        email: undefined,
      },
      // Reset field "Hợp tác với đơn vị bán hàng" khi thay đổi loại đơn vị
      orgchartPartnerId: undefined,
      // Set distributionChannel mặc định dựa trên loại đơn vị
      distributionChannel: defaultDistributionChannelCode,
    });

    // Reset state khi thay đổi loại đơn vị
    setOrgchartId(undefined);
  }, [orgchartType, form]);

  useEffect(() => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    if (currentMainBankId) {
      const [bankCode, index] = currentMainBankId.split('-');
      const bankAccount = form.getFieldValue('bankAccount') || [];
      const selectedBank = bankAccount[parseInt(index)];

      // Chỉ clear mainBankId nếu tài khoản chính không còn tồn tại hoặc bankCode không khớp
      if (!selectedBank || selectedBank.bankCode !== bankCode) {
        form.setFieldsValue({
          mainBankId: undefined,
          mainBank: {
            code: '',
            name: '',
            accountNumber: '',
            beneciary: '',
          },
        });
      }
    }
  }, [bankInfoOptions, form]);

  // Khởi tạo giá trị mặc định cho distributionChannel
  useEffect(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;
    const defaultDistributionChannelCode = currentType === ORGCHART_TYPE.INTERNAL ? '10' : '13';

    const currentDistributionChannel = form.getFieldValue('distributionChannel');
    if (!currentDistributionChannel) {
      form.setFieldsValue({
        distributionChannel: defaultDistributionChannelCode,
      });
    }
  }, []);

  useEffect(() => {
    if (!depositAmount && depositAmount <= 0) {
      setInstallments([]);
      setTotalPaymentError(null);
    } else {
      const updatedInstallments = installments.map((inst: Installment) => ({
        ...inst,
        // convertedAmount: calculateConvertedAmount(inst.value || 0, inst.type, depositAmount),
      }));
      setInstallments(updatedInstallments);
      validateTotalPayment(updatedInstallments);
    }
  }, [depositAmount]);

  const defaultBankOptions: BankOption[] = useMemo(
    () =>
      bankInfoOptions
        .filter((bank: Bank) => bank?.bankCode && bank?.bankName)
        .map((bank: Bank, index: number) => ({
          value: `${bank.bankCode}-${index}`,
          label: `${bank?.bankName || 'N/A'} - ${bank?.accountNumber || ''} - ${bank?.beneciary || ''}`,
          originalBankCode: bank.bankCode,
        })),
    [bankInfoOptions],
  );

  // Tính toán kênh phân phối mặc định dựa trên loại đơn vị
  const defaultDistributionChannel = useMemo(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;
    return currentType === ORGCHART_TYPE.INTERNAL
      ? { code: '10', name: 'Kênh bán nội bộ' }
      : { code: '13', name: 'Kênh bán bán lẻ' }; // Sửa tên cho đúng
  }, [orgchartType]);

  const handleRemoveBankAccount = (name: number) => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    const bankAccount = form.getFieldValue('bankAccount');
    const removedBank = bankAccount[name];

    // Kiểm tra xem tài khoản bị xóa có phải là tài khoản chính không
    const isMainBankRemoved = currentMainBankId === `${removedBank?.bankCode}-${name}`;

    if (isMainBankRemoved) {
      // Nếu tài khoản chính bị xóa, đặt lại mainBankId và mainBank
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: {
          code: '',
          name: '',
          accountNumber: '',
          beneciary: '',
        },
      });
    } else if (currentMainBankId) {
      // Nếu tài khoản chính không bị xóa, cập nhật lại mainBankId để phản ánh chỉ số mới
      const [bankCode, currentIndex] = currentMainBankId.split('-');
      const currentIndexNum = parseInt(currentIndex);

      // Nếu chỉ số của tài khoản chính lớn hơn chỉ số bị xóa, giảm chỉ số đi 1
      if (currentIndexNum > name) {
        const newMainBankId = `${bankCode}-${currentIndexNum - 1}`;
        form.setFieldsValue({
          mainBankId: newMainBankId,
        });
      }
    }
  };

  const handleSelectBankInfo = (value: string) => {
    const selectedOption = defaultBankOptions.find(option => option.value === value);
    const selectedBank = bankInfoOptions.find((bank: Bank) => bank.bankCode === selectedOption?.originalBankCode);
    if (selectedBank) {
      form.setFieldsValue({
        mainBankId: value,
        mainBank: {
          code: selectedBank.bankCode || '',
          name: selectedBank.bankName || '',
          accountNumber: selectedBank.accountNumber || '',
          beneciary: selectedBank.beneciary || '',
        },
      });
    } else {
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: {
          code: selectedBank.bankCode || '',
          name: '',
          accountNumber: '',
          beneciary: '',
        },
      });
    }
  };

  // function để tính ngày đến hạn
  const calculateExpiredDate = (signedDate: dayjs.Dayjs, expiredDays: number): string => {
    if (!signedDate || !expiredDays || expiredDays <= 0) return '';
    return signedDate.add(expiredDays, 'day').format('DD/MM/YYYY');
  };

  // Hàm tính convertedAmount
  const calculateConvertedAmount = (value: number, type: 'percent' | 'currency', depositAmount: number) => {
    if (!value || !depositAmount || value <= 0 || depositAmount <= 0) return 0;

    let result: number;
    if (type === 'percent') {
      result = (value / 100) * depositAmount; // % → VNĐ
    } else {
      result = (value / depositAmount) * 100; // VNĐ → %
    }

    return Number(Math.round(result * 100) / 100);
  };

  // Hàm tính tổng số tiền thanh toán (chỉ tính các giá trị VND)
  const calculateTotalPayment = (installments: Installment[], depositAmount: number) => {
    return installments.reduce((total, inst) => {
      if (inst.type === 'currency' && inst.value) {
        return total + inst.value;
      } else if (inst.type === 'percent' && inst.value) {
        return total + (inst.value / 100) * depositAmount;
      }
      return total;
    }, 0);
  };

  // Hàm kiểm tra tổng số tiền thanh toán
  const validateTotalPayment = (installments: Installment[]) => {
    const total = calculateTotalPayment(installments, depositAmount);
    if (total > depositAmount) {
      setTotalPaymentError('Vui lòng nhập tổng giá trị thanh toán nhỏ hơn số tiền ký quỹ');
      return false;
    } else {
      setTotalPaymentError(null);
      return true;
    }
  };

  const handleCreateEscrowContract = async (values: any) => {
    // Validate installments trước khi submit
    const invalidInstallments = installments.filter((inst, index) => {
      // Kiểm tra expiredDays
      if (!inst.expiredDays || inst.expiredDays <= 0) {
        return true;
      }

      // Kiểm tra giá trị thanh toán
      if (!inst.value || inst.value <= 0 || !Number.isInteger(Number(inst.value))) {
        return true;
      }

      // Kiểm tra thứ tự expiredDays
      if (index > 0) {
        const prevInstallment = installments[index - 1];
        if (prevInstallment.expiredDays && inst.expiredDays <= prevInstallment.expiredDays) {
          return true;
        }
      }

      return false;
    });

    if (invalidInstallments.length > 0) {
      // Set errors cho các installments không hợp lệ
      invalidInstallments.forEach(inst => {
        const installmentIndex = installments.findIndex(i => i.id === inst.id);

        if (!inst.expiredDays || inst.expiredDays <= 0) {
          form.setFields([
            {
              name: ['installments', installmentIndex, 'expiredDays'],
              errors: ['Vui lòng nhập số ngày thanh toán'],
            },
          ]);
        }
      });
      return;
    }

    // Xác định distributionChannel: sử dụng giá trị đã chọn hoặc giá trị mặc định
    const finalDistributionChannel = values.distributionChannel || defaultDistributionChannel.code;

    const payload = {
      projectId: values?.projectId,
      orgchart: {
        id: orgchartId || '',
        code: values.orgchart?.code || '',
        name: values.orgchart?.name || '',
        taxCode: values.orgchart?.taxCode,
        bpID: values.orgchart?.bpID || '',
        email: values.orgchart?.email,
        type: values.orgchart?.type,
      },
      orgchartPartnerId: values?.orgchartPartnerId,
      bank: {
        code: values.mainBank?.code,
        name: values.mainBank?.name,
        accountNumber: values.mainBank?.accountNumber,
        beneficiary: values.mainBank?.beneciary,
      },
      depositForm: values.depositForm,
      depositTime: values.depositTime,
      depositAmount: values.depositAmount,
      salePolicyId: values.salePolicyId || '',
      startDate: depositDates[0],
      expiredDate: depositDates[1],
      type: values.type,
      POnumber: values.POnumber,
      distributionChannel: finalDistributionChannel,
      division: values.division ? Number(values.division) : undefined,
      propertyUnitIds: selectedPropertyUnitIds || [],
      installments: installments.map(inst => ({
        id: inst.id,
        name: inst.name,
        type: inst.type,
        value: inst.value,
        expiredDays: inst.expiredDays,
        description: inst.description,
        expiredDateType: inst.expiredDateType,
      })),
      files: fileList.map(file => ({
        name: file.name,
        url: file.url || '',
        absoluteUrl: file.absoluteUrl || '',
      })),
    };

    onFinish?.(payload);
  };

  const handleSelectOrgchartInternal = (values: any) => {
    setOrgchartId(values?.id || undefined);
    form.setFieldsValue({
      orgchart: {
        id: values?.id || undefined,
        name: values?.nameVN || undefined,
        code: values?.code || undefined,
        taxCode: values?.taxCode || undefined,
        email: values?.email || undefined,
      },
      // Reset đơn vị bán hàng khi thay đổi hoặc clear đơn vị chính
      orgchartPartnerId: undefined,
    });
  };

  const handleSelectOrgchartExternal = (values: any) => {
    setOrgchartId(values?.id || undefined);
    form.setFieldsValue({
      orgchart: {
        id: values?.id || undefined,
        code: values?.partnershipCode || undefined,
        name: values?.partnershipName || undefined,
        taxCode: values?.taxCode || undefined,
        bpID: values?.businessPartnerCode || undefined,
        email: values?.email || undefined,
      },

      // Reset đơn vị bán hàng khi thay đổi hoặc clear đơn vị chính
      orgchartPartnerId: undefined,
    });
  };

  const handleSelectDistributionChannel = (values: any) => {
    form.setFieldsValue({ distributionChannel: values?.code });
  };

  const handleSelectDivision = (values: any) => {
    form.setFieldsValue({ division: values?.code });
  };

  const handleSelectDepositProject = (values: any) => {
    form.setFieldsValue({ projectId: values?.id });
  };

  const handleSelectOrgchartPartnerInternal = (values: any) => {
    form.setFieldsValue({ orgchartPartnerId: values?.id });
  };

  const handleSelectOrgchartPartnerExternal = (values: any) => {
    form.setFieldsValue({ orgchartPartnerId: values?.id });
  };

  const handleSelectSalePolicy = (values: any) => {
    form.setFieldsValue({ salePolicyId: values?.id });
  };

  const handleCancel = useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        okText: 'Đồng ý',
        onOk: () => {
          navigate(ESCROW_CONTRACT);
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      navigate(ESCROW_CONTRACT);
    }
  }, [form, navigate]);

  return (
    <div className="booking-ticket-detail">
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>{`Hợp đồng ký quỹ ${initialValues?.name || ''}`}</Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{initialValues?.name || ''}</span>
          </Text>
        </div>

        <div className="project-image">
          <img
            src={initialValues?.imageUrl ? `${import.meta.env.VITE_S3_IMAGE_URL}/${initialValues?.imageUrl}` : FPTLogo}
            alt="Project"
          />
        </div>
      </div>
      <Form
        form={form}
        layout="vertical"
        // initialValues={initialValues}
        onFinish={handleCreateEscrowContract}
        style={{ marginTop: 32 }}
      >
        <Row gutter={32}>
          <Col span={14}>
            {/* Thông tin khách hàng */}
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label="Loại đơn vị"
                  name={['orgchart', 'type']}
                  rules={[{ required: true, message: 'Vui lòng chọn loại đơn vị' }]}
                >
                  <Select placeholder="Chọn loại đơn vị" defaultValue={ORGCHART_TYPE.EXTERNAL}>
                    <Option value={ORGCHART_TYPE.EXTERNAL}>Đơn vị ĐTHT</Option>
                    <Option value={ORGCHART_TYPE.INTERNAL}>Đơn vị nội bộ</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col span={12}>
                {orgchartType === ORGCHART_TYPE.INTERNAL ? (
                  <Form.Item
                    label="Tên đơn vị"
                    name={['orgchart', 'name']}
                    key="internal"
                    rules={[{ required: true, message: 'Vui lòng chọn tên đơn vị' }]}
                  >
                    <SingleSelectLazy
                      key="internal-select"
                      apiQuery={getListOrgchartInternal}
                      queryKey={['get-orgchart-internal']}
                      keysLabel={['code', 'nameVN']}
                      placeholder="Chọn tên đơn vị"
                      handleSelect={handleSelectOrgchartInternal}
                      enabled={orgchartType === ORGCHART_TYPE.INTERNAL}
                      allowClear={true}
                    />
                  </Form.Item>
                ) : (
                  <Form.Item
                    label="Tên đơn vị"
                    name={['orgchart', 'name']}
                    key="external"
                    rules={[{ required: true, message: 'Vui lòng chọn tên đơn vị' }]}
                  >
                    <SingleSelectLazy
                      key="external-select"
                      apiQuery={getListOrgchartExternal}
                      queryKey={['get-orgchart-external']}
                      keysLabel={['partnershipCode', 'partnershipName']}
                      placeholder="Chọn tên đơn vị"
                      handleSelect={handleSelectOrgchartExternal}
                      enabled={orgchartType === ORGCHART_TYPE.EXTERNAL}
                      allowClear={true}
                    />
                  </Form.Item>
                )}
              </Col>
              <Col span={12}>
                <Form.Item label="Mã đơn vị" name={['orgchart', 'code']}>
                  <Input placeholder="Hiển thị mã đơn vị" maxLength={15} disabled />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label="Mã Business Partner" name={['orgchart', 'bpID']}>
                  <Input placeholder="Hiển thị mã BP" maxLength={15} disabled />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label="Mã số thuế" name={['orgchart', 'taxCode']}>
                  <Input placeholder="Hiển thị mã số thuế" maxLength={15} disabled />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label="Địa chỉ email"
                  name={['orgchart', 'email']}
                  rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                >
                  <Input placeholder="Nhập địa chỉ email" maxLength={254} />
                </Form.Item>
              </Col>

              <Col span={24}>
                {orgchartType === ORGCHART_TYPE.INTERNAL ? (
                  <Form.Item label="Hợp tác với đơn vị bán hàng" name="orgchartPartnerId" key="partner-internal">
                    <SingleSelectLazy
                      key={`partner-internal-select-${orgchartId || 'empty'}`}
                      apiQuery={getListOrgchartPartner}
                      queryKey={['get-orgchart-partner-internal']}
                      keysLabel={['code', 'nameVN']}
                      placeholder="Chọn tên đơn vị bán hàng"
                      handleSelect={handleSelectOrgchartPartnerInternal}
                      moreParams={{ nids: orgchartId }}
                      enabled={!!orgchartId}
                      allowClear={true}
                    />
                  </Form.Item>
                ) : (
                  <Form.Item label="Hợp tác với đơn vị bán hàng" name="orgchartPartnerId" key="partner-external">
                    <SingleSelectLazy
                      key={`partner-external-select-${orgchartId || 'empty'}`}
                      apiQuery={getListOrgchartPartner}
                      queryKey={['get-orgchart-partner-external']}
                      keysLabel={['code', 'nameVN']}
                      placeholder="Chọn tên đơn vị bán hàng"
                      handleSelect={handleSelectOrgchartPartnerExternal}
                      moreParams={{ ids: orgchartId }}
                      enabled={!!orgchartId}
                      allowClear={true}
                    />
                  </Form.Item>
                )}
              </Col>
            </Row>

            <Title level={5}>Thông tin thanh toán</Title>
            <Form.Item label="Tài khoản giao dịch">
              <div
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: 20,
                  margin: 0,
                  marginBottom: 16,
                }}
              >
                <Form.List name="bankAccount">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Row align="middle" gutter={[8, 8]} key={key}>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, 'bankCode']}
                              label="Ngân hàng"
                              rules={[{ required: false }]}
                            >
                              <Select
                                placeholder="Chọn ngân hàng"
                                allowClear
                                filterOption={(input, option) =>
                                  typeof option?.label === 'string'
                                    ? option.label.toLowerCase().includes(input.toLowerCase())
                                    : false
                                }
                                showSearch
                                loading={isLoadingBanks}
                                options={banks.map(item => ({
                                  value: item?.bankCode,
                                  label: item?.bankName,
                                }))}
                                onChange={(value, option) => {
                                  const currentAccountNumber = form.getFieldValue([
                                    'bankAccount',
                                    name,
                                    'accountNumber',
                                  ]);
                                  const currentBeneciary = form.getFieldValue(['bankAccount', name, 'beneciary']);

                                  // Set bankName riêng biệt để lưu tên ngân hàng
                                  form.setFieldsValue({
                                    bankAccount: {
                                      [name]: {
                                        bankName: value
                                          ? Array.isArray(option)
                                            ? ''
                                            : option?.label || ''
                                          : undefined,
                                        // Reset số tài khoản và tên người thụ hưởng khi bỏ chọn ngân hàng
                                        accountNumber: value ? currentAccountNumber || '' : undefined,
                                        beneciary: value ? currentBeneciary || '' : undefined,
                                      },
                                    },
                                  });

                                  // Chỉ clear errors khi bỏ chọn ngân hàng, không trigger validation ngay
                                  if (!value) {
                                    form.setFields([
                                      {
                                        name: ['bankAccount', name, 'accountNumber'],
                                        errors: [],
                                      },
                                      {
                                        name: ['bankAccount', name, 'beneciary'],
                                        errors: [],
                                      },
                                    ]);
                                  }
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Form.Item
                            noStyle
                            shouldUpdate={(prevValues, currentValues) => {
                              const prevBankCode = prevValues?.bankAccount?.[name]?.bankCode;
                              const currentBankCode = currentValues?.bankAccount?.[name]?.bankCode;
                              return prevBankCode !== currentBankCode;
                            }}
                          >
                            {() => {
                              const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                              const isDisabled = !bankCode || (typeof bankCode === 'string' && !bankCode.trim());

                              return (
                                <>
                                  <Col span={6}>
                                    <Form.Item
                                      {...restField}
                                      name={[name, 'accountNumber']}
                                      label="Số tài khoản"
                                      dependencies={[['bankAccount', name, 'bankCode']]}
                                      validateTrigger={['onBlur', 'onSubmit']}
                                      rules={[
                                        {
                                          message: 'Vui lòng nhập số tài khoản',
                                          validator: (_, value) => {
                                            const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                            if (
                                              bankCode &&
                                              typeof bankCode === 'string' &&
                                              bankCode.trim() &&
                                              !value?.trim()
                                            ) {
                                              return Promise.reject('Vui lòng nhập số tài khoản');
                                            }
                                            return Promise.resolve();
                                          },
                                        },
                                      ]}
                                    >
                                      <Input placeholder="Nhập số tài khoản" maxLength={20} disabled={isDisabled} />
                                    </Form.Item>
                                  </Col>
                                  <Col span={7}>
                                    <Form.Item
                                      {...restField}
                                      name={[name, 'beneciary']}
                                      label="Tên người thụ hưởng"
                                      dependencies={[['bankAccount', name, 'bankCode']]}
                                      validateTrigger={['onBlur', 'onSubmit']}
                                      rules={[
                                        {
                                          message: 'Vui lòng nhập tên người thụ hưởng',
                                          validator: (_, value) => {
                                            const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                            if (
                                              bankCode &&
                                              typeof bankCode === 'string' &&
                                              bankCode.trim() &&
                                              !value?.trim()
                                            ) {
                                              return Promise.reject('Vui lòng nhập tên người thụ hưởng');
                                            }
                                            return Promise.resolve();
                                          },
                                        },
                                      ]}
                                    >
                                      <Input
                                        placeholder="Nhập tên người thụ hưởng"
                                        maxLength={255}
                                        disabled={isDisabled}
                                      />
                                    </Form.Item>
                                  </Col>
                                </>
                              );
                            }}
                          </Form.Item>
                          <Col span={1}>
                            <CloseOutlined
                              style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                              onClick={() => {
                                remove(name);
                                handleRemoveBankAccount(name);
                              }}
                            />
                          </Col>
                        </Row>
                      ))}

                      {fields.length < 10 ? (
                        <Col span={23}>
                          <Button
                            type="dashed"
                            onClick={() => {
                              add({ bankCode: undefined, bankName: undefined, accountNumber: '', beneciary: '' });
                            }}
                            style={{ padding: 0 }}
                            block
                            icon={<PlusOutlined />}
                          >
                            Thêm tài khoản giao dịch
                          </Button>
                        </Col>
                      ) : null}
                    </>
                  )}
                </Form.List>
              </div>
            </Form.Item>
            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name="mainBankId"
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select
                  placeholder="Chọn tài khoản giao dịch chính"
                  options={defaultBankOptions}
                  onChange={handleSelectBankInfo}
                  showSearch
                  optionFilterProp="label"
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                />
              </Form.Item>
              {/* Trường ẩn để lưu trữ đối tượng MainBank */}
              <Form.Item name="mainBank" hidden>
                <Input type="hidden" />
              </Form.Item>
            </div>

            <InfoEscrow
              form={form}
              depositDates={depositDates}
              setDepositDates={setDepositDates}
              defaultDistributionChannel={defaultDistributionChannel}
              handleSelectDistributionChannel={handleSelectDistributionChannel}
              handleSelectDivision={handleSelectDivision}
              handleSelectDepositProject={handleSelectDepositProject}
              formatNumber={formatNumber}
            />

            {/* Sản phẩm ký quỹ */}
            <EscrowProduct
              selectedProducts={selectedProducts}
              setSelectedProducts={setSelectedProducts}
              selectedPropertyUnitIds={selectedPropertyUnitIds}
              setSelectedPropertyUnitIds={setSelectedPropertyUnitIds}
              removedProductInfo={removedProductInfo}
              setRemovedProductInfo={setRemovedProductInfo}
            />
          </Col>

          <Col span={10}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin phí mô giới</Title>
            <Form.Item label="Chính sách phí môi giới" name="salePolicyId">
              <SingleSelectLazy
                apiQuery={getListSalesPolicy}
                queryKey={['get-sale-policy']}
                keysLabel={'name'}
                placeholder="Chọn chính sách phí môi giới"
                handleSelect={handleSelectSalePolicy}
                moreParams={{ isActive: true, projectId: '' }}
              />
            </Form.Item>

            <Title level={5}>Tài liệu đính kèm</Title>
            <Form.Item name="files">
              <UploadFileDepositContract
                fileList={fileList}
                setFileList={setFileList}
                uploadPath="primary-contract/deposit-contract"
                size={25}
              />
            </Form.Item>
          </Col>
        </Row>

        <EscrowPaymentPlan
          installments={installments}
          setInstallments={setInstallments}
          form={form}
          handleKeyDownEnterNumber={handleKeyDownEnterNumber}
          totalPaymentError={totalPaymentError}
          setTotalPaymentError={setTotalPaymentError}
          depositAmount={depositAmount}
          calculateExpiredDate={calculateExpiredDate}
          validateTotalPayment={validateTotalPayment}
          calculateConvertedAmount={calculateConvertedAmount}
        />

        <div className="create-footer">
          <div className="button-create">
            <Button htmlType="button" style={{ marginRight: 12 }} onClick={handleCancel}>
              Hủy
            </Button>

            <Button type="primary" htmlType="submit">
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default EscrowContractForm;

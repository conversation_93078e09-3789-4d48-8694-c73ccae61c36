import { Form, Modal } from 'antd';
import ModalComponent from '../../../../components/modal';
import { useCreateField } from '../../../../hooks';
import { createDepositContract } from '../../../../service/depositContract';
import { useCallback } from 'react';
import EscrowContractForm from '../escrowContractForm';

interface EscrowContractCreateModalProps {
  visible: boolean;
  onClose: () => void;
}

const EscrowContractCreateModal = ({ visible, onClose }: EscrowContractCreateModalProps) => {
  const [form] = Form.useForm();

  const { mutateAsync: _createDepositContract, isPending } = useCreateField({
    apiQuery: createDepositContract,
    keyOfListQuery: ['get-deposit-contract'],
    // keyOfDetailQuery: ['get-deposit-contract'],
    isMessageError: false,
    messageSuccess: 'Tạo mới hợp đồng ký quỹ thành công!',
  });

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      onClose();
    }
  }, [form, onClose]);

  const handleCreate = useCallback(
    async (values: any) => {
      try {
        const resp = await _createDepositContract(values);
        if (resp?.data?.statusCode === '0' && resp?.data?.success) {
          onClose();
        }
      } catch (error) {
        console.error('Create error:', error);
      }
    },
    [_createDepositContract, onClose],
  );
  return (
    <ModalComponent
      className="modal-deposit-contract"
      title="Tạo mới hợp đồng ký quỹ"
      open={visible}
      footer={null}
      onCancel={handleCancel}
      destroyOnClose
    >
      <EscrowContractForm form={form} onFinish={handleCreate} loading={isPending} />
    </ModalComponent>
  );
};

export default EscrowContractCreateModal;

import { <PERSON><PERSON>, Col, Form, Modal, Row } from 'antd';
import React, { useEffect, useState } from 'react';
import FormPeriod from '../../../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../../../components/select/singleSelectLazy';
import { INDICATOR } from '../../../../../configs/path';
import { useCreateField } from '../../../../../hooks';
import { handleErrors } from '../../../../../service/error/errorsService';
import { createIndicator } from '../../../../../service/indicator';
import { IModalCreateIndicator, TCreateIndicator } from '../../../../../types/indicator';
import './styles.scss';
import { getOrgChartDropdown } from '../../../../../service/lead';

const { Item } = Form;

const ModalCreateIndicator = (props: IModalCreateIndicator) => {
  const { isOpen, handleCancel } = props;
  const [form] = Form.useForm();

  const [, setDataSubmit] = useState<TCreateIndicator>();
  const [isModified, setIsModified] = useState(false);

  const { mutateAsync: createIndicatorModal } = useCreateField<TCreateIndicator>({
    apiQuery: createIndicator,
    keyOfListQuery: ['get-indicators'],
    checkDuplicate: true,
    isMessageError: false,
  });

  const handleSubmit = async (values: TCreateIndicator) => {
    const periodObj = form.getFieldValue('periodObj');

    const pos = values?.pos;

    const newData = {
      ...values,
      ...periodObj,
      pos: undefined,
      year: Number(values?.year),
      isActive: '1',
      listPos: [{ id: pos ? pos?.id : '', code: values?.pos?.value, name: values?.pos?.name }],
    };

    const res = await createIndicatorModal(newData);
    const responseData = res?.data;
    if (responseData) {
      const openViewDetail = () => {
        window.open(`${INDICATOR}/${(responseData?.data as TCreateIndicator)?.id}`, '_blank', 'noopener,noreferrer');
      };
      switch (responseData.statusCode) {
        case '0':
          handleCancel();
          removeDataSubmit();
          openViewDetail();
          break;
        default:
          handleErrors(responseData);
      }
    }
  };

  const removeDataSubmit = () => {
    setDataSubmit(undefined);
  };

  useEffect(() => {
    if (form.isFieldsTouched(true) && isOpen) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [form, isOpen]);

  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const validateForm = () => {
    setIsModified(true);
  };

  const handleCancelModalCreate = React.useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          handleCancel();
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
      setIsModified(false);
    } else {
      form.resetFields();
      setIsModified(false);
      handleCancel();
    }
  }, [form, handleCancel]);

  const handleSelectSalePos = (value: string) => {
    form.setFieldsValue({ pos: value });
  };

  return (
    <>
      <Modal
        rootClassName="wrapper-modal-create-indicator"
        title="Tạo mới chỉ tiêu"
        open={isOpen}
        maskClosable={false}
        centered
        onCancel={() => {
          handleCancelModalCreate();
          removeDataSubmit();
        }}
        destroyOnClose
        zIndex={1000}
        afterClose={() => form.resetFields()}
        footer={
          <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
            Tạo mới
          </Button>
        }
      >
        <Form form={form} layout="vertical" onFinish={handleSubmit} onValuesChange={validateForm}>
          <Row gutter={16} className="content-header-form">
            <Col span={24}>
              <Item label="Đơn vị" name="pos" required rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}>
                <SingleSelectLazy
                  apiQuery={getOrgChartDropdown}
                  queryKey={['orgChart-exchanges']}
                  keysLabel={'name'}
                  placeholder="Chọn đơn vị"
                  handleSelect={handleSelectSalePos}
                  defaultValues={{
                    value: form.getFieldValue(['pos', 'id']),
                    label: form.getFieldValue(['pos', 'name']),
                  }}
                />
              </Item>
            </Col>
            <Col span={24}>
              <FormPeriod
                messageValidate="Vui lòng chọn kỳ tính hoa hồng"
                label="Kỳ tính phí/ hoa hồng"
                fieldPos="pos"
              />
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default ModalCreateIndicator;

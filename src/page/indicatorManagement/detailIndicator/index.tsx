import { useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Button,
  Col,
  Flex,
  Form,
  Input,
  notification,
  Row,
  Switch,
  Table,
  TableColumnsType,
  Typography,
  Upload,
} from 'antd';
import { FormProps } from 'antd/lib';
import { RcFile } from 'antd/lib/upload';
import { AxiosError } from 'axios';
import dayjs from 'dayjs';
import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import FormPeriod from '../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { FORMAT_DATE_TIME, OPTIONS_STATUS_KPI } from '../../../constants/common';
import { useD<PERSON><PERSON><PERSON><PERSON>, useFetch, useUpdateField } from '../../../hooks';
import { handleErrors } from '../../../service/error/errorsService';
import {
  deleteIndicatorList,
  downloadTemplate,
  getDetailIndicator,
  importIndicatorList,
  putUpdateIndicator,
} from '../../../service/indicator';
import { getOrgChartDropdown } from '../../../service/lead';
import { IIndicator, IIndicatorDetail, TListIndicatorDetail } from '../../../types/indicator';
import { downloadArrayBufferFile } from '../../../utilities/shareFunc';
import { columnsIndicator } from '../listIndicators/columns';
import SkeletonDetail from './components/skeletonDetail';
import './styles.scss';
import { COMMISSION_HISTORY_IMPORT } from '../../../configs/path';

const { Item } = Form;
const { Title, Text } = Typography;

const DetailIndicator = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const cloneAddress = Form.useWatch(['info', 'cloneAddress'], form);
  const address = Form.useWatch(['address'], form);
  const pos = Form.useWatch('pos', form);
  const queryClient = useQueryClient();

  const [initialValues, setInitialValues] = useState<IIndicator>();
  const [isModified, setIsModified] = useState(false);
  const [actived, setActived] = useState(OPTIONS_STATUS_KPI[0]?.value);

  const [stateIsLoadingUpload, setStateIsLoadingUpload] = React.useState<boolean>(false);
  const [loadingListIndicator, setLoadingListIndicator] = React.useState<boolean>(false);

  const [indicatorList, setIndicatorList] = React.useState<TListIndicatorDetail[]>([]);
  const [searchText, setSearchText] = React.useState('');

  const { mutateAsync, isPending } = useUpdateField({
    apiQuery: putUpdateIndicator,
    keyOfListQuery: ['get-indicators'],
    keyOfDetailQuery: ['get-detail-indicator', id],
    checkDuplicate: true,
    isMessageError: false,
  });

  const { mutateAsync: handleDeleteIndicatorList, isPending: isLoadingDeleteRoleGroup } = useDeleteField({
    apiQuery: deleteIndicatorList,
    keyOfDetailQuery: ['delete-indicator-record'],
    messageSuccess: 'Xoá đợt chỉ tiêu thành công!',
    isMessageError: false,
  });

  const { data: dataDetail } = useFetch<IIndicatorDetail>({
    api: () => id && getDetailIndicator(id),
    queryKeyArr: ['get-detail-indicator', id],
    enabled: !!id,
    cacheTime: 10,
  });
  const exportTemplate = useMutation({
    mutationFn: () => downloadTemplate({ indicatorId: id }),
  });
  const dataIndicator = dataDetail?.data?.data?.indicator;
  const dataIndicatorList = dataDetail?.data?.data?.indicatorList;

  const handelChangeStatus = React.useCallback(
    (checked: boolean) => {
      setActived(checked ? 1 : 2);
      form.setFieldValue('isActive', checked ? 1 : 2);
      setIsModified(true);
    },
    [form],
  );
  // cảnh báo khi thoát trang khi chưa lưu dữ liệu
  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  useEffect(() => {
    if (dataIndicator) {
      const initialData = {
        ...dataIndicator,
        periodObj: {
          periodFrom: dataIndicator?.periodFrom,
          periodTo: dataIndicator?.periodTo,
          periodName: dataIndicator?.periodName,
        },
        year: dataIndicator?.year,
        listPos: dataIndicator?.listPos?.map(o => ({ ...o, label: o?.name, value: o?.code })),
        pos: dataIndicator?.listPos?.[0],
      };
      setInitialValues(initialData as IIndicator);
      setActived(Number(dataIndicator?.isActive));
      setIndicatorList(dataIndicatorList ?? []);
    }
  }, [dataIndicator, dataIndicatorList, form]);

  const onFinish: FormProps['onFinish'] = async (values: IIndicator) => {
    const periodObj = form.getFieldValue('periodObj');

    const newData = {
      ...periodObj,
      id: id,
      pos: undefined,
      period: values?.period,

      year: Number(values?.year),
      isActive: values?.isActive,
      listPos: [{ id: values?.pos?.id || '', code: values?.pos?.code, name: values?.pos?.name }],
    };

    const response = await mutateAsync({ ...newData });
    const responseData = response?.data;

    if (responseData) {
      switch (responseData?.statusCode) {
        case '0':
          setIsModified(false);
          break;
        default:
          handleErrors(responseData);
      }
    }
  };

  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  useEffect(() => {
    if (cloneAddress) {
      form.setFieldsValue({
        rootAddress: form.getFieldValue('address'),
      });
    }
  }, [cloneAddress, form, address]);

  useEffect(() => {
    if (isModified) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [isModified]);

  const handleBeforeUpload = async (file: RcFile) => {
    const isAllowedType = file?.name.toLowerCase().endsWith('.xlsx');
    if (!isAllowedType) {
      notification.error({ message: 'File không đúng định dạng. Vui lòng sử dụng file .xlsx' });
      return Upload.LIST_IGNORE;
    }
    return true;
  };

  const handleDownloadTemplate = async () => {
    try {
      const response = await exportTemplate.mutateAsync();
      downloadArrayBufferFile({ data: response.data, fileName: `Template-Chỉ tiêu.xlsx` });
    } catch (error) {
      notification.error({ message: 'Xuất dữ liệu thất bại.' });
    }
  };

  const filteredData = React.useMemo(() => {
    const normalize = (str: string = '') => str.toLowerCase().replace(/\s+/g, ' ').trim(); // chuẩn hóa: viết thường + loại bỏ khoảng trắng thừa

    const normalizedSearch = normalize(searchText);

    return indicatorList.filter(item => {
      const code = normalize(item.employee?.code);
      const name = normalize(item.employee?.name);

      return code.includes(normalizedSearch) || name.includes(normalizedSearch);
    });
  }, [indicatorList, searchText]);

  const handleCustomUpload = async (
    file: string | RcFile | Blob,
    onSuccess?: (response: string) => void,
    onError?: (error: Error) => void,
  ) => {
    try {
      setStateIsLoadingUpload(true);
      setLoadingListIndicator(true);
      setIsModified(true);

      const resp = await importIndicatorList(file as RcFile, dataIndicator?.id ?? '');

      form.setFieldValue('indicatorList', resp?.data?.data?.indicatorList);

      const status = resp?.data?.statusCode;

      const link = (
        <Typography.Link href={COMMISSION_HISTORY_IMPORT} target="_blank">
          đây
        </Typography.Link>
      );

      if (status === 'PENDING') {
        notification.warning({
          message: <>Upload đang được xử lý, xem tại {link}</>,
        });
      } else if (status === 'SUCCESS') {
        notification.success({
          message: <>Import thành công Danh sách giao dịch chỉ tiêu</>,
        });
      } else {
        notification.error({
          message: <>Upload thất bại, xem tại {link}</>,
        });
      }

      onSuccess?.('ok');

      if (dataIndicator?.id) {
        queryClient.invalidateQueries({
          queryKey: ['get-detail-indicator', dataIndicator.id],
        });
      }
    } catch (error: unknown) {
      onError?.(error as AxiosError);
    } finally {
      setStateIsLoadingUpload(false);
      setLoadingListIndicator(false);
    }
  };

  const handleDeleteRecordIndicator = React.useCallback(
    async (indicator: IIndicator) => {
      const newIndicatorList = indicatorList;
      const findIndex = newIndicatorList?.findIndex(o => o?.id === indicator?.id);
      const response = await handleDeleteIndicatorList({ id: indicator?.id ?? '' });
      if (response?.data?.statusCode === '0') {
        if (findIndex !== undefined && findIndex !== null) {
          // newIndicatorList?.splice(findIndex, 1);
          setIndicatorList(
            newIndicatorList
              ?.slice(0, findIndex)
              ?.concat(newIndicatorList?.slice(findIndex + 1, newIndicatorList?.length))
              ? newIndicatorList
                  ?.slice(0, findIndex)
                  ?.concat(newIndicatorList?.slice(findIndex + 1, newIndicatorList?.length))
              : [],
          );
        }
      } else {
        notification.error({ message: 'Xóa đợt chỉ tiêu không thành công!' });
      }
    },
    [handleDeleteIndicatorList, indicatorList],
  );

  const handleSelectSalePos = (value: string) => {
    form.setFieldsValue({ pos: value });
    setIsModified(true);
  };

  const actionIndicatorColumns: TableColumnsType = React.useMemo(() => {
    return [
      ...columnsIndicator,
      {
        dataIndex: 'action',
        align: 'center',
        width: '80px',
        render: (_value, record: IIndicator) => {
          return (
            <Text style={{ color: 'red', cursor: 'pointer' }} onClick={() => handleDeleteRecordIndicator(record)}>
              Xóa
            </Text>
          );
        },
      },
    ];
  }, [handleDeleteRecordIndicator]);

  return (
    <div className="wrapper-detail-personal-indicator">
      <BreadCrumbComponent titleBread={''} />
      {!initialValues ? (
        <SkeletonDetail />
      ) : (
        <Form
          form={form}
          layout="vertical"
          onFinish={onFinish}
          onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
          preserve={false}
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}>
              <Title level={5}>Thông tin chung</Title>
            </Col>
            <Col xs={24} md={12}>
              <Row gutter={24}>
                <Col xs={24} md={12}>
                  <Item label="Mã chỉ tiêu" name="code" required rules={[{ required: false }]}>
                    <Input placeholder="" disabled />
                  </Item>
                </Col>
                <Col xs={24} md={12}>
                  <Item label="Đơn vị" name="pos" rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}>
                    <SingleSelectLazy
                      apiQuery={getOrgChartDropdown}
                      queryKey={['orgChart-exchanges']}
                      keysLabel={'name'}
                      placeholder="Chọn đơn vị"
                      handleSelect={handleSelectSalePos}
                      defaultValues={{
                        value: pos?.id,
                        label: pos?.name,
                      }}
                    />
                  </Item>
                </Col>

                <Col span={24}>
                  <FormPeriod
                    label="Kỳ tính phí/ hoa hồng"
                    required
                    fieldPos="pos"
                    messageValidate="Vui lòng chọn kỳ tính hoa hồng"
                  />
                </Col>

                {/**Phần chọn trạng thái */}
                <Col xs={24} md={4}>
                  <Item label="Trạng thái" name="isActive"></Item>
                </Col>
                <Col xs={24} md={3}>
                  <Switch defaultValue={true} value={actived === 1} onChange={handelChangeStatus} />
                </Col>
                <Col xs={24} md={6}>
                  <Text style={{ color: actived === 1 ? '#389E0D' : '#CF1322' }}>
                    {actived === 1 ? OPTIONS_STATUS_KPI[0]?.label : OPTIONS_STATUS_KPI[1]?.label}
                  </Text>
                </Col>
              </Row>
            </Col>
            <Col xs={24} md={12}>
              <div className="info">
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày cập nhật: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(dataIndicator?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                      {`${dataIndicator?.modifiedBy?.userName} - ${dataIndicator?.modifiedBy?.fullName}`}
                    </Text>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col lg={6} xs={8}>
                    <Text disabled>Ngày tạo: </Text>
                  </Col>
                  <Col lg={18} xs={16}>
                    <Text disabled>
                      {dayjs(dataIndicator?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                      {`${dataIndicator?.createdBy?.userName} - ${dataIndicator?.createdBy?.fullName}`}
                    </Text>
                  </Col>
                </Row>
              </div>
            </Col>

            {/* <Row gutter={{ md: 24, lg: 40 }}> */}
            <Col xs={24} md={24}>
              <Row gutter={{ md: 24, lg: 40 }} align={'middle'}>
                <Col span={24}>
                  <Title level={5}>Danh sách chỉ tiêu</Title>
                </Col>
                <Col span={10}>
                  <Input.Search
                    placeholder="Tìm theo mã hoặc tên nhân viên"
                    allowClear
                    style={{ width: 300 }}
                    onSearch={value => setSearchText(value)}
                  />
                </Col>
                <Col span={14}>
                  <Flex gap="small" wrap justify="flex-end" align="center">
                    <Upload
                      beforeUpload={handleBeforeUpload}
                      maxCount={1}
                      customRequest={({ file, onSuccess, onError }) => handleCustomUpload(file, onSuccess, onError)}
                      disabled={stateIsLoadingUpload}
                      showUploadList={false}
                      name="file-upload"
                    >
                      <Button>Tải nhập</Button>
                    </Upload>
                    <Button style={{ marginLeft: '8px' }} onClick={handleDownloadTemplate}>
                      Tải về template
                    </Button>
                  </Flex>
                </Col>
              </Row>
            </Col>
            {/* </Row> */}
            <Col xs={24} md={24} style={{ marginTop: '8px' }}>
              <Table
                columns={actionIndicatorColumns}
                loading={loadingListIndicator || isLoadingDeleteRoleGroup}
                dataSource={filteredData}
                pagination={false}
              />
            </Col>
          </Row>
        </Form>
      )}
      {isModified && (
        <ButtonOfPageDetail handleSubmit={() => form.submit()} handleCancel={handleCancel} loadingSubmit={isPending} />
      )}
    </div>
  );
};

export default DetailIndicator;

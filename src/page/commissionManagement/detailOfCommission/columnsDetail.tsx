import { notification, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import dayjs from 'dayjs';
import {
  FORMAT_DATE,
  FORMAT_DATE_TIME,
  OPTIONS_STATUS_ADJUSTMENT_VERSION,
  OPTIONS_STATUS_TRANSACTIONS,
} from '../../../constants/common';
import { TAdjustmentVersion, TOriginalData } from '../../../types/commission';
import { handleDownloadFileS3 } from '../../../utilities/shareFunc';

export const transactionColumns: ColumnsType<TOriginalData> = [
  {
    title: 'Mã giao dịch',
    dataIndex: ['contract', 'code'],
    key: 'code',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Dự án',
    dataIndex: ['project', 'name'],
    key: 'projectName',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Mã BĐS',
    dataIndex: ['propertyUnit', 'code'],
    key: 'propertyUnitCode',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Mã BĐS',
    dataIndex: ['propertyUnit', 'apartmentType'],
    key: 'apartmentType',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Tên khách hàng',
    dataIndex: ['customer', 'name'],
    key: 'name',
    render: (value: string) => (value ? value : '-'),
  },
  {
    title: 'Giá chưa VAT(Tạm tính)',
    dataIndex: ['propertyUnit', 'nvatPrice'],
    key: 'price',
    align: 'right',
    render: (value: number) => (value ? value : 0),
  },
  {
    title: 'Giá tham chiếu',
    dataIndex: ['propertyUnit', 'price'],
    key: 'referencePrice',
    align: 'right',
    render: (value: number) => (value ? value : 0),
  },
  {
    title: 'Tỷ lệ phí',
    dataIndex: ['transaction', 'otherFee'],
    key: 'otherFee',
    align: 'right',
    render: (value: number) => (value ? value : 0),
  },
  {
    title: 'Tỷ lệ chia',
    dataIndex: ['revenue', 'revenueRate'],
    key: 'revenueRate',
    render: (value: number) => (value ? value : 0),
  },
  {
    title: 'Phí cơ bản',
    dataIndex: ['transaction', 'registerFee'],
    key: 'registerFee',
    render: (value: number) => (value ? value : 0),
  },

  {
    title: 'Trạng thái giao dịch',
    dataIndex: ['transaction', 'state'],
    key: 'state',
    render: (value: string) => {
      const status = OPTIONS_STATUS_TRANSACTIONS.find(item => item.value === value);
      return status ? status?.label : '-';
    },
  },

  {
    title: 'Số tiền tạm ứng',
    dataIndex: ['transaction', 'advance'],
    key: 'advance',
    render: (value: number) => (value ? value : 0),
  },
  {
    title: 'Ngày KH cọc',
    dataIndex: 'escrowDate',
    key: 'escrowDate',
    render: (value: string) => (value ? dayjs(value).format(FORMAT_DATE) : '-'),
  },
  {
    title: 'Phí tư vấn (Đã VAT)',
    dataIndex: ['transaction', 'consultingFee'],
    key: 'consultingFee',
    render: (value: number) => (value ? value : 0),
  },
  {
    title: 'Phí khác',
    dataIndex: ['transaction', 'otherFee'],
    key: 'otherFee1',
    render: (value: number) => (value ? value : 0),
  },
  {
    title: 'Tổng phí cơ bản & phí khác',
    dataIndex: ['transaction', 'total'],
    key: 'total',
    render: (value: number) => (value ? value : 0),
  },
];

// Version control table columns
export const versionColumns: ColumnsType<TAdjustmentVersion> = [
  {
    title: 'Phiên bản',
    dataIndex: 'version',
    key: 'version',
  },
  {
    title: 'Ngày tải lên',
    dataIndex: 'uploadDate',
    key: 'uploadDate',
    render: (text, record) => (
      <div>
        {text ? dayjs(text).format(FORMAT_DATE_TIME) : '-'}
        <br />
        {record?.uploadBy?.fullName ? record?.uploadBy?.fullName : '-'}
      </div>
    ),
  },
  {
    title: 'Log file',
    dataIndex: 'fileName',
    key: 'fileName',
    render: (text, record: TAdjustmentVersion) => {
      const handleDownload = () => {
        const nameFile = `${record?.fileName}.xlsx`;
        if (!record?.fileUrl) {
          notification.error({ message: 'Không tìm thấy đường dẫn tải xuống' });
          return;
        }
        handleDownloadFileS3(record.fileUrl, nameFile);
      };
      return (
        <Typography.Link
          href="#"
          onClick={e => {
            e.preventDefault();
            handleDownload();
          }}
          download
        >
          {text}
        </Typography.Link>
      );
    },
  },
  {
    title: 'Trạng thái',
    dataIndex: 'status',
    key: 'status',
    align: 'center',
    render: value => {
      const status = OPTIONS_STATUS_ADJUSTMENT_VERSION.find(item => item.value === value);
      return status ? <Typography.Text style={{ color: status?.color }}>{status?.name}</Typography.Text> : 'null';
    },
  },
];

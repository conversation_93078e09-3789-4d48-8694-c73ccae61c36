import { Form } from 'antd';
import { Dispatch, SetStateAction, useState } from 'react';
import DropdownFilterSearch from '../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import useFilter from '../../../../hooks/filter';
import { sendGetListOfDropdownProjects } from '../../../../service/salesPolicy';
import { TFilterTransaction } from '../../../../types/commission';

const FilterTransaction = ({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<TFilterTransaction | undefined>>;
}) => {
  const [form] = Form.useForm();
  const [filter, setFilter] = useFilter({});
  const [initialValues, setInitialValues] = useState<TFilterTransaction>();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const handleSubmitFilter = (values: TFilterTransaction) => {
    const newFilter: TFilterTransaction = {
      projectId: values?.projectId || undefined,
    };
    setFilterParams(prev => ({ ...prev, ...newFilter }));
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setInitialValues(undefined);
    setFilterParams(prev => ({ search: prev?.search }));
    setFilter({});
    setTimeout(() => {
      setIsOpenFilter(false);
    }, 100);
  };

  const handleSelectProject = (values: unknown) => {
    const newProjectFilter = (values as { value: string }[]).map(item => item.value)?.join(',');
    form.setFieldsValue({ projectId: newProjectFilter });
  };

  const handleSearchExpense = (value: string) => {
    const search = value || undefined;
    setFilterParams(prev => ({ ...prev, search: search }));
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        initialValues={initialValues}
        form={form}
        onClearFilters={handleClearFilters}
        onChangeSearch={handleSearchExpense}
        showParams={false}
        defaultValueSearch={filter?.search}
        extraFormItems={
          <>
            <Form.Item label="Chọn dự án" name="projectId">
              <MultiSelectLazy
                apiQuery={sendGetListOfDropdownProjects}
                queryKey={['list-projects-commission']}
                enabled={isOpenFilter}
                placeholder="Chọn dự án"
                keysLabel={['name']}
                keysTag={['name']}
                handleListSelect={handleSelectProject}
              />
            </Form.Item>
          </>
        }
      />
    </>
  );
};

export default FilterTransaction;

import { useQueryClient } from '@tanstack/react-query';
import { Button, Form, Input, Space } from 'antd';
import React from 'react';
import { useParams } from 'react-router-dom';
import { modalConfirm } from '../../../../components/modal/specials/ModalConfirm';
import { STATUS_ADJUSTMENT_VERSION } from '../../../../constants/common';
import { PERMISSION_COMMISSION } from '../../../../constants/permissions/commission';
import { FetchResponse, TDataList, useCheckPermissions, useUpdateField } from '../../../../hooks';
import { updateAnNounced } from '../../../../service/commission';
import { ButtonConfig, GroupButtonProps, TCommission, TExpense } from '../../../../types/commission';
import UploadFileVersionAdjustment from './UploadFileVersionAdjustment';

export const GroupButton: React.FC<GroupButtonProps> = ({
  handleStatusUpdate,
  modal,
  notification,
  tab,
  versionAdjustment,
  loadingButton,
  setSelectedAdjustment,
}) => {
  const { id } = useParams();
  const [formCancelApprove] = Form.useForm();
  const [formCancelPublish] = Form.useForm();
  const { publish, create, update, getById, approve } = useCheckPermissions(PERMISSION_COMMISSION);

  const queryClient = useQueryClient();
  const data = queryClient.getQueryData<FetchResponse<TDataList<TCommission>>>(['detail-of-commission', id]);
  const dataCommission = data?.data?.data;
  const adjustment = tab === 'adjustment' ? versionAdjustment : dataCommission?.adjustmentVersions?.[0];

  const dataExpense = queryClient.getQueryData<FetchResponse<TDataList<TExpense>>>(['detail-of-ExpenseList', id]);

  const dataExpenseList = dataExpense?.data?.data?.expenseList;

  const mutateAnNounced = useUpdateField({
    apiQuery: updateAnNounced,
    keyOfDetailQuery: ['detail-of-commission', id],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const handleDownloadOriginal = async () => {
    const linkDownload = adjustment?.fileUrl;
    const nameFile = `Commission_${dataCommission?.period}.xlsx`;
    if (!linkDownload) {
      notification.error({ message: 'Không tìm thấy đường dẫn tải xuống' });
      return;
    }
    const response = await fetch(linkDownload);
    const blob = await response.blob();
    const url = window.URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `${nameFile}`;
    link.click();
    window.URL.revokeObjectURL(url);
  };

  const isStatusWaiting = adjustment?.status === STATUS_ADJUSTMENT_VERSION.WAITING && approve;

  const isButtonSendApprove =
    adjustment?.status === STATUS_ADJUSTMENT_VERSION.NEW || adjustment?.status === STATUS_ADJUSTMENT_VERSION.REJECTED;

  const isStatusApprove = adjustment?.status === STATUS_ADJUSTMENT_VERSION.APPROVED;

  const isStatusAnnounced = adjustment?.status === STATUS_ADJUSTMENT_VERSION.ANNOUNCED && publish;

  // Danh sách các nút với điều kiện hiển thị
  const buttonConfigs: ButtonConfig[] = [
    {
      key: 'approve',
      label: 'Duyệt',
      permission: isStatusWaiting,
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          title: 'Duyệt đợt tính phí',
          content: 'Bạn có muốn duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate({ status: 'APPROVED', successMessage: 'Duyệt thành công.' });
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'sendApprove',
      label: 'Gửi duyệt',
      permission: isButtonSendApprove && update,
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          title: 'Gửi duyệt đợt tính phí',
          content: 'Bạn có muốn gửi duyệt đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate({ status: 'WAITING', successMessage: 'Gửi duyệt thành công.' });
          },

          modal,
        });
      },

      type: 'primary',
    },

    {
      key: 'publish',
      label: 'Công bố',
      permission: isStatusApprove && publish,
      loading: mutateAnNounced.isPending,
      onClick: () => {
        modalConfirm({
          title: 'Xác nhận công bố',
          content: 'Bạn có muốn công bố đợt tính phí không?',
          handleConfirm: async () => {
            if (id) {
              const res = await mutateAnNounced.mutateAsync({
                id,
                status: versionAdjustment?.status || adjustment?.status || '',
                adjustmentVersionId: versionAdjustment?.id || adjustment?.id || '',
              });
              if (res?.data?.statusCode === '0') {
                setSelectedAdjustment(null);
                notification.success({ message: 'Công bố kỳ tính phí thành công.' });
              }
            }
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'cancelApprove',
      label: 'Hủy phê duyệt',
      permission: isStatusApprove && (update || getById),
      loading: loadingButton,
      onClick: () => {
        modalConfirm({
          className: 'modal-cancel-approve',
          title: 'Hủy phê duyệt đợt tính phí',
          content: (
            <Form form={formCancelApprove}>
              <p>Bạn có muốn gửi duyệt đợt tính phí không?</p>
              <Form.Item
                name="cancelApprovalReason"
                rules={[{ required: true, message: 'Vui lòng nhập lý do hủy phê duyệt' }]}
              >
                <Input placeholder="Vui lòng nhập lý do muốn hủy phê duyệt đợt tính hoa hồng" />
              </Form.Item>
            </Form>
          ),
          handleConfirm: async () => {
            await formCancelApprove.validateFields();
            const values = formCancelApprove.getFieldsValue();
            await handleStatusUpdate({
              status: 'WAITING',
              successMessage: 'Hủy duyệt thành công.',
              cancelPublishReason: values.cancelApprovalReason,
            });
            formCancelApprove.resetFields();
          },
          destroyOnClose: true,
          onCancel: () => formCancelApprove.resetFields(),
          modal,
        });
      },
    },
    {
      key: 'cancel-publish',
      label: 'Hủy công bố',
      permission: isStatusAnnounced,
      loading: mutateAnNounced.isPending,
      onClick: () => {
        modalConfirm({
          className: 'modal-cancel-approve',
          title: 'Hủy công bố đợt tính hoa hồng',
          content: (
            <Form form={formCancelPublish}>
              <p>Vui lòng nhập lý do muốn hủy công bố đợt tính hoa hồng không?</p>
              <Form.Item
                name="cancelApprovalReason"
                rules={[{ required: true, message: 'Vui lòng nhập lý do hủy phê duyệt' }]}
              >
                <Input placeholder="Vui lòng nhập lý do muốn hủy phê duyệt đợt tính hoa hồng" />
              </Form.Item>
            </Form>
          ),
          handleConfirm: async () => {
            await formCancelPublish.validateFields();
            const values = formCancelPublish.getFieldsValue();
            if (id) {
              const res = await mutateAnNounced.mutateAsync({
                id,
                status: versionAdjustment?.status || adjustment?.status || '',
                adjustmentVersionId: versionAdjustment?.id || adjustment?.id || '',
                cancelPublishReason: values?.cancelApprovalReason,
              });
              if (res?.data?.statusCode === '0') {
                formCancelPublish.resetFields();
                notification.success({ message: 'Hủy công bố thành công.' });
                setSelectedAdjustment(null);
              }
            }
          },
          onCancel: () => formCancelPublish.resetFields(),
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'reject',
      label: 'Từ chối ',
      permission: isStatusWaiting,
      onClick: () => {
        modalConfirm({
          title: 'Từ chối đợt tính phí',
          content: 'Bạn có muốn từ chối đợt tính phí không?',
          handleConfirm: () => {
            handleStatusUpdate({ status: 'REJECTED', successMessage: 'Từ chối thành công' });
          },
          modal,
        });
      },
      type: 'default',
    },
    {
      key: 'uploadTemplate',
      label: 'Tải nhập giao dịch',
      permission: true && !(dataExpenseList?.length === 0),
      component: <UploadFileVersionAdjustment />,
    },
    {
      key: 'downloadOriginal',
      label: 'Tải về dữ liệu gốc',
      permission: create || update || getById,
      onClick: handleDownloadOriginal,
      type: 'default',
    },
  ];

  return (
    <Space>
      {buttonConfigs.map(button => {
        if (!button.permission) return null;

        return (
          <React.Fragment key={button.key}>
            {button.component ? (
              button.component
            ) : (
              <Button className={button.key} loading={button?.loading} type={button.type} onClick={button.onClick}>
                {button.label}
              </Button>
            )}
          </React.Fragment>
        );
      })}
      {/* <ConfirmDeleteModal
        label="Đợt phí"
        open={isOpenModalDelete}
        apiQuery={softDeleteCommission as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-commission']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá đợt phí"
        description="Vui lòng nhập lý do muốn xoá đợt phí này"
      />
       <ConfirmDeleteModal
        label="Đợt phí"
        open={isOpenModalDelete}
        apiQuery={softDeleteCommission as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-commission']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá đợt phí"
        description="Vui lòng nhập lý do muốn xoá đợt phí này"
      /> */}
    </Space>
  );
};

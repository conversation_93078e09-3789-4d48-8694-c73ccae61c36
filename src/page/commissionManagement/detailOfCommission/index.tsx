import { App, Button, Col, Flex, Form, Input, Radio, Row, Spin, Table, Tabs, TabsProps, Typography } from 'antd';
import { ColumnsType } from 'antd/lib/table';
import dayjs from 'dayjs';
import { useEffect, useMemo, useState } from 'react';
import { useBeforeUnload, useParams, useSearchParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import FormPeriod from '../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions, useCreateField, useFetch, useUpdateField } from '../../../hooks';
import {
  getDetailOfCommission,
  getDetailOfCommissionPublish,
  getExpenseList,
  postExpenseDraft,
  postMergeData,
  updateCommission,
  updateStatusCommission,
} from '../../../service/commission';
import { getSalesPolicyOfCommissionPeriod } from '../../../service/commissionPeriod';
import { getOrgChartDropdown } from '../../../service/lead';
import { TAdjustmentVersion, TCommission, TExpense, TOriginalData } from '../../../types/commission';
import { transactionColumns, versionColumns } from './columnsDetail';
import FilterTransaction from './components/FilterTransaction';
import { GroupButton } from './components/GroupButtonAction';
import './styles.scss';
import { FORMAT_DATE_TIME } from '../../../constants/common';
import { Units } from '../../../types/units/units';

const { Title, Text } = Typography;

// footer table expense
const transactionTableFooter = (dataSourceExpense: TExpense) => {
  const totalProperty = dataSourceExpense?.totalProperty || 0;
  const totalFee = dataSourceExpense?.listBonus?.toLocaleString() || 0;
  const totalFeePay = dataSourceExpense?.totalFee?.toLocaleString() || 0;

  return (
    <>
      <div className="item-footer">
        <p>
          Tổng số sản phẩm : <strong>{totalProperty}</strong>
        </p>
      </div>
      <div className="item-footer">
        <p>
          Tổng phí thưởng : <strong>{totalFee}</strong>
        </p>
      </div>
      <div className="item-footer">
        <p>
          Tổng phí phái trả: <strong>{totalFeePay}</strong>
        </p>
      </div>
    </>
  );
};

const DetailOfCommission = () => {
  const { id } = useParams();
  const [searchParams, setSearchParams] = useSearchParams();
  const params = Object.fromEntries([...searchParams]);
  const { modal, notification } = App.useApp();
  const [form] = Form.useForm();
  const pos = Form.useWatch('pos', form);
  const salePolicy = Form.useWatch('salePolicy', form);
  const period = Form.useWatch('period', form);
  const year = dayjs(Form.useWatch('year', form)).format('YYYY');

  const disabled = useMemo(() => {
    return !!(pos && period && year);
  }, [pos, period, year]);

  const { getById, publishGetId, update: permissionUpdate } = useCheckPermissions(PERMISSION_COMMISSION);

  const [tab, setTab] = useState(params?.tabData || 'original');
  const [isModified, setIsModified] = useState(false);
  const [filterParams, setFilterParams] = useState({});
  const [initialValue, setInitialValue] = useState<TCommission>({} as TCommission);
  const [dataExpenseList, setDataExpenseList] = useState<TExpense>();
  const [selectedAdjustment, setSelectedAdjustment] = useState<TAdjustmentVersion | null>(null);
  const [isCalculate, setIsCalculate] = useState(false);

  const { data: commission, isLoading } = useFetch<TCommission>({
    api: getById || (getById && publishGetId) ? getDetailOfCommission : getDetailOfCommissionPublish,
    queryKeyArr: ['detail-of-commission', id],
    moreParams: { id },
    withFilter: false,
    enabled: !!(getById || publishGetId),
  });
  const dataSource = commission?.data?.data;

  const { data: ExpenseList, isLoading: isLoadingExpense } = useFetch<TExpense>({
    api: getExpenseList,
    queryKeyArr: ['detail-of-ExpenseList', id, filterParams],
    moreParams: { commissionId: id, ...filterParams },
    withFilter: false,
  });
  const dataSourceExpense = ExpenseList?.data?.data;

  const update = useUpdateField({
    apiQuery: updateCommission,
    keyOfDetailQuery: ['detail-of-commission', id],
    isMessageError: false,
    isMessageSuccess: false,
  });

  const updateCommissionStatus = useUpdateField({
    apiQuery: updateStatusCommission,
    keyOfDetailQuery: ['detail-of-commission', id],
    isMessageSuccess: false,
    isMessageError: false,
  });

  const calculateDraft = useCreateField({
    apiQuery: postExpenseDraft,
    isMessageSuccess: false,
    isMessageError: false,
  });

  const mergeData = useUpdateField({
    apiQuery: postMergeData,
    isMessageError: false,
    isMessageSuccess: false,
  });

  useEffect(() => {
    if (!dataSource) return;
    const newDataSource = {
      ...dataSource,
      periodObj: {
        periodFrom: dataSource?.periodFrom,
        periodTo: dataSource?.periodTo,
        periodName: dataSource?.periodName,
      },
    };
    setInitialValue(newDataSource);
    form.setFieldsValue(newDataSource);
  }, [dataSource, form]);

  useBeforeUnload(event => {
    if (isModified) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  useEffect(() => {
    setDataExpenseList(dataSourceExpense);
  }, [dataSourceExpense]);

  const RadioColumns: ColumnsType<TAdjustmentVersion> = [
    {
      title: '',
      dataIndex: 'radio',
      key: 'radio',
      align: 'center',
      width: 48,
      render: (_, record) => (
        <Radio
          checked={selectedAdjustment?.id === record.id}
          onClick={() => {
            if (selectedAdjustment?.id === record.id) {
              setSelectedAdjustment(null);
            } else {
              setSelectedAdjustment(record);
            }
          }}
        />
      ),
    },
    ...versionColumns,
  ];

  const items = [
    getById && {
      label: 'Dữ liệu gốc',
      key: 'original',
      children: (
        <Table<TOriginalData>
          className="table-expense-list"
          columns={transactionColumns}
          dataSource={dataExpenseList?.expenseList}
          footer={() => (dataExpenseList ? transactionTableFooter(dataExpenseList) : null)}
          loading={isLoadingExpense}
          rowKey={'code'}
          pagination={false}
          scroll={{ x: 2000 }}
        />
      ),
    },

    (getById || publishGetId) && {
      label: 'Dữ liệu điều chỉnh',
      key: 'adjustment',
      children: (
        <Table<TAdjustmentVersion>
          className="table-adjustment-list"
          pagination={false}
          footer={() => <></>}
          rowKey={'id'}
          columns={RadioColumns}
          dataSource={initialValue?.adjustmentVersions}
        />
      ),
    },
  ].filter(Boolean) as TabsProps['items'];

  const handleCancel = () => {
    form.setFieldsValue(initialValue);
    setIsModified(false);
  };

  const validateForm = () => {
    setIsModified(true);
  };

  const handleChangeTab = (key: string) => {
    setTab(key);
    setSearchParams({ ...params, tabData: key });
  };

  const handleSelectSalePolicy = (value: string) => {
    form.setFieldsValue({ salePolicy: value });
    setIsModified(true);
  };

  const handleSelectSalePos = (value: Units) => {
    form.setFieldsValue({
      pos: value
        ? {
            id: value?.id,
            name: value?.name,
            code: value?.code,
          }
        : undefined,
      salePolicy: undefined,
      period: undefined,
      year: new Date().getFullYear().toString(),
    });
    setIsModified(true);
  };

  const handleSubmit = async () => {
    const allValues = form.getFieldsValue(true);
    const periodObj = form.getFieldValue('periodObj');
    if (!allValues) return;
    const newData = {
      ...allValues,
      id,
      pos: {
        id: allValues.pos?.id,
        name: allValues.pos?.name,
        code: allValues.pos?.code,
      },
      salePolicy: {
        id: allValues.salePolicy?.id,
        name: allValues.salePolicy?.name,
      },
      type: 'FEE',
      year: allValues?.year,
      ...periodObj,
    };
    const newDataMerge = {
      commissionId: id,
      modelList: dataExpenseList?.expenseList,
    };
    const res = await update.mutateAsync(newData);
    const resMerge = isCalculate && (await mergeData.mutateAsync(newDataMerge));

    if (res?.data?.statusCode === '0') {
      setIsModified(false);
      notification.success({
        message: 'Cập nhật thành công',
      });

      if (resMerge && resMerge?.data?.statusCode === '0') {
        notification.success({
          message: 'Cập nhật tính phí thành công',
        });
      }
    }
  };

  const handleStatusUpdate = async (status: string, successMessage: string) => {
    const idVersion = tab === 'original' ? dataSource?.adjustmentVersions[0]?.id : selectedAdjustment?.id;

    // Check if selectedAdjustment status and status parameter are both "WAITING"
    if (selectedAdjustment?.status === 'WAITING' && status === 'WAITING') {
      notification.info({
        message: 'Phiên bản đang được phê duyệt',
      });
      return;
    }

    const res = await updateCommissionStatus.mutateAsync({
      id,
      status,
      adjustmentVersionId: idVersion,
    });

    if (res?.data?.statusCode === '0') {
      notification.success({
        message: successMessage,
      });
      setSelectedAdjustment(null);
    }
  };

  const handleCalculateDraft = async () => {
    try {
      await form.validateFields();
      const payload = {
        pos: form.getFieldValue('pos'),
        commissionId: id,
        periodForm: form.getFieldValue('periodFrom'),
        periodTo: form.getFieldValue('periodTo'),
      };
      const res = await calculateDraft.mutateAsync(payload);
      if (res?.data?.statusCode === '0') {
        setDataExpenseList(res?.data?.data as TExpense);
        setIsModified(true);
        setIsCalculate(true);
      }
    } catch (error) {
      console.error(error);
    }
  };

  const handleClearFormFields = () => {
    form.setFieldsValue({
      salePolicy: undefined,
    });
    setIsModified(true);
  };

  return (
    <div>
      <Spin spinning={isLoading || update.isPending}>
        <BreadCrumbComponent titleBread={dataSource?.code} />
        <Form
          form={form}
          layout="vertical"
          onValuesChange={validateForm}
          onFinish={handleSubmit}
          className="wrapper-detail-commission"
        >
          <Title level={5}>Thông tin chi tiết</Title>
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col sm={12} xs={24}>
              <Row gutter={24}>
                <Col sm={12} xs={24}>
                  <Form.Item label="Mã đợt tính phí/ hoa hồng" required name="code">
                    <Input disabled />
                  </Form.Item>
                </Col>
                <Col sm={12} xs={24}>
                  <Form.Item label="Loại phí" required>
                    <Input disabled value={'Tính phí'} />
                  </Form.Item>
                </Col>

                <Col sm={12} xs={24}>
                  <Form.Item
                    label="Đơn vị"
                    name="pos"
                    required
                    rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getOrgChartDropdown}
                      queryKey={['orgChart-exchanges']}
                      keysLabel={'name'}
                      placeholder=" Chọn đơn vị bán hàng"
                      handleSelect={handleSelectSalePos}
                      defaultValues={{ value: pos?.id, label: pos?.name }}
                    />
                  </Form.Item>
                </Col>
                <Col sm={12} xs={24}>
                  <Form.Item
                    label="Chính sách phí - hoa hồng"
                    name="salePolicy"
                    rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
                  >
                    <SingleSelectLazy
                      apiQuery={getSalesPolicyOfCommissionPeriod}
                      queryKey={['sales-policy']}
                      keysLabel={['code', 'name']}
                      placeholder="Chọn chính sách phí - hoa hồng"
                      handleSelect={handleSelectSalePolicy}
                      disabled={!disabled}
                      enabled={disabled}
                      moreParams={{ name: pos?.name, isActive: 1 }}
                      defaultValues={{
                        value: salePolicy?.id,
                        label: salePolicy?.name,
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={24}>
                  <FormPeriod
                    required
                    label="Kỳ tính phí"
                    fieldPos="pos"
                    clearFormValueDependency={handleClearFormFields}
                  />
                  {permissionUpdate && (
                    <Form.Item>
                      <Button disabled={!pos || !period || !salePolicy} type="primary" onClick={handleCalculateDraft}>
                        Tính phí
                      </Button>
                    </Form.Item>
                  )}
                </Col>
              </Row>
            </Col>
            <Col sm={12} xs={24}>
              <Row gutter={24}>
                <Col lg={6} xs={8}>
                  <Text disabled>Ngày cập nhật: </Text>
                </Col>
                <Col lg={18} xs={16}>
                  <Text disabled>
                    {dayjs(dataSource?.createdDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                    {`${dataSource?.createdBy?.userName} - ${dataSource?.createdBy?.fullName}`}
                  </Text>
                </Col>
              </Row>
              <Row gutter={24}>
                <Col lg={6} xs={8}>
                  <Text disabled>Ngày tạo: </Text>
                </Col>
                <Col lg={18} xs={16}>
                  <Text disabled>
                    {dayjs(dataSource?.modifiedDate).format(FORMAT_DATE_TIME)}&nbsp;&nbsp;
                    {`${dataSource?.modifiedBy?.userName} - ${dataSource?.modifiedBy?.fullName}`}
                  </Text>
                </Col>
              </Row>
            </Col>
          </Row>
        </Form>
        <Title level={5}>Danh sách giao dịch</Title>
        <Flex justify="space-between">
          <FilterTransaction setFilterParams={setFilterParams} />
          <GroupButton
            modal={modal}
            notification={notification}
            versionAdjustment={selectedAdjustment}
            tab={tab}
            handleStatusUpdate={handleStatusUpdate}
            setSelectedAdjustment={setSelectedAdjustment}
            loadingButton={updateCommissionStatus.isPending}
          />
        </Flex>
        <Tabs className="tabs-transaction" type="card" activeKey={tab} onChange={handleChangeTab} items={items} />
        {isModified && permissionUpdate && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={update.isPending}
          />
        )}
      </Spin>
    </div>
  );
};

export default DetailOfCommission;

import { But<PERSON>, Col, Form, Modal, Row } from 'antd';
import dayjs from 'dayjs';
import { useMemo } from 'react';
import { useBeforeUnload } from 'react-router-dom';
import { showConfirmCancelModal } from '../../../components/modal/specials/ConfirmCancelModal';
import FormPeriod from '../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { COMMISSION } from '../../../configs/path';
import { useCreateField } from '../../../hooks';
import { createCommission } from '../../../service/commission';
import { getSalesPolicyOfCommissionPeriod } from '../../../service/commissionPeriod';
import { getOrgChartDropdown } from '../../../service/lead';
import './styles.scss';

interface Props {
  handleCancel: () => void;
  open: boolean;
}

const CreateCommission = (props: Props) => {
  const { handleCancel, open } = props;
  const [form] = Form.useForm();

  const pos = Form.useWatch('pos', form);
  const period = Form.useWatch('period', form);
  const year = dayjs(Form.useWatch('year', form)).format('YYYY');
  const salePolicy = Form.useWatch('salePolicy', form);

  const disabled = useMemo(() => {
    return !!(pos && period && year);
  }, [pos, period, year]);

  const { mutateAsync, isPending } = useCreateField({
    apiQuery: createCommission,
    keyOfListQuery: ['list-of-commission'],
    isMessageError: false,
  });

  const handleSubmit = async () => {
    const allValues = form.getFieldsValue(true);
    const periodObj = form.getFieldValue('periodObj');
    const newData = {
      year: allValues?.year,
      pos: {
        id: allValues.pos?.id,
        name: allValues.pos?.name,
        code: allValues.pos?.code,
      },
      salePolicy: {
        id: allValues.salePolicy?.id,
        name: allValues.salePolicy?.name,
      },
      type: 'FEE',
      ...periodObj,
    };

    const res = await mutateAsync(newData);

    const idCommission = res?.data?.data ? (res.data.data as { id: string }).id : undefined;
    if (res?.data?.statusCode === '0') {
      handleCancel();
      form.resetFields();
      window.open(`${COMMISSION}/${idCommission}`, '_blank', 'noopener,noreferrer');
    }
  };

  const handleSelectSalePolicy = (value: string) => {
    form.setFieldsValue({ salePolicy: value });
  };

  const handleSelectSalePos = (value: string) => {
    form.setFieldsValue({
      pos: value ? value : undefined,
      salePolicy: undefined,
      year: new Date().getFullYear().toString(),
      period: undefined,
    });
  };

  useBeforeUnload(event => {
    if (open && form.isFieldsTouched()) {
      event.preventDefault();
      return true;
    }
    return undefined;
  });

  return (
    <Modal
      className="wrapper-modal-create-commission"
      title="Tạo mới kỳ tính phí"
      open={open}
      footer={[
        <Button type="primary" loading={isPending} onClick={() => form.submit()}>
          Tạo mới
        </Button>,
      ]}
      onCancel={() => {
        if (!form.isFieldsTouched()) {
          handleCancel();
          return;
        }
        showConfirmCancelModal({
          onConfirm: handleCancel,
        });
      }}
      destroyOnClose
      maskClosable={false}
      afterClose={() => form.resetFields()}
      width={645}
    >
      <Form form={form} onFinish={handleSubmit} requiredMark={false} layout="vertical">
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item label="Đơn vị" name="pos" rules={[{ required: true, message: 'Vui lòng chọn đơn vị' }]}>
              <SingleSelectLazy
                apiQuery={getOrgChartDropdown}
                queryKey={['orgChart-exchanges']}
                keysLabel={'name'}
                enabled={open}
                placeholder="Chọn đơn vị"
                handleSelect={handleSelectSalePos}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              label="Chính sách phí - hoa hồng"
              name="salePolicy"
              rules={[{ required: true, message: 'Vui lòng chọn chính sách phí - hoa hồng' }]}
            >
              <SingleSelectLazy
                apiQuery={getSalesPolicyOfCommissionPeriod}
                queryKey={['sales-policy']}
                keysLabel={['code', 'name']}
                placeholder="Chọn chính sách phí - hoa hồng"
                handleSelect={handleSelectSalePolicy}
                disabled={!disabled}
                enabled={disabled}
                moreParams={{ name: pos?.name, isActive: 1 }}
                defaultValues={{
                  value: salePolicy?.id,
                  label: salePolicy?.name,
                }}
              />
            </Form.Item>
          </Col>

          <Col span={24}>
            <FormPeriod
              messageValidate="Vui lòng chọn kỳ tính phí"
              label="Kỳ tính phí"
              fieldPos="pos"
              clearFormValueDependency={() =>
                form.setFieldsValue({
                  salePolicy: undefined,
                })
              }
            />
          </Col>
        </Row>
      </Form>
    </Modal>
  );
};

export default CreateCommission;

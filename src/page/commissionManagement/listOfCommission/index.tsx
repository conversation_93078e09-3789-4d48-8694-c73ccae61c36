import { useMemo, useState } from 'react';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { PERMISSION_COMMISSION } from '../../../constants/permissions/commission';
import { useCheckPermissions, useFetch } from '../../../hooks';
import { getAllOfCommission, softDeleteCommission } from '../../../service/commission';
import { TFilterCommission, TListOfCommission } from '../../../types/commission';
import { columns } from './columns';
import { Button, Flex, TableColumnsType } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import FilterCommission from './FilterCommission';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { COMMISSION } from '../../../configs/path';
import CreateCommission from '../createCommission';
import './styles.scss';

const ListOfCommission = () => {
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListOfCommission>();
  const [isOpenModalCreate, setIsOpenModalCreate] = useState(false);
  const [filterParams, setFilterParams] = useState<TFilterCommission>();

  const {
    getById: isCheckGetById,
    publishGetId: isCheckPublishGetId,
    delete: isCheckDelete,
    create: isCheckCreate,
  } = useCheckPermissions(PERMISSION_COMMISSION);

  const { data, isFetching } = useFetch<TListOfCommission[]>({
    queryKeyArrWithFilter: ['list-of-commission', filterParams],
    api: getAllOfCommission,
    moreParams: { ...filterParams },
  });
  const dataCommission = data?.data?.data?.rows;

  const actionsColumns: TableColumnsType<TListOfCommission> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        fixed: 'right',
        render: (_, record: TListOfCommission) => {
          const isDelete =
            !record?.isPublish &&
            record?.adjustmentVersions?.every(item => item?.status === null || item?.status === 'NEW');

          const openViewDetail = (): void => {
            window.open(`${COMMISSION}/${record?.id}`, '_blank', 'noopener,noreferrer');
          };

          const handleDeleteCommission = (): void => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          return (
            <ActionsColumns
              handleViewDetail={isCheckGetById || isCheckPublishGetId ? openViewDetail : undefined}
              handleDelete={isCheckDelete && isDelete ? handleDeleteCommission : undefined}
            />
          );
        },
      },
    ];
  }, [isCheckDelete, isCheckGetById, isCheckPublishGetId]);

  const handleCancelModalCreate = () => {
    setIsOpenModalCreate(false);
  };

  return (
    <div className="wrapper-list-of-commission">
      <BreadCrumbComponent />
      <Flex justify="space-between" style={{ marginBottom: 16 }}>
        <FilterCommission setFilterParams={setFilterParams} />
        {isCheckCreate && (
          <Button type="primary" onClick={() => setIsOpenModalCreate(true)}>
            Tạo kỳ tính phí
          </Button>
        )}
      </Flex>
      <TableComponent
        queryKeyArr={['list-of-commission', filterParams]}
        loading={isFetching}
        columns={actionsColumns}
        dataSource={dataCommission}
        rowKey={'id'}
      />
      <ConfirmDeleteModal
        label="Đợt phí"
        open={isOpenModalDelete}
        apiQuery={softDeleteCommission as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-commission', filterParams]}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá đợt phí"
        description="Vui lòng nhập lý do muốn xoá đợt phí này"
      />
      <CreateCommission handleCancel={handleCancelModalCreate} open={isOpenModalCreate} />
    </div>
  );
};

export default ListOfCommission;

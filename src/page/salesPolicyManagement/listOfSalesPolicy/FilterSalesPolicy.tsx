import { Col, DatePicker, Form, Row } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useState } from 'react';
import DropdownFilterSearch from '../../../components/dropdown/dropdownFilterSearch';
import MutilSelectLazy from '../../../components/select/mutilSelectLazy';
import { DEFAULT_PARAMS, FORMAT_DATE, FORMAT_DATE_API } from '../../../constants/common';
import { sendGetListOfDropdownProjects } from '../../../service/salesPolicy';
import { IResponseObjMultiSelect } from '../../../types/common/common';
import { TFilterSalesPolicy } from '../../../types/salesPolicy';

const FilterSalesPolicy = ({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<TFilterSalesPolicy | undefined>>;
}) => {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const handleSubmitFilter = (values: TFilterSalesPolicy) => {
    const newFilter: Record<string, unknown> = {
      startCreatedDate: values?.startCreatedDate ? dayjs(values?.startCreatedDate).format(FORMAT_DATE_API) : null,
      endCreatedDate: values?.endCreatedDate ? dayjs(values?.endCreatedDate).format(FORMAT_DATE_API) : null,
      projectID: values?.projectID || null,
    };
    setFilterParams(prev => ({ ...prev, ...DEFAULT_PARAMS, ...newFilter }));

    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setFilterParams(prev => ({ search: prev?.search }));
      setIsOpenFilter(false);
    }, 100);
  };
  const onChangeSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, ...DEFAULT_PARAMS, search: search }));
  };

  const handleSelectProject = (values: IResponseObjMultiSelect[]) => {
    form.setFieldsValue({
      project: values
        ?.map(item => item?.option?.id)
        .filter(item => item !== undefined)
        .join(','),
    });
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        onChangeSearch={onChangeSearch}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Chọn dự án" name="projectID">
              <MutilSelectLazy
                apiQuery={sendGetListOfDropdownProjects}
                queryKey={['list-source']}
                enabled={isOpenFilter}
                placeholder="Chọn dự án"
                keysLabel={['name']}
                keysTag={'name'}
                handleListSelect={handleSelectProject}
              />
            </Form.Item>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="startCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const endCreatedDate = form.getFieldValue('endCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (endCreatedDate && current > dayjs(endCreatedDate).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="endCreatedDate">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const startCreatedDate = form.getFieldValue('startCreatedDate');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (startCreatedDate && current < dayjs(startCreatedDate).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterSalesPolicy;

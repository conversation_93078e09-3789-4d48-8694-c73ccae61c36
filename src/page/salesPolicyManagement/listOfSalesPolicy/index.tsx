import { ImportOutlined } from '@ant-design/icons';
import { App, But<PERSON>, notification, Space } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useMemo, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { SALES_POLICY } from '../../../configs/path';
import { PERMISSION_SALES_POLICY } from '../../../constants/permissions/commission';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import { changeStatusPolicy, getAllOfSalesPolicy, softDeleteSalesPolicy } from '../../../service/salesPolicy';
import { TFilterSalesPolicy, TListOfSalesPolicy } from '../../../types/salesPolicy';
import CreateSalePolicy from '../createSalePolicy';
import { columns } from './columns';
import FilterSalesPolicy from './FilterSalesPolicy';
import './styles.scss';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { useSalesPolicyStore } from '../store';

const ListOfSalesPolicy = () => {
  const { modal } = App.useApp();
  const {
    create: checkCreate,
    import: checkImport,
    getById: checkGetDetail,
    clone: checkClone,
    changeStatus: checkChangeStatus,
    delete: checkDelete,
  } = useCheckPermissions(PERMISSION_SALES_POLICY);

  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [currentRecord, setCurrentRecord] = useState<TListOfSalesPolicy>();
  const [filterParams, setFilterParams] = useState<TFilterSalesPolicy>();

  const { data, isLoading } = useFetch<TListOfSalesPolicy[]>({
    api: getAllOfSalesPolicy,
    queryKeyArrWithFilter: ['list-of-sales-policy', filterParams],
    moreParams: { ...filterParams },
  });
  const dataSalesPolicy = data?.data?.data?.rows || [];

  const { mutateAsync: updateActive } = useUpdateField({
    apiQuery: changeStatusPolicy,
    keyOfListQuery: ['list-of-sales-policy', filterParams],
    isMessageError: false,
    isMessageSuccess: false,
  });
  const { setActionModal } = useSalesPolicyStore();

  const columnsActions: ColumnsType<TListOfSalesPolicy> = useMemo(() => {
    return [
      ...columns,
      {
        title: 'Hành động',
        dataIndex: 'action',
        key: 'action',
        width: '100px',
        align: 'center',
        render: (_: unknown, record: TListOfSalesPolicy) => {
          const textModalConfirmActive = record?.active === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
          const handleActivePolicy = () => {
            return modalConfirm({
              modal: modal,
              title: `${textModalConfirmActive} chính sách`,
              content: `Bạn có muốn ${textModalConfirmActive} chính sách này không?`,
              handleConfirm: async () => {
                const res = await updateActive({
                  id: record?.id ?? '',
                  active: record?.active === 1 ? 2 : 1,
                });
                if (res?.data?.statusCode === '0') {
                  notification.success({ message: `${textModalConfirmActive} chính sách thành công` });
                  return Promise.resolve();
                } else {
                  notification.error({ message: `${textModalConfirmActive} chính sách Thất bại!` });
                  return Promise.reject();
                }
              },
            });
          };
          const handleDeleteRole = () => {
            setIsOpenModalDelete(true);
            setCurrentRecord(record);
          };

          const openViewDetail = () => {
            window.open(`${SALES_POLICY}/${record?.id}`, '_blank', 'noopener,noreferrer');
          };
          const cloneRole = () => {
            setActionModal({ isOpen: true, type: 'clone', id: record?.id });
          };

          return (
            <ActionsColumns
              handleViewDetail={checkGetDetail ? openViewDetail : undefined}
              handleCloneRole={checkClone ? cloneRole : undefined}
              textModalConfirmActive={textModalConfirmActive}
              handleActive={checkChangeStatus ? handleActivePolicy : undefined}
              handleDelete={checkDelete ? handleDeleteRole : undefined}
            />
          );
        },
      },
    ];
  }, [checkChangeStatus, checkClone, checkDelete, checkGetDetail, modal, setActionModal, updateActive]);

  const handleOpenModalCreate = () => {
    setActionModal({ isOpen: true, type: 'create' });
  };

  return (
    <div className="wrapper-list-of-sales-policy">
      <BreadCrumbComponent />
      <div className="header-filter-sales-policy" style={{ marginBottom: 16 }}>
        <FilterSalesPolicy setFilterParams={setFilterParams} />
        <Space size={16}>
          {checkImport && (
            <Button type="default" icon={<ImportOutlined />}>
              Nhập biểu mẫu
            </Button>
          )}
          {checkCreate && (
            <Button type="primary" onClick={handleOpenModalCreate}>
              Tạo mới
            </Button>
          )}
        </Space>
      </div>
      <TableComponent
        columns={columnsActions}
        dataSource={dataSalesPolicy}
        rowKey={'id'}
        queryKeyArr={['list-of-sales-policy', filterParams]}
        loading={isLoading}
      />
      <CreateSalePolicy />
      <ConfirmDeleteModal
        label="Chính sách"
        open={isOpenModalDelete}
        apiQuery={softDeleteSalesPolicy as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-sales-policy', filterParams]}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="Xoá chính sách"
        description="Vui lòng nhập lý do muốn xoá chính sách này"
      />
    </div>
  );
};

export default ListOfSalesPolicy;

import { Checkbox, Col, Form, Input, Row, Typography } from 'antd';
import { useEffect, useMemo, useState } from 'react';
import CurrencyInput from '../../../components/input/CurrencyInput';
import PercentInput from '../../../components/input/PercentInput';
import FormPeriod from '../../../components/select/FormPeriod';
import SingleSelectLazy from '../../../components/select/singleSelectLazy';
import { useFetch } from '../../../hooks';
import {
  sendGetDetailOfDropdownProject,
  sendGetListOfDropdownOrgCharts,
  sendGetListOfDropdownProjects,
} from '../../../service/salesPolicy';
import { TListDropdown } from '../../../types/salesPolicy';
import { useSalesPolicyStore } from '../store';

const { Item } = Form;
const { Title } = Typography;

const DetailedInfo = ({ enabled }: { enabled?: boolean }) => {
  const form = Form.useFormInstance();
  const { disabled, setIsModified, initialValue } = useSalesPolicyStore();

  const [defaultProject, setDefaultProject] = useState<
    { label: string | React.ReactElement; value: string } | undefined
  >();

  const { data } = useFetch<TListDropdown>({
    api: () => sendGetDetailOfDropdownProject({ id: initialValue?.listProjectId?.[0] }),
    queryKeyArrWithFilter: ['project-dropdown-list', initialValue?.listProjectId?.[0]],
    withFilter: false,
    enabled: !!initialValue?.listProjectId?.[0],
  });
  const dataProject = data?.data?.data;

  useEffect(() => {
    if (dataProject?.name && dataProject?.id) {
      setDefaultProject({ label: dataProject.name, value: dataProject.id });
    }
  }, [dataProject]);

  const formattedDefaultValue = useMemo(() => {
    const pos = initialValue?.listPos?.[0];
    return pos
      ? {
          label: pos?.name,
          value: pos?.id,
          name: pos?.name,
          code: pos?.code || '',
          id: pos?.id,
        }
      : undefined;
  }, [initialValue?.listPos]);

  const handleSelectProject = (value: TListDropdown) => {
    form.setFieldsValue({ listProjectId: value?.id ? [value?.id] : undefined });
    setIsModified(true);
    form.validateFields(['listProjectId']);
  };

  const handleSelectListPos = (value: TListDropdown) => {
    form.setFieldsValue({
      listPos: [
        value
          ? {
              name: value?.name,
              id: value?.id,
              code: value?.code,
            }
          : undefined,
      ],
    });
    setIsModified(true);
  };

  return (
    <div>
      <Title level={4}>Thông tin chi tiết</Title>
      <Row gutter={24}>
        <Col md={12} xs={24}>
          <Item
            label="Dự án áp dụng"
            name="listProjectId"
            required
            rules={[{ required: true, message: 'Vui lòng chọn dự án áp dụng' }]}
          >
            <SingleSelectLazy
              placeholder="Chọn dự án áp dụng"
              queryKey={['project-dropdown-list']}
              apiQuery={sendGetListOfDropdownProjects}
              keysLabel={'name'}
              handleSelect={handleSelectProject}
              enabled={!!enabled}
              disabled={!!disabled}
              defaultValues={defaultProject}
            />
          </Item>
        </Col>
        <Col md={12} xs={24}>
          <Item label="Đơn vị bán hàng" name="listPos">
            <SingleSelectLazy
              placeholder="Chọn đơn vị áp dụng"
              queryKey={['orgChart-dropdown-list']}
              apiQuery={sendGetListOfDropdownOrgCharts}
              keysLabel={'name'}
              handleSelect={handleSelectListPos}
              enabled={!!enabled}
              disabled={!!disabled}
              defaultValues={formattedDefaultValue}
            />
          </Item>
        </Col>
        <Col span={24}>
          <FormPeriod
            label="Kỳ tính phí"
            required
            disabled={!!disabled}
            fieldPos="listPos"
            messageValidate="Vui lòng chọn kỳ tính phí"
          />
        </Col>
        <Col md={12} xs={24}>
          <Item name="isapplyVAT" style={{ marginBottom: '12px' }} valuePropName="checked">
            <Checkbox disabled={disabled}>Tính từ giá VAT</Checkbox>
          </Item>
        </Col>
        <Col md={12} xs={24}></Col>
        <Col md={12} xs={24}>
          <Item label="Tỉ lệ doanh thu ghi nhận" name="revenueRate">
            <PercentInput placeholder="Nhập tỉ lệ doanh thu ghi nhận " suffix="%" disabled={disabled} />
          </Item>
        </Col>
        <Col md={12} xs={24}>
          <Item label="Tỉ lệ hoa hồng nhân viên hưởng" name="comrate">
            <PercentInput placeholder="Nhập tỉ lệ hoa hồng " suffix="%" disabled={disabled} />
          </Item>
        </Col>
        <Col md={12} xs={24}>
          <Item label="Số tiền thưởng" name="amount" normalize={value => value.replace(/[^0-9]/g, '')}>
            <CurrencyInput placeholder={'Nhập số tiền thưởng'} disabled={disabled} />
          </Item>
        </Col>
        <Col md={12} xs={24}>
          <Item label="Số tiền nhân viên hưởng" name="comamount">
            <CurrencyInput placeholder={'Nhập số tiền nhân viên hưởng'} disabled={disabled} />
          </Item>
        </Col>
        <Col span={24}>
          <Form.Item label="Ghi chú" name="comments">
            <Input.TextArea maxLength={500} rows={3} placeholder="Nhập ghi chú" disabled={disabled} />
          </Form.Item>
        </Col>
      </Row>
    </div>
  );
};

export default DetailedInfo;

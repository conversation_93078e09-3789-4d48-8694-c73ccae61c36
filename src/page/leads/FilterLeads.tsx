import { Col, DatePicker, Form, Row, Select } from 'antd';
import dayjs from 'dayjs';
import { Dispatch, SetStateAction, useState } from 'react';
import DropdownFilterSearch from '../../components/dropdown/dropdownFilterSearch';
import MutilSelectLazy from '../../components/select/mutilSelectLazy';
import { DEFAULT_PARAMS, FORMAT_DATE, FORMAT_DATE_API, TYPE_LEAD_SOURCE } from '../../constants/common';
import { getListLeadSource } from '../../service/leadSource';
import { IResponseObjMultiSelect } from '../../types/common/common';
import { TFilterLeadCommon } from '../../types/leadCommon';

const FilterLeads = ({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<TFilterLeadCommon | undefined>>;
}) => {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const handleSubmitFilter = (values: TFilterLeadCommon) => {
    const newFilter: Record<string, unknown> = {
      createdFrom: values?.createdFrom ? dayjs(values?.createdFrom).format(FORMAT_DATE_API) : null,
      createdTo: values?.createdTo ? dayjs(values?.createdTo).format(FORMAT_DATE_API) : null,
      isHot: values?.isHot || null,
      source: values?.source || null,
    };
    setFilterParams(prev => ({ ...prev, ...DEFAULT_PARAMS, ...newFilter }));
    setIsOpenFilter(false);
  };

  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectLeadSource = (values: IResponseObjMultiSelect[]) => {
    form.setFieldsValue({ source: values ? values?.map(item => item?.option?.name).join(',') : undefined });
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setIsOpenFilter(false);
      setFilterParams(prev => ({ search: prev?.search }));
    }, 100);
  };

  const onChangeSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, ...DEFAULT_PARAMS, search: search }));
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onChangeSearch={onChangeSearch}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Nguồn Lead" name="source">
              <MutilSelectLazy
                apiQuery={getListLeadSource}
                queryKey={['list-source']}
                enabled={isOpenFilter}
                placeholder="Chọn nguồn lead"
                keysLabel={['name']}
                handleListSelect={handleSelectLeadSource}
                keysTag={['name']}
              />
            </Form.Item>
            <Form.Item label="Loại Lead" name="isHot">
              <Select placeholder="Chọn loại Lead" allowClear options={TYPE_LEAD_SOURCE} />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Từ ngày" name="createdFrom">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const createdTo = form.getFieldValue('createdTo');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (createdTo && current > dayjs(createdTo).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="createdTo">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const createdFrom = form.getFieldValue('createdFrom');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (createdFrom && current < dayjs(createdFrom).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
};

export default FilterLeads;

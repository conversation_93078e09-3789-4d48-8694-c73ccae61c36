import { useState, useEffect, useMemo } from 'react';
import { Input, Button, Typography, Form, Tabs, Select, notification } from 'antd';
import { EditOutlined, EyeOutlined } from '@ant-design/icons';
import { getListTemplate } from '../../service/report';
import { useCreateField, useFetch } from '../../hooks';
import { FileTemplate, Pdf, Template } from '../../types/discountPolicy';
import PdfViewer from '../../components/pdf';
import { useSellProgramStore } from '../Store';
import { useProjectStore } from '../operateSellProgram/store';
import {
  sendCreateCustomerExploitationProposal,
  sendCreateProposal,
  sendGetPreInforProposal,
  getPdfProposal,
} from '../../service/transaction';
import { useNavigate } from 'react-router-dom';
import { PROPOSAL } from '../../configs/path';

const { Title } = Typography;
const { TextArea } = Input;
export interface File {
  name?: string;
  url?: string;
}

interface ProposalData {
  proposal: {
    id: string;
    name: string;
    code: string;
  };
}

const getIdentityNumber = (identities: Array<{ type?: string; value?: string }> = []) => {
  if (!identities.length) return '';

  const priorityOrder = ['CCCD', 'CMND', 'Hộ chiếu'];
  for (const type of priorityOrder) {
    const found = identities.find(id => id.type === type);
    if (found?.value) return found.value;
  }
  return identities[0]?.value || '';
};

const CreateProposal = () => {
  // Form & UI states
  const [form] = Form.useForm();
  const [activeTab, setActiveTab] = useState('edit');
  const [pdfBlob, setPdfBlob] = useState<Blob | null>(null);
  const [pdfBase64, setPdfBase64] = useState<string | null>(null);
  const [selectedTemplate, setSelectedTemplate] = useState<string | null>(null);
  const [hasError, setHasError] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Data states
  const [dataPreview, setDataPreview] = useState<any>(null);
  const [preInforData, setPreInforData] = useState<any>(null);

  // Constants
  const formId = '326ff2e3-3ca4-412a-b8fb-48bb56f15f7a';
  const navigate = useNavigate();

  // Store data
  const projectId = useSellProgramStore(state => state.projectId);
  const { employeeInfo, selectedCustomerStore } = useProjectStore();

  const { mutateAsync: getCreateProposal } = useCreateField<unknown>({
    apiQuery: sendCreateProposal,
    isMessageError: false,
    isMessageSuccess: false,
    isShowMessage: false,
  });

  const { data: preInforResponse } = useFetch({
    queryKeyArr: ['pre-infor-proposal'],
    api: () => sendGetPreInforProposal({ serviceType: 'CUSTOMER_EXPLOITATION_PROPOSAL' }),
    enabled: true,
  });

  const { data: templateData, isLoading: isLoadingTemplate } = useFetch<Template>({
    queryKeyArr: ['get-template', projectId, formId],
    api: () => getListTemplate({ projectId, formId }),
    enabled: !!projectId,
  });

  const { refetch: previewPdf } = useFetch<Pdf>({
    queryKeyArr: ['preview-pdf'],
    api: async () => {
      const payload = {
        employee: {
          id: employeeInfo?.id || null,
          name: employeeInfo?.name || null,
          code: employeeInfo?.code || null,
        },
        customer: {
          id: selectedCustomerStore?.id || null,
          code: selectedCustomerStore?.code || null,
          name: selectedCustomerStore?.name || null,
          identityNumber:
            selectedCustomerStore?.type === 'individual'
              ? getIdentityNumber(selectedCustomerStore?.personalInfo?.identities)
              : selectedCustomerStore?.personalInfo?.identities?.[0]?.value || null,
          phone: selectedCustomerStore?.personalInfo?.phone || null,
        },
        formId,
        fileUrl: selectedTemplate || null,
        eappNumber: preInforData?.eappCode || null,
        dear: form.getFieldValue('dear') || null,
      };

      const resp = await getPdfProposal(payload);
      setDataPreview(resp);
      return resp;
    },
    enabled: false,
  });

  const accountTakeCare = useMemo(() => {
    return (
      dataPreview?.accountTakeCare || {
        username: employeeInfo?.name || '',
        code: employeeInfo?.code || '',
        fullName: employeeInfo?.name || null,
        email: employeeInfo?.email || null,
        orgCode: employeeInfo?.orgCode || null,
        orgName: employeeInfo?.orgName || null,
        id: employeeInfo?.id || null,
      }
    );
  }, [dataPreview?.accountTakeCare, employeeInfo]);

  useEffect(() => {
    if (preInforResponse?.data?.statusCode === '0') {
      setPreInforData(preInforResponse.data.data);
    }
  }, [preInforResponse]);

  const templates = useMemo(() => {
    const data = templateData?.data?.data;
    return Array.isArray(data) ? data : [];
  }, [templateData?.data?.data]);

  useEffect(() => {
    if (templates.length > 0 && templates[0]?.files?.length > 0) {
      const defaultTemplate = templates[0].files[0].fileUrl;
      setSelectedTemplate(defaultTemplate);
      form.setFieldsValue({ filename: defaultTemplate });
    }
  }, [templates, form]);

  const handleTabChange = async (key: string) => {
    setActiveTab(key);

    if (key === 'preview') {
      if (!selectedTemplate) {
        notification.error({ message: 'Vui lòng chọn một mẫu tờ trình trước khi xem trước!' });
        setHasError(true);
        return;
      }

      try {
        const resp = await previewPdf();
        const filePDF = (resp.data?.data?.data as any)?.filePDF;

        if (resp?.data?.data?.statusCode === '0') {
          setPdfBlob(filePDF);
          setPdfBase64(typeof filePDF === 'string' ? filePDF : null);
          setHasError(false);
        } else {
          throw new Error('Tệp mẫu không đúng định dạng!');
        }
      } catch (error: unknown) {
        const errorMessage = error instanceof Error ? error.message : 'Đã xảy ra lỗi khi tải file PDF!';
        notification.error({ message: errorMessage });
        setPdfBlob(null);
        setPdfBase64(null);
        setHasError(true);
      }
    }
  };

  const handleTemplateChange = (value: string) => {
    setSelectedTemplate(value);
    setHasError(false);
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;

    try {
      setIsSubmitting(true);
      await form.validateFields();

      if (!preInforData?.eappCode || !preInforData?.proDefId) {
        notification.error({
          message: 'Thiếu thông tin tờ trình',
          description: 'Không tìm thấy eappCode hoặc procDefId. Vui lòng tải lại trang.',
        });
        return;
      }

      // Step 1: Generate PDF
      const pdfPayload = {
        employee: {
          id: employeeInfo?.id || '',
          name: employeeInfo?.name || '',
          code: employeeInfo?.code || '',
        },
        customer: {
          id: selectedCustomerStore?.id || '',
          code: selectedCustomerStore?.code || '',
          name: selectedCustomerStore?.name || '',
          identityNumber:
            selectedCustomerStore?.type === 'individual'
              ? getIdentityNumber(selectedCustomerStore?.personalInfo?.identities)
              : selectedCustomerStore?.personalInfo?.identities?.[0]?.value || '',
          phone: selectedCustomerStore?.personalInfo?.phone || '',
        },
        formId,
        fileUrl: selectedTemplate || '',
        eappNumber: preInforData?.eappCode,
        dear: form.getFieldValue('dear') || '',
        output: 'link',
      };

      // await getPdfProposal(pdfPayload);
      const resp = await getPdfProposal(pdfPayload);
      const responseDataPreview = resp.data?.data.filePDF;
      // Step 2: Create proposal
      const proposalPayload = {
        eappNumber: preInforData?.eappCode,
        procDefId: preInforData?.proDefId,
        customerId: selectedCustomerStore?.id,
        type: 'CUSTOMER_EXPLOITATION_PROPOSAL',
        accountTakeCare,
      };

      const proposalResp = await getCreateProposal(proposalPayload);

      if (proposalResp?.data?.statusCode !== '0') {
        throw new Error(proposalResp?.data?.message || 'Gửi tờ trình thất bại!');
      }

      const proposalData = proposalResp.data.data as ProposalData;

      // Step 3: Create customer exploitation proposal
      const customerPayload = {
        name: proposalData?.proposal?.name || '',
        code: proposalData?.proposal?.code || '',
        eappNumber: preInforData?.eappCode,
        urlFile: responseDataPreview.Location || '',
        urlCrm: `${import.meta.env.VITE_API_BASE_URL}proposal/${proposalData?.proposal?.id || ''}`,
        fileName: (() => {
          const fullFileName = responseDataPreview.Key || '';
          if (!fullFileName) return '';

          const parts = fullFileName.split('/');
          const fileName = parts[parts.length - 1] || '';

          return fileName.replace(/\.pdf$/, '');
        })(),
        fileType: 'pdf',
        procDefId: preInforData.proDefId,
        account: 'nve100000130',
        updatedBy: '<EMAIL>',
        proposalId: proposalData?.proposal?.id,
      };

      const customerResp = await sendCreateCustomerExploitationProposal(customerPayload);

      if (customerResp?.data?.statusCode === '0') {
        notification.success({
          message: 'Tạo tờ trình khai thác thành công!',
        });
        navigate(PROPOSAL);
      } else {
        throw new Error(customerResp?.data?.message || 'Tạo tờ trình khai thác thất bại!');
      }
    } catch (error: unknown) {
      console.error('Submit error:', error);
      const errorMessage = error instanceof Error ? error.message : 'Có lỗi xảy ra!';
      notification.error({
        message: errorMessage,
        description: 'Vui lòng thử lại sau',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  // Render
  const tabItems = [
    {
      key: 'edit',
      label: (
        <div>
          <EditOutlined />
          <span style={{ marginLeft: 10 }}>Soạn thảo</span>
        </div>
      ),
      children: (
        <Form
          form={form}
          labelAlign="left"
          style={{ maxWidth: 600 }}
          labelCol={{ xs: { span: 24 }, sm: { span: 24 }, md: { span: 6 }, lg: { span: 6 }, xl: { span: 5 } }}
          wrapperCol={{ xs: { span: 24 }, sm: { span: 24 }, md: { span: 18 }, lg: { span: 18 }, xl: { span: 19 } }}
        >
          <Form.Item
            label="Tên tệp mẫu"
            name="filename"
            rules={[{ required: true, message: 'Vui lòng chọn tên tệp mẫu' }]}
          >
            <Select
              style={{ width: '100%' }}
              placeholder="Chọn mẫu tờ trình"
              loading={isLoadingTemplate}
              value={selectedTemplate}
              onChange={handleTemplateChange}
              options={
                templates[0]?.files?.map((item: FileTemplate) => ({
                  value: item.fileUrl,
                  label: item.fileName,
                })) || []
              }
              notFoundContent="Không tìm thấy mẫu tờ trình"
            />
          </Form.Item>

          <Form.Item label="Kính gửi" name="dear">
            <TextArea rows={2} placeholder="Nhập nội dung kính gửi" maxLength={255} />
          </Form.Item>
        </Form>
      ),
    },
    {
      key: 'preview',
      label: (
        <div>
          <EyeOutlined />
          <span style={{ marginLeft: 10 }}>Xem trước</span>
        </div>
      ),
      children: <div style={{ marginTop: 33 }}>{pdfBlob && <PdfViewer pdfBase64={pdfBase64} />}</div>,
    },
  ];

  return (
    <div>
      <Title level={5} style={{ marginBottom: 30 }}>
        Tạo mới tờ trình xin khai thác
      </Title>

      <Tabs
        activeKey={activeTab}
        onChange={handleTabChange}
        items={tabItems}
        tabBarStyle={{ marginBottom: 16 }}
        renderTabBar={() => (
          <div style={{ display: 'flex', gap: 8, marginBottom: 16 }}>
            {tabItems.map(item => (
              <Button
                key={item.key}
                onClick={() => handleTabChange(item.key)}
                style={{
                  backgroundColor: activeTab === item.key ? '#f0f0f0' : '#fff',
                  border: activeTab === item.key ? 'none' : '1px solid #d9d9d9',
                  borderRadius: 4,
                  padding: '4px 12px',
                  display: 'flex',
                  alignItems: 'center',
                  gap: 4,
                }}
              >
                {item.label}
              </Button>
            ))}
          </div>
        )}
      />

      <div className="create-footer">
        <div className="button-create">
          <Button
            type="primary"
            onClick={handleSubmit}
            loading={isSubmitting}
            disabled={hasError || !selectedTemplate || activeTab === 'edit'}
          >
            {isSubmitting ? 'Đang gửi...' : 'Gửi tờ trình'}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CreateProposal;

import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Row,
  Select,
  Spin,
  Switch,
  TableColumnsType,
  Tag,
  TimePicker,
  Typography,
} from 'antd';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { QueryKey } from '@tanstack/react-query';
import ModalComponent from '../../../../../components/modal';
import { daysOfWeek, defaultWorkingTimes, OPTIONS_STATUS_SALE_PROGRAM } from '../../../../../constants/common';
import dayjs, { Dayjs } from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';

dayjs.extend(isSameOrAfter);
import { CheckboxChangeEvent } from 'antd/es/checkbox';
import {
  CycleWorkingTime,
  DetailProject,
  IBlock,
  ListĐTHT,
  ProjectSaleProgram,
  SelectValueDVHB,
  WorkingTimeConvert,
} from '../../../../../types/project/project';
import { useCreateField, useFetch } from '../../../../../hooks';
import {
  getBlockByProjectId,
  getListOrgchartProject,
  getListĐTHT,
  sendCreateProjectSaleProgram,
} from '../../../../../service/project';
import { handleKeyDownBlockSpecialCharacters } from '../../../../../utilities/regex';
import { useParams } from 'react-router-dom';
import TableComponent from '../../../../../components/table';
import { debounce } from 'lodash';
import { useSellProgramStore } from '../../../../Store';

const { Title } = Typography;

interface FormModalProjectProps {
  visible: boolean;
  onClose: () => void;
  keyQuery: QueryKey;
  dataProject?: DetailProject;
}

export interface SalesUnitType {
  id: string;
  code?: string;
  nameVN?: string;
  externalOrgcharts?: ExternalOrgChart[];
}

export interface ExternalOrgChart {
  partnershipName: string;
  partnershipCode: string;
  id: string;
}

const FormModalSellProgram = ({ visible, onClose, keyQuery, dataProject }: FormModalProjectProps) => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const [isWorkingTime, setIsWorkingTime] = useState(false);
  const [countByWorkingHours, setCountByWorkingHours] = useState(false);
  const [isCustomerConfirmRequired, setIsCustomerConfirmRequired] = useState(false);
  const [workingTimeConvert, setWorkingTimeConvert] = useState<WorkingTimeConvert[]>(defaultWorkingTimes);
  const [cycleWorkingTime, setCycleWorkingTime] = useState<CycleWorkingTime>({
    fromDay: '',
    toDay: '',
    timeRange: [null, null],
  });
  const [orgChartProject, setOrgChartProject] = useState<SalesUnitType[]>([
    { id: '', nameVN: '', code: '', externalOrgcharts: [] },
  ]);
  const [selectedValue, setSelectedValue] = useState<SelectValueDVHB | null>(null);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [searchPartnerShip, setSearchPartnerShip] = useState<string>('');
  const [currentListĐTHT, setCurrentListĐTHT] = useState<ListĐTHT[]>();
  const [partnerCodeId, setPartnerCodeId] = useState<string>('');
  const [selectedUnits, setSelectedUnits] = useState<string[]>([]);
  const [selectedRow, setSelectedRow] = useState<string | null>(null);
  const [searchSaleUnit, setSearchSaleUnit] = useState<string>('');
  const salesProgramIds = useSellProgramStore(state => state.ArrSalesProgramIds);

  const { data: dataSaleUnitOfProject, isLoading: LoadingListSaleUnitOfProject } = useFetch<SalesUnitType[]>({
    queryKeyArr: ['orgchart-tab', id, searchSaleUnit],
    api: getListOrgchartProject,
    moreParams: { id: id, search: searchSaleUnit },
  });
  const listSaleUnitOfProject = dataSaleUnitOfProject?.data?.data;

  // Fetch danh sách ĐTHT
  const { data: listĐTHT, isLoading: loadingPartnerShip } = useFetch<ListĐTHT[]>({
    queryKeyArrWithFilter: ['list-ĐTHT', partnerCodeId || '', searchPartnerShip],
    api: getListĐTHT,
    enabled: !!partnerCodeId, // Chỉ fetch khi partnerCodeId có giá trị hợp lệ
    moreParams: { partnerCode: partnerCodeId, search: searchPartnerShip },
  });
  // Fetch list Block
  const { data: dataBlocks } = useFetch<IBlock[]>({
    queryKeyArr: ['getBlockByProjectId', id],
    api: () => getBlockByProjectId({ idProject: id }),
    withFilter: false,
  });

  const handleTimeChange = (index: number, value: [Dayjs | null, Dayjs | null] | null) => {
    const updatedRange: [Dayjs | null, Dayjs | null] = value ?? [null, null];
    setWorkingTimeConvert(prev => {
      const newWorkingTime = [...prev];
      newWorkingTime[index].timeRange = updatedRange;
      return newWorkingTime;
    });
  };

  const handleCycleWorkingTimeChange = (field: string, value: unknown[] | string) => {
    setCycleWorkingTime(prevState => ({
      ...prevState,
      [field]: value,
    }));
  };

  const [checkboxValues, setCheckboxValues] = useState({
    lockConfirmable: false,
    saleUnitLockConfirmable: false,
    combineSaleUnitConfirmable: false,
    allowSendCustomerStartPriority: false,
    isSuccessTransactionConfirmable: false,
    notSetPriority: false,
    customerConfirmRequired: false,
    allowBookingPriority: false,
    allowViewAllUnitProcessing: false,
    hasPopupAdminConfirm: false,
    holdPropertyWhenUnregister: false,
    unCountPermissionNumPropertyOfUnit: false,
    allowViewPricePublic: false,
    skipCountHasCustomer: false,
  });

  const createProjectSaleProgram = useCreateField<ProjectSaleProgram>({
    keyOfListQuery: keyQuery,
    keyOfDetailQuery: ['get-list-payment-transaction', id, salesProgramIds],
    apiQuery: sendCreateProjectSaleProgram,
    label: 'chương trình bán hàng',
    isMessageError: false,
  });

  const handleSwitchChange = (checked: boolean) => {
    setCountByWorkingHours(checked);
  };
  const handleCustomerConfirmChange = (e: CheckboxChangeEvent) => {
    const { checked } = e.target;
    setIsCustomerConfirmRequired(checked);
    setCheckboxValues(prev => ({
      ...prev,
      customerConfirmRequired: checked,
    }));
  };

  const handleCancel = useCallback(() => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu đang chưa được lưu, bạn có chắc muốn hủy dữ liệu đang nhập không?',
        cancelText: 'Quay lại',
        onOk: () => {
          form.resetFields();
          setWorkingTimeConvert(defaultWorkingTimes);
          setCycleWorkingTime({
            fromDay: '',
            toDay: '',
            timeRange: [null, null],
          });
          setOrgChartProject([{ id: '', nameVN: '', code: '', externalOrgcharts: [] }]);
          onClose();
        },
        okButtonProps: {
          type: 'default',
        },
        cancelButtonProps: {
          type: 'primary',
        },
      });
    } else {
      form.resetFields();
      setWorkingTimeConvert(defaultWorkingTimes);
      setCycleWorkingTime({
        fromDay: '',
        toDay: '',
        timeRange: [null, null],
      });
      setOrgChartProject([{ id: '', nameVN: '', code: '', externalOrgcharts: [] }]);
      onClose();
    }
  }, [form, onClose]);

  // chuyển đổi dạng data hiển thị thành dạng data gửi xuống Backend
  const convertDataWorkingTime = (data: WorkingTimeConvert[]) => {
    return data.map(item => ({
      name: item.name,
      startTime: item.timeRange[0]?.format('HH:mm:ss') || null,
      endTime: item.timeRange[1]?.format('HH:mm:ss') || null,
    }));
  };

  const handleDateChange = (date: Dayjs | null) => {
    form.setFieldsValue({ openingTimeSale: date });
    form.setFieldsValue({ endTimeSale: null });
  };
  const handleFinish = async (values: ProjectSaleProgram) => {
    let workingTime = null;
    if (isWorkingTime) {
      const { fromDay, toDay, timeRange } = cycleWorkingTime;

      const startIdx = daysOfWeek.indexOf(fromDay);
      const endIdx = daysOfWeek.indexOf(toDay);

      if (startIdx === -1 || endIdx === -1 || startIdx > endIdx) {
        return;
      }

      // danh sách các ngày đã chọn.
      const selectedDays = daysOfWeek.slice(startIdx, endIdx + 1);
      workingTime = selectedDays.map(day => ({
        name: day,
        startTime: timeRange[0]?.format('HH:mm:ss') || null,
        endTime: timeRange[1]?.format('HH:mm:ss') || null,
      }));
    } else {
      workingTime = convertDataWorkingTime(workingTimeConvert);
    }
    const internalOrgCharts = orgChartProject
      .filter(item => item.id !== '')
      .map(item => ({
        id: item.id,
        externalOrgchartIds: item.externalOrgcharts?.map(org => org.id) || [],
      }));
    const [openingTimeSale, endTimeSale] = values.saleTimeRange || [null, null];
    const transformedValues = {
      ...values,
      project: { id: dataProject?.id || '' },
      isWorkingTime,
      workingTime: workingTime,
      sharedPos: values.sharedPos || [],
      externalOrgPos: values.externalOrgPos || [],
      priceStatus: values.priceStatus || [],
      countByWorkingHours: countByWorkingHours,
      internalOrgcharts: internalOrgCharts,
      openingTimeSale: openingTimeSale ? openingTimeSale.format('YYYY-MM-DD') : null,
      endTimeSale: endTimeSale ? endTimeSale.format('YYYY-MM-DD') : null,
      blocks: values.blocks,
      ...checkboxValues,
    } as unknown as ProjectSaleProgram;
    const res = await createProjectSaleProgram.mutateAsync(transformedValues);
    const statusCode = res?.data?.statusCode;
    if (statusCode === '0') {
      form.resetFields();
      onClose();
    }
  };

  const handleSearch = useMemo(
    () =>
      debounce((value: string) => {
        setSearchSaleUnit(value);
      }, 300),
    [],
  );

  // Xử lý mở popup chọn ĐTHT
  const showPopup = useCallback(
    (id: string) => {
      const currentRow = orgChartProject.find(item => item.id === id);
      if (currentRow) {
        setSelectedUnits(currentRow.externalOrgcharts?.map(org => org.id) || []);
      }
      setSelectedRow(id);
      setIsModalVisible(true);
    },
    [orgChartProject],
  );

  const handleCheckboxChange = useCallback((e: CheckboxChangeEvent) => {
    const { name, checked } = e.target;
    if (name) {
      setCheckboxValues(prevState => ({
        ...prevState,
        [name]: checked,
      }));
    }
  }, []);

  // Xử lý thay đổi checkbox
  const handleCheckbox = useCallback((unitId: string, checked: boolean) => {
    setSelectedUnits(prev => (checked ? [...prev, unitId] : prev.filter(id => id !== unitId)));
  }, []);
  // Nếu có ID của ĐVBH thì hiển thị danh sách ĐTHT của ĐVBH đó
  useEffect(() => {
    if (partnerCodeId && listĐTHT?.data?.data?.rows) {
      setCurrentListĐTHT(listĐTHT.data.data.rows);
    }
  }, [partnerCodeId, listĐTHT]);
  const handleSearchPartnerShip = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const keyword = e.target.value.toLowerCase();
    setSearchPartnerShip(keyword);
  }, []);

  const handleRemoveUnit = (rowId: string, unitId: string) => {
    setOrgChartProject(prev =>
      prev.map(item =>
        item.id === rowId
          ? {
              ...item,
              externalOrgcharts: item.externalOrgcharts?.filter(unit => unit.id !== unitId) || [],
            }
          : item,
      ),
    );
  };

  //handleDeleteOrgchartProject
  const handleDeleteOrgchartProject = (id: string) => {
    setOrgChartProject(prev => prev.filter(item => item.id !== id));
  };

  const handleSelectĐVHB = async (value: SelectValueDVHB, option: unknown) => {
    if (!value) return;

    const orgChartId = value.value; // ID thật của ĐVBH
    const selectedUnit = option; // Thông tin ĐVBH

    // Nếu là ID khởi tạo, không gửi request và chỉ cập nhật dữ liệu ở UI
    setOrgChartProject(prev => [
      ...prev.filter(item => item.id !== ''), // Giữ lại phần tử cũ không có ID khởi tạo
      {
        id: orgChartId,
        nameVN: (selectedUnit as SalesUnitType).nameVN,
        code: (selectedUnit as SalesUnitType).code,
        externalOrgcharts: prev.find(item => item.id === orgChartId)?.externalOrgcharts || [], // Giữ lại externalOrgcharts của phần tử cũ
      }, // Thêm phần tử mới
      { id: '', nameVN: '', code: '', externalOrgcharts: [] },
    ]);
    setSelectedValue(null);
  };
  useEffect(() => {
    if (orgChartProject.length === 0) {
      setOrgChartProject([{ id: '', nameVN: '', code: '', externalOrgcharts: [] }]);
    }
  }, [orgChartProject]);
  const filteredOptions = listSaleUnitOfProject?.filter(item => !orgChartProject.some(org => org.id === item.id));

  const handleSaveUnits = async () => {
    if (selectedRow) {
      // Lấy phần tử có id là selectedRow
      const currentRow = orgChartProject.find(item => item.id === selectedRow);

      if (currentRow) {
        setOrgChartProject(prev =>
          prev.map(item =>
            item.id === selectedRow
              ? {
                  ...item,
                  externalOrgcharts:
                    currentListĐTHT
                      ?.filter(unit => selectedUnits.includes(unit.id)) // Chỉ lấy đơn vị còn được chọn
                      .map(unit => ({
                        id: unit.id,
                        partnershipName: unit.partnershipName,
                        partnershipCode: unit.partnershipCode,
                      })) || [],
                }
              : item,
          ),
        );
      }
    }
    setIsModalVisible(false);
  };

  const columns: TableColumnsType<SalesUnitType> = [
    {
      title: 'Tên ĐVBH',
      dataIndex: 'nameVN',
      key: 'nameVN',
      width: 491,
      render: value =>
        value ? (
          <p>{value}</p>
        ) : (
          <Select
            placeholder="Thêm đơn vị bán hàng"
            allowClear
            filterOption={false} // Không dùng lọc client-side, để lọc bằng API
            options={filteredOptions?.map((item: SalesUnitType) => ({
              value: item.id,
              label: item.code + ' - ' + item.nameVN,
              ...item,
            }))}
            onChange={handleSelectĐVHB}
            style={{ width: 250 }}
            labelInValue
            showSearch
            onSearch={handleSearch}
            value={selectedValue}
            loading={LoadingListSaleUnitOfProject}
            dropdownRender={menu => (
              <>
                {menu}
                {LoadingListSaleUnitOfProject && (
                  <div style={{ textAlign: 'center', padding: 10 }}>
                    <Spin size="small" />
                  </div>
                )}
              </>
            )}
          />
        ),
    },
    {
      title: 'Mã ĐVBH',
      key: 'code',
      dataIndex: 'code',
      width: 140,
      render: value => <p>{value}</p>,
    },
    {
      title: 'Tên ĐTHT (F2)',
      key: 'externalOrgchartIds',
      dataIndex: 'externalOrgchartIds',
      render: (_, record) =>
        record.nameVN ? (
          <>
            {record.externalOrgcharts?.map(partner => (
              <Tag
                key={partner.partnershipCode}
                closable
                onClose={() => handleRemoveUnit(record.id, partner.id)}
                style={{
                  fontSize: 12,
                  border: '1px solid rgba(0, 0, 0, 0.15)',
                  borderRadius: '4px',
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  margin: '0 5px 5px 0',
                }}
              >
                {partner.partnershipName}
              </Tag>
            ))}

            <Button
              color="default"
              size="small"
              style={{
                gap: 4,
                padding: '6px 12px',
                fontSize: 12,
                borderRadius: 4,
                height: 22,
                margin: '0 5px 5px 0',
              }}
              onClick={() => {
                setPartnerCodeId(record.id);
                showPopup(record.id);
              }}
            >
              Thêm ĐTHT +
            </Button>
          </>
        ) : null,
    },
    {
      key: 'action',
      align: 'center',
      width: 100,
      render: (_, record) =>
        record.nameVN ? (
          <Button type="link" danger onClick={() => handleDeleteOrgchartProject(record.id)}>
            Xóa
          </Button>
        ) : null,
    },
  ];

  return (
    <ModalComponent
      title={'Tạo mới chương trình bán hàng'}
      open={visible}
      onCancel={handleCancel}
      destroyOnClose
      footer={
        <Button type="primary" htmlType="submit" onClick={() => form.submit()}>
          Lưu
        </Button>
      }
    >
      <Form form={form} layout="vertical" colon={false} labelAlign="left" onFinish={handleFinish}>
        <Row gutter={126}>
          <Col xs={24} lg={12}>
            <Row gutter={[24, 0]}>
              <Col xs={24} md={16} lg={16}>
                <Form.Item label="Dự án" name="projectName" required>
                  <Input disabled placeholder="Nhập tên dự án" defaultValue={dataProject?.name} />
                </Form.Item>
              </Col>
              <Col xs={24} md={8} lg={8}>
                <Form.Item
                  label="Trạng thái"
                  name="status"
                  rules={[{ required: true, message: 'Vui lòng chọn trạng thái' }]}
                >
                  <Select
                    placeholder="Chọn trạng thái"
                    options={OPTIONS_STATUS_SALE_PROGRAM.map(item => {
                      return {
                        value: item.value,
                        label: item.label,
                      };
                    })}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={16} lg={16}>
                <Form.Item
                  label="Tên chương trình"
                  name="name"
                  rules={[
                    {
                      required: true,
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập tên chương trình'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập tên chương trình"
                    maxLength={50}
                    onBlur={e => {
                      form.setFieldsValue({ name: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={8} lg={8}>
                <Form.Item
                  label="Mã chương trình"
                  name="code"
                  rules={[{ required: true, message: 'Vui lòng nhập mã chương trình' }]}
                >
                  <Input
                    placeholder="Nhập mã chương trình"
                    onKeyDown={e => {
                      if (e.key === ' ') {
                        e.preventDefault();
                      }
                      handleKeyDownBlockSpecialCharacters(e);
                    }}
                    maxLength={20}
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Thời gian" name="saleTimeRange">
                  <DatePicker.RangePicker
                    placeholder={['Chọn ngày bắt đầu', 'Chọn ngày kết thúc']}
                    format={'DD/MM/YYYY'}
                    allowEmpty={[false, true]}
                    onChange={dates => {
                      handleDateChange(dates ? dates[0] : null);
                      if (dates) {
                        form.setFieldsValue({
                          openingTimeSale: dates[0],
                          endTimeSale: dates[1],
                        });
                      } else {
                        form.setFieldsValue({
                          openingTimeSale: null,
                          endTimeSale: null,
                        });
                      }
                    }}
                    value={
                      form.getFieldValue('openingTimeSale') && form.getFieldValue('endTimeSale')
                        ? [dayjs(form.getFieldValue('openingTimeSale')), dayjs(form.getFieldValue('endTimeSale'))]
                        : null
                    }
                  />
                </Form.Item>
              </Col>
              <Col xs={24} md={12} lg={12}>
                <Form.Item label="Block" name="blocks" rules={[{ required: true, message: 'Vui lòng chọn block' }]}>
                  <Select
                    allowClear
                    placeholder="Chọn block"
                    mode="multiple"
                    options={dataBlocks?.data?.data?.map(item => ({ value: item.id, label: item.block }))}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Tự động trả về sản phẩm đăng ký (phút)" name="dwellTime">
                  <InputNumber
                    placeholder="Nhập tự động trả về sản phẩm đăng ký (phút)"
                    min={0}
                    maxLength={3}
                    step={1}
                    parser={value => Math.floor(Number(value))}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Thời gian book lại sản phẩm đăng ký (phút)" name="dwellTimeReBooking">
                  <InputNumber
                    placeholder="Nhập thời gian book lại sản phẩm đăng ký (phút)"
                    min={0}
                    maxLength={3}
                    step={1}
                    parser={value => Math.floor(Number(value))}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Tự động trả về sản phẩm đã xác nhận (phút)" name="dwellTimeBookingPriority">
                  <InputNumber
                    placeholder="Nhập tự động trả về sản phẩm đã xác nhận (phút)"
                    min={0}
                    maxLength={3}
                    step={1}
                    parser={value => Math.floor(Number(value))}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Chiết khấu" name="discount">
                  <InputNumber placeholder="Nhập chiết khẩu" min={0} maxLength={2} />
                </Form.Item>
              </Col>
              <Col>
                <Form.Item layout="horizontal" label="Thời gian làm việc" name="countByWorkingHours">
                  <Switch style={{ marginLeft: 20 }} onChange={handleSwitchChange} />
                </Form.Item>
              </Col>
              {countByWorkingHours && (
                <>
                  <Col span={24}>
                    <Form.Item>
                      <Checkbox checked={isWorkingTime} onChange={value => setIsWorkingTime(value.target.checked)}>
                        Theo chu kỳ
                      </Checkbox>
                    </Form.Item>
                  </Col>
                  {isWorkingTime ? (
                    <>
                      <Col span={12}>
                        <Form.Item label="Ngày làm việc trong tuần">
                          <Select
                            allowClear
                            placeholder="Chọn từ thứ"
                            value={cycleWorkingTime.fromDay || null}
                            options={daysOfWeek.map(day => ({ value: day, label: day }))}
                            onChange={value => handleCycleWorkingTimeChange('fromDay', value)}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item>
                          <Select
                            allowClear
                            placeholder="Chọn đến thứ"
                            style={{ marginTop: 30 }}
                            value={cycleWorkingTime.toDay || null}
                            options={daysOfWeek.map(day => ({ value: day, label: day }))}
                            onChange={value => handleCycleWorkingTimeChange('toDay', value)}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={24}>
                        <Form.Item label="Giờ làm việc">
                          <TimePicker.RangePicker
                            placeholder={['Chọn giờ bắt đầu', 'Chọn giờ kết thúc']}
                            value={
                              cycleWorkingTime.timeRange[0] && cycleWorkingTime.timeRange[1]
                                ? [dayjs(cycleWorkingTime.timeRange[0]), dayjs(cycleWorkingTime.timeRange[1])]
                                : [null, null]
                            }
                            onChange={value => {
                              if (value && value[0] && value[1]) {
                                handleCycleWorkingTimeChange('timeRange', [value[0], value[1]]);
                              } else {
                                handleCycleWorkingTimeChange('timeRange', [null, null]);
                              }
                            }}
                          />
                        </Form.Item>
                      </Col>
                    </>
                  ) : (
                    <>
                      {workingTimeConvert.map((day, index) => (
                        <Col span={24} key={day.name}>
                          <Form.Item label={daysOfWeek[index]}>
                            <TimePicker.RangePicker
                              value={day.timeRange}
                              format="HH:mm:ss"
                              onChange={value => handleTimeChange(index, value)}
                            />
                          </Form.Item>
                        </Col>
                      ))}
                    </>
                  )}
                </>
              )}
            </Row>
          </Col>
          <Col xs={24} lg={12} style={{ marginTop: 20 }}>
            <Form.Item name="lockConfirmable" valuePropName="checked">
              <Checkbox name="lockConfirmable" onChange={handleCheckboxChange}>
                Bổ sung hồ sơ sau
              </Checkbox>
            </Form.Item>
            <Form.Item name="saleUnitLockConfirmable" valuePropName="checked">
              <Checkbox name="saleUnitLockConfirmable" onChange={handleCheckboxChange}>
                Đơn vị bán hàng xác nhận giao dịch
              </Checkbox>
            </Form.Item>
            <Form.Item name="combineSaleUnitConfirmable" valuePropName="checked">
              <Checkbox name="combineSaleUnitConfirmable" onChange={handleCheckboxChange}>
                Gộp xác nhận và xác nhận bổ sung hồ sơ sau
              </Checkbox>
            </Form.Item>
            <Form.Item name="allowSendCustomerStartPriority" valuePropName="checked">
              <Checkbox name="allowSendCustomerStartPriority" onChange={handleCheckboxChange}>
                Cho phép gửi HS KH khi bắt đầu ưu tiên
              </Checkbox>
            </Form.Item>
            <Form.Item name="isSuccessTransactionConfirmable" valuePropName="checked">
              <Checkbox name="isSuccessTransactionConfirmable" onChange={handleCheckboxChange}>
                Gửi email xác nhận giao dịch thành công
              </Checkbox>
            </Form.Item>
            <Form.Item name="notSetPriority" valuePropName="checked">
              <Checkbox name="notSetPriority" onChange={handleCheckboxChange}>
                Chương trình bán hàng không ráp ưu tiên
              </Checkbox>
            </Form.Item>
            <Form.Item name="customerConfirmRequired" valuePropName="checked">
              <Checkbox
                name="customerConfirmRequired"
                checked={checkboxValues.customerConfirmRequired}
                onChange={handleCustomerConfirmChange}
              >
                Gửi sms cho khách hàng xác thực
              </Checkbox>
            </Form.Item>
            {isCustomerConfirmRequired && (
              <>
                <Form.Item
                  name="customerSmsExpiredTime"
                  label="Thời gian hiệu lực sms"
                  rules={[{ required: true, message: 'Vui lòng nhập thời gian hiệu lực sms' }]}
                >
                  <InputNumber placeholder="Nhập thời gian hiệu lực sms" maxLength={3} />
                </Form.Item>
                <Form.Item
                  name="customerSmsContent"
                  label="Nội dung sms gửi khách hàng xác nhận"
                  required
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(new Error('Vui lòng nhập nội dung sms gửi khách hàng xác nhận'));
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập nội dung sms gửi khách hàng xác nhận"
                    maxLength={255}
                    onBlur={e => {
                      form.setFieldsValue({ customerSmsContent: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
                <Form.Item
                  name="customerSmsContentSuccess"
                  label="Nội dung sms gửi khách hàng xác nhận thành công"
                  required
                  rules={[
                    {
                      validator: (_, value) => {
                        if (!value || value.trim() === '') {
                          return Promise.reject(
                            new Error('Vui lòng nhập nội dung sms gửi khách hàng xác nhận thành công'),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <Input
                    placeholder="Nhập nội dung sms gửi khách hàng xác nhận thành công"
                    maxLength={255}
                    onBlur={e => {
                      form.setFieldsValue({ customerSmsContentSuccess: e.target.value.trim() });
                    }}
                  />
                </Form.Item>
              </>
            )}
            <Form.Item name="allowBookingPriority" valuePropName="checked">
              <Checkbox name="allowBookingPriority" onChange={handleCheckboxChange}>
                Đặt chỗ chọn sản phẩm
              </Checkbox>
            </Form.Item>
            <Form.Item name="allowViewAllUnitProcessing" valuePropName="checked">
              <Checkbox name="allowViewAllUnitProcessing" onChange={handleCheckboxChange}>
                Đơn vị bán hàng nhìn thấy sản phẩm của ĐVBH khác đã đăng ký
              </Checkbox>
            </Form.Item>
            <Form.Item name="hasPopupAdminConfirm" valuePropName="checked">
              <Checkbox name="hasPopupAdminConfirm" onChange={handleCheckboxChange}>
                Cho phép popup khi ĐVBH xác nhận giao dịch
              </Checkbox>
            </Form.Item>
            <Form.Item name="holdPropertyWhenUnregister" valuePropName="checked">
              <Checkbox name="holdPropertyWhenUnregister" onChange={handleCheckboxChange}>
                Cho phép ĐVBH giữ sp giao đích danh khi trả về
              </Checkbox>
            </Form.Item>
            <Form.Item name="unCountPermissionNumPropertyOfUnit" valuePropName="checked">
              <Checkbox name="unCountPermissionNumPropertyOfUnit" onChange={handleCheckboxChange}>
                Không tính số lượng quyền khi giao sản phẩm đích danh
              </Checkbox>
            </Form.Item>
            <Form.Item name="allowViewPricePublic" valuePropName="checked">
              <Checkbox name="allowViewPricePublic" onChange={handleCheckboxChange}>
                Đơn vị bán hàng thấy giá không cần đăng ký
              </Checkbox>
            </Form.Item>
            <Form.Item name="skipCountHasCustomer" valuePropName="checked">
              <Checkbox name="skipCountHasCustomer" onChange={handleCheckboxChange}>
                Không đếm thời gian trả về khi đã nhập khách hàng
              </Checkbox>
            </Form.Item>
          </Col>
          <Col span={24}>
            <Title style={{ marginTop: 32, marginBottom: 16 }} level={5}>
              Đơn vị bán hàng
            </Title>
            <TableComponent
              queryKeyArr={[]}
              columns={columns}
              dataSource={orgChartProject}
              rowKey="id"
              isPagination={false}
            />
            <Modal
              title="Thêm đơn vị ĐTHT"
              open={isModalVisible}
              onOk={handleSaveUnits}
              onCancel={() => {
                setIsModalVisible(false);
                setSearchPartnerShip('');
              }}
              footer={null}
              width={400}
            >
              <Input
                placeholder="Tìm kiếm đơn vị ĐTHT"
                style={{ marginBottom: 10 }}
                value={searchPartnerShip}
                onChange={handleSearchPartnerShip}
              />
              {currentListĐTHT ? (
                currentListĐTHT.length > 0 ? (
                  currentListĐTHT.map((unit: ListĐTHT) => (
                    <div key={unit.id}>
                      <Checkbox
                        checked={selectedUnits.includes(unit.id)}
                        onChange={e => handleCheckbox(unit.id, e.target.checked)}
                      >
                        {`${unit.partnershipCode} - ${unit.partnershipName}`}
                      </Checkbox>
                    </div>
                  ))
                ) : (
                  <div style={{ textAlign: 'center', color: 'gray' }}>Không có ĐTHT thuộc đơn vị bán hàng</div>
                )
              ) : (
                <Spin spinning={loadingPartnerShip}></Spin>
              )}
              <div style={{ textAlign: 'end' }}>
                <Button type="primary" style={{ marginTop: 10 }} onClick={handleSaveUnits}>
                  Thêm đơn vị
                </Button>
              </div>
            </Modal>
          </Col>
        </Row>
      </Form>
    </ModalComponent>
  );
};

export default FormModalSellProgram;

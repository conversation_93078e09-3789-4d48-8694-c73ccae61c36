import { Button, TableColumnsType } from 'antd';
import dayjs from 'dayjs';
import BreadCrumbComponent from '../../../components/breadCrumb';
import FilterLiquidationProposal from './components/filterSearch';
import TableComponent from '../../../components/table';
import { STATUS_LIQUIDATION } from '../../../constants/common';
import { ILiquidation } from '../../../types/liquidation';
import { useFetch } from '../../../hooks/query';
import { useMemo, useState } from 'react';
import useFilter from '../../../hooks/filter';
import { deleteLiquidationProposal, getListLiquidationProposal } from '../../../service/liquidation';
import LiquidationProposalModal from './createLiquidation';
import { LIQUIDATION_PROPOSAL_MANAGEMENT } from '../../../configs/path';
import { useNavigate } from 'react-router-dom';
import { useDeleteField } from '../../../hooks';

const formatDate = (date: string) => {
  const formattedDate = dayjs(date).format('DD/MM/YYYY');
  const formattedTime = dayjs(date).format('HH:mm');
  return (
    <>
      <div>{formattedDate}</div>
      <div className="text-time">{formattedTime}</div>
    </>
  );
};

const LiquidationProposalManagement = () => {
  const navigate = useNavigate();
  const [filterParams, setFilterParams] = useState<Record<string, unknown>>({});
  const [isModalCreateLiquidation, setModalCreateLiquidation] = useState(false);
  const [filter] = useFilter();

  const combinedFilter = useMemo(
    () => ({
      ...filterParams,
      page: filter.page || '1',
      pageSize: filter.pageSize || '10',
    }),
    [filterParams, filter.page, filter.pageSize],
  );

  const {
    data: liquidation,
    isLoading,
    isPlaceholderData,
    isFetching,
    refetch,
  } = useFetch<ILiquidation[]>({
    queryKeyArrWithFilter: ['get-list-liquidation-proposal', combinedFilter],
    defaultFilter: combinedFilter,
    api: getListLiquidationProposal,
    moreParams: combinedFilter,
  });

  const handleFilterChange = (newFilterParams: Record<string, unknown>) => {
    setFilterParams(newFilterParams);
  };

  const { mutateAsync: remove, data: res } = useDeleteField({
    apiQuery: deleteLiquidationProposal,
    keyOfDetailQuery: ['get-list-liquidation-proposal', combinedFilter],
    label: 'đơn đề nghị thanh lý',
  });
  const handleDelete = async (id: string) => {
    remove(id);
    if (res?.data?.statusCode === '0') {
      refetch();
    }
  };

  const dataLiquidation = liquidation?.data?.data?.rows || [];

  const columns: TableColumnsType<ILiquidation> = [
    {
      title: 'Mã đơn đề nghị',
      dataIndex: 'code',
      key: 'code',
      render: (_, record: ILiquidation) => (
        <a
          onClick={() => navigate(`${LIQUIDATION_PROPOSAL_MANAGEMENT}/${record.id}`)}
          style={{ cursor: 'pointer', color: '#1890ff' }}
        >
          {record.code}
        </a>
      ),
    },
    {
      title: 'Dự án',
      dataIndex: ['escrowTicket', 'project', 'name'],
      key: 'projectName',
    },
    {
      title: 'Phiếu YCDCO',
      dataIndex: ['escrowTicket', 'escrowTicketCode'],
      key: 'escrowTicketCode',
    },
    {
      title: 'Mã hợp đồng',
      dataIndex: ['escrowTicket', 'code'],
      key: 'escrowTicketCode',
    },
    {
      title: 'Mã sản phẩm',
      dataIndex: ['escrowTicket', 'propertyUnit', 'code'],
      key: 'escrowTicketProductCode',
    },
    {
      title: 'Khách hàng',
      dataIndex: ['customerTransfer', 'name'],
      key: 'customerTransferName',
    },
    { title: 'Ngày tạo', dataIndex: 'createdDate', key: 'createdDate', render: formatDate, width: 150 },
    {
      title: 'Lý do tạo',
      dataIndex: 'reasonLiquidation',
      key: 'reasonLiquidation',
    },
    {
      title: 'Lý do từ chối',
      dataIndex: 'reasonReject',
      key: 'reasonReject',
    },
    {
      title: 'Trạng thái HĐ',
      dataIndex: 'status',
      key: 'status',
      align: 'center',
      render: (status: string) => (
        <span>{STATUS_LIQUIDATION.find(item => item.value === status)?.label || status}</span>
      ),
    },
    {
      width: 100,
      render: (_, record: ILiquidation) =>
        record.status === 'init' && (
          <Button
            style={{ color: '#FF3B30' }}
            type="text"
            onClick={() => {
              handleDelete(record.id);
            }}
          >
            Xóa
          </Button>
        ),
    },
  ];

  return (
    <>
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterLiquidationProposal onFilterChange={handleFilterChange} />
        <div style={{ display: 'flex', gap: '10px' }}>
          <Button type="default">Tải về</Button>
          <Button type="primary" onClick={() => setModalCreateLiquidation(true)}>
            Tạo mới đơn đề nghị
          </Button>
        </div>
      </div>
      <TableComponent
        columns={columns}
        dataSource={dataLiquidation}
        rowKey="id"
        queryKeyArr={['get-list-liquidation-proposal', combinedFilter]}
        loading={isFetching || isPlaceholderData || isLoading}
        defaultFilter={combinedFilter}
      />
      <LiquidationProposalModal
        visible={isModalCreateLiquidation}
        onClose={() => {
          setModalCreateLiquidation(false);
        }}
      />
    </>
  );
};

export default LiquidationProposalManagement;

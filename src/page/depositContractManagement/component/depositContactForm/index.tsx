import { <PERSON><PERSON>, Col, DatePicker, Form, Input, InputNumber, Row, Select, Typography, UploadFile } from 'antd';
import React, { useEffect, useMemo, useRef, useState } from 'react';
import UploadFileDepositContract from '../UploadFileDepositContract';
import FPTLogo from '../../../../assets/images/FPT_logo.png';
import { useParams } from 'react-router-dom';
import { CloseOutlined, PlusOutlined } from '@ant-design/icons';
import { useCreateField, useFetch } from '../../../../hooks';
import { Bank, BankOption } from '../../../../types/bookingRequest';
import { getBanks } from '../../../../service/bank';
import dayjs, { Dayjs } from 'dayjs';
import { v4 as uuidv4 } from 'uuid';
import './styles.scss';
import TableComponent from '../../../../components/table';
import { Installment } from '../../../../types/depositContract';
import { formatNumber, handleKeyDownEnterNumber } from '../../../../utilities/regex';
import { ORGCHART_TYPE } from '../../../../constants/common';
import {
  createDepositContract,
  getListDepositProject,
  getListDistributionChannel,
  getListDivision,
  getListOrgchartExternal,
  getListOrgchartInternal,
  getListOrgchartPartner,
  getListSalesPolicy,
} from '../../../../service/depositContract';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import FilterAddProduct from './filterMoreProduct';

const { Title, Text } = Typography;
const { Option } = Select;
const { RangePicker } = DatePicker;
interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

const DepositContactForm: React.FC = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const orgchartType = Form.useWatch(['orgchart', 'type'], form);
  const depositAmount = Form.useWatch('depositAmount', form) || 0;
  const bankInfoOptions = Form.useWatch('bankAccount', form) || [];

  const [orgchartPartnerId, setOrgchartPartnerId] = useState('');

  useEffect(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;

    // Tính toán distributionChannel mặc định dựa trên loại đơn vị
    const defaultDistributionChannelCode = currentType === ORGCHART_TYPE.INTERNAL ? 10 : 13;

    form.setFieldsValue({
      orgchart: {
        type: currentType,
        id: undefined,
        name: undefined,
        code: undefined,
        bpID: undefined,
        taxCode: undefined,
        email: undefined,
      },
      // Reset field "Hợp tác với đơn vị bán hàng" khi thay đổi loại đơn vị
      orgchartPartnerId: undefined,
      // Set distributionChannel mặc định dựa trên loại đơn vị
      distributionChannel: defaultDistributionChannelCode,
    });

    // Reset state khi thay đổi loại đơn vị
    setOrgchartPartnerId('');
  }, [orgchartType, form]);

  const defaultBankOptions: BankOption[] = useMemo(
    () =>
      bankInfoOptions
        .filter((bank: Bank) => bank?.bankCode && bank?.bankName)
        .map((bank: Bank, index: number) => ({
          value: `${bank.bankCode}-${index}`,
          label: `${bank?.bankName || 'N/A'} - ${bank?.accountNumber || ''} - ${bank?.beneciary || ''}`,
          originalBankCode: bank.bankCode,
        })),
    [bankInfoOptions],
  );

  const handleRemoveBankAccount = (name: number) => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    const bankAccount = form.getFieldValue('bankAccount');
    const removedBank = bankAccount[name];

    // Kiểm tra xem tài khoản bị xóa có phải là tài khoản chính không
    const isMainBankRemoved = currentMainBankId === `${removedBank?.bankCode}-${name}`;

    if (isMainBankRemoved) {
      // Nếu tài khoản chính bị xóa, đặt lại mainBankId và mainBank
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: {
          code: '',
          name: '',
          accountNumber: '',
          beneciary: '',
        },
      });
    } else if (currentMainBankId) {
      // Nếu tài khoản chính không bị xóa, cập nhật lại mainBankId để phản ánh chỉ số mới
      const [bankCode, currentIndex] = currentMainBankId.split('-');
      const currentIndexNum = parseInt(currentIndex);

      // Nếu chỉ số của tài khoản chính lớn hơn chỉ số bị xóa, giảm chỉ số đi 1
      if (currentIndexNum > name) {
        const newMainBankId = `${bankCode}-${currentIndexNum - 1}`;
        form.setFieldsValue({
          mainBankId: newMainBankId,
        });
      }
    }
  };

  useEffect(() => {
    const currentMainBankId = form.getFieldValue('mainBankId');
    if (currentMainBankId) {
      const [bankCode, index] = currentMainBankId.split('-');
      const bankAccount = form.getFieldValue('bankAccount') || [];
      const selectedBank = bankAccount[parseInt(index)];

      // Chỉ clear mainBankId nếu tài khoản chính không còn tồn tại hoặc bankCode không khớp
      if (!selectedBank || selectedBank.bankCode !== bankCode) {
        form.setFieldsValue({
          mainBankId: undefined,
          mainBank: {
            code: '',
            name: '',
            accountNumber: '',
            beneciary: '',
          },
        });
      }
    }
  }, [bankInfoOptions, form]);

  const handleSelectBankInfo = (value: string) => {
    const selectedOption = defaultBankOptions.find(option => option.value === value);
    const selectedBank = bankInfoOptions.find((bank: Bank) => bank.bankCode === selectedOption?.originalBankCode);
    if (selectedBank) {
      form.setFieldsValue({
        mainBankId: value,
        mainBank: {
          code: selectedBank.bankCode || '',
          name: selectedBank.bankName || '',
          accountNumber: selectedBank.accountNumber || '',
          beneciary: selectedBank.beneciary || '',
        },
      });
    } else {
      form.setFieldsValue({
        mainBankId: undefined,
        mainBank: {
          code: selectedBank.bankCode || '',
          name: '',
          accountNumber: '',
          beneciary: '',
        },
      });
    }
  };

  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [depositDates, setDepositDates] = useState<[Dayjs | null, Dayjs | null]>([dayjs(), null]);
  const depositRangePickerRef = useRef<React.ComponentRef<typeof DatePicker.RangePicker>>(null);
  const [totalPaymentError, setTotalPaymentError] = useState<string | null>(null);
  const [installments, setInstallments] = useState<Installment[]>([]);
  const [selectedProducts, setSelectedProducts] = useState<any[]>([]);
  const [selectedPropertyUnitIds, setSelectedPropertyUnitIds] = useState<string[]>([]);
  const [removedProductInfo, setRemovedProductInfo] = useState<{ salesProgramId: string; timestamp: number } | null>(
    null,
  );
  const initialValues = { name: '', imageUrl: '' };

  // Helper function để tính ngày đến hạn
  const calculateExpiredDate = (signedDate: dayjs.Dayjs, expiredDays: number): string => {
    if (!signedDate || !expiredDays || expiredDays <= 0) return '';
    return signedDate.add(expiredDays, 'day').format('DD/MM/YYYY');
  };

  // Khởi tạo giá trị mặc định cho distributionChannel khi component mount
  useEffect(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;
    const defaultDistributionChannelCode = currentType === ORGCHART_TYPE.INTERNAL ? '10' : '13';

    // Chỉ set nếu chưa có giá trị
    const currentDistributionChannel = form.getFieldValue('distributionChannel');
    if (!currentDistributionChannel) {
      form.setFieldsValue({
        distributionChannel: defaultDistributionChannelCode,
      });
    }
  }, []); // Chỉ chạy một lần khi component mount

  const { mutateAsync: _createDepositContract } = useCreateField({
    apiQuery: createDepositContract,
    keyOfDetailQuery: [''],
    label: 'thư mục',
    isMessageError: false,
  });

  // Tính toán kênh phân phối mặc định dựa trên loại đơn vị
  const defaultDistributionChannel = useMemo(() => {
    const currentType = orgchartType || ORGCHART_TYPE.EXTERNAL;
    return currentType === ORGCHART_TYPE.INTERNAL
      ? { code: '10', name: 'Kênh bán nội bộ' }
      : { code: '13', name: 'Kênh bán bán lẻ' }; // Sửa tên cho đúng
  }, [orgchartType]);

  const { data: dataBanks, isLoading: isLoadingBanks } = useFetch<Bank[]>({
    queryKeyArrWithFilter: ['get-list-banks'],
    api: getBanks,
  });
  const banks = dataBanks?.data?.data || [];

  useEffect(() => {
    if (!depositAmount && depositAmount <= 0) {
      setInstallments([]);
      setTotalPaymentError(null);
    } else {
      const updatedInstallments = installments.map((inst: any) => ({
        ...inst,
        convertedAmount: calculateConvertedAmount(inst.value || 0, inst.type, depositAmount),
      }));
      setInstallments(updatedInstallments);
      validateTotalPayment(updatedInstallments);
    }
  }, [depositAmount]);

  const columnsInstallments = [
    {
      title: 'Tên đợt',
      dataIndex: 'name',
      key: 'name',
      width: 200,
      render: (_: string, record: any) => (
        <>
          <Input
            onChange={e => updateInstallment(record?.id, 'name', e.target.value)}
            placeholder="Nhập tên đợt thanh toán"
            maxLength={150}
          />
        </>
      ),
    },
    {
      title: 'Giá trị thanh toán',
      dataIndex: 'value',
      key: 'value',
      width: 250,
      render: (_: string, record: any) => (
        <>
          <Select defaultValue={'TT số tiền ký quỹ'} style={{ width: '100%', marginBottom: 16 }}>
            <Option value="TT số tiền ký quỹ">TT số tiền ký quỹ</Option>
          </Select>
          <InputNumber
            maxLength={record.type === 'percent' ? 5 : 15}
            style={{ width: '100%' }}
            placeholder="Nhập giá trị thanh toán"
            min={1} // Phải lớn hơn 0
            max={record.type === 'percent' ? 100 : undefined}
            step={1} // Chỉ cho phép số nguyên
            precision={0} // Không cho phép số thập phân
            formatter={(value: number | string | undefined) =>
              record.type === 'currency' && value !== undefined
                ? `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ',')
                : `${value}`
            }
            parser={(value: string | undefined) => {
              if (record.type === 'currency' && value) {
                const parsed = parseInt(value.replace(/,/g, ''), 10);
                return isNaN(parsed) ? 0 : parsed;
              }
              return value || '';
            }}
            onKeyDown={handleKeyDownEnterNumber}
            onChange={value => {
              // Validate số nguyên dương
              const numValue = Number(value);
              if (value && numValue > 0 && Number.isInteger(numValue)) {
                updateInstallment(record?.id, 'value', numValue);
              } else if (value === null || value === undefined || numValue === 0) {
                updateInstallment(record?.id, 'value', null);
              }
            }}
            onBlur={e => {
              const currentValue = Number(e.target.value);
              if (!currentValue || currentValue <= 0 || !Number.isInteger(currentValue)) {
              }
            }}
            addonAfter={
              <Form.Item noStyle>
                <Select
                  style={{ width: 80 }}
                  defaultValue={'currency'}
                  onChange={value => updateInstallment(record.id, 'type', value)}
                >
                  <Option value="currency">VND</Option>
                  <Option value="percent">%</Option>
                </Select>
              </Form.Item>
            }
          />
        </>
      ),
    },
    {
      title: 'Thời hạn thanh toán',
      dataIndex: 'expiredDays',
      key: 'expiredDays',
      width: 200,
      render: (_: string, record: any, index: number) => (
        <>
          <Select defaultValue={'Từ sau ngày ký quỹ'} style={{ width: '100%', marginBottom: 16 }}>
            <Option value="Từ sau ngày ký quỹ">Từ sau ngày ký kết</Option>
          </Select>
          <Form.Item
            name={['installments', index, 'expiredDays']}
            style={{ margin: 0 }}
            rules={[
              {
                required: true,
                message: 'Vui lòng nhập số ngày thanh toán',
              },
              {
                validator: (_, value) => {
                  if (!value || value <= 0) {
                    return Promise.reject('Vui lòng nhập số ngày thanh toán');
                  }

                  // Kiểm tra thứ tự với đợt trước
                  if (index > 0) {
                    const prevInstallment = installments[index - 1];
                    if (prevInstallment && prevInstallment.expiredDays && value <= prevInstallment.expiredDays) {
                      return Promise.reject('Vui lòng nhập số ngày nhiều hơn số ngày TT của đợt liền trước');
                    }
                  }

                  return Promise.resolve();
                },
              },
            ]}
          >
            <InputNumber
              style={{ width: '100%' }}
              onChange={value => updateInstallment(record?.id, 'expiredDays', value, index)}
              placeholder="Nhập số ngày thanh toán"
              suffix="Ngày"
              min={1}
              precision={0}
            />
          </Form.Item>
        </>
      ),
    },
    {
      title: 'Ngày đến hạn dự kiến',
      dataIndex: 'expiredDate',
      key: 'expiredDate',
      width: 180,
      render: (_: string, record: any) => (
        <div style={{ textAlign: 'center', padding: '8px 0' }}>
          {record.expiredDate ? (
            <span style={{ color: '#1890ff', fontWeight: 500 }}>{record.expiredDate}</span>
          ) : (
            <span style={{ color: '#999', fontStyle: 'italic' }}>Chưa tính được</span>
          )}
        </div>
      ),
    },
    {
      title: 'Mô tả',
      dataIndex: 'description',
      key: 'description',
      width: 230,
      render: (_: string, record: any) => (
        <>
          <Input
            onChange={e => updateInstallment(record?.id, 'description', e.target.value)}
            placeholder="Nhập mô tả tiến độ thanh toán"
            maxLength={255}
          />
        </>
      ),
    },
    {
      title: 'Quy đổi tỉ lệ/ số tiền tương ứng',
      dataIndex: 'convertedAmount',
      key: 'convertedAmount',
      width: 280,
      render: (_: string, record: any) => (
        <InputNumber
          maxLength={record.type === 'percent' ? 5 : 15}
          style={{ width: '100%' }}
          placeholder="Số tiền TT"
          min={0}
          max={record.type === 'percent' ? 100 : undefined}
          step={record.type === 'percent' ? 0.01 : 1}
          precision={2}
          formatter={formatNumber}
          parser={(value: string | undefined) =>
            record.type === 'currency' && value ? parseFloat(value.replace(/,/g, '')) : value || ''
          }
          onKeyDown={handleKeyDownEnterNumber}
          addonAfter={
            <Form.Item noStyle>
              <Select
                style={{ width: 80 }}
                defaultValue={'currency'}
                value={record.type === 'percent' ? 'currency' : 'percent'}
              >
                <Option value="currency">VND</Option>
                <Option value="percent">%</Option>
              </Select>
            </Form.Item>
          }
          disabled
          value={record.convertedAmount || 0}
        />
      ),
    },
    {
      title: '',
      key: 'action',
      width: 80,
      render: (_: string, record: any) =>
        installments.length > 1 ? (
          <CloseOutlined
            style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
            onClick={() => deleteInstallment(record.id)}
          />
        ) : null,
    },
  ];

  // Function để xóa sản phẩm
  const removeProduct = (productId: string) => {
    // Tìm sản phẩm cần xóa để lấy salesProgramId
    const productToRemove = selectedProducts.find(p => p.id === productId);

    // Xóa sản phẩm khỏi danh sách
    setSelectedProducts(prev => prev.filter(p => p.id !== productId));

    // Xóa salesProgramId khỏi propertyUnitIds nếu không còn sản phẩm nào sử dụng
    if (productToRemove?.salesProgramId) {
      setSelectedPropertyUnitIds(prev => {
        // Kiểm tra xem còn sản phẩm nào khác sử dụng salesProgramId này không
        const remainingProducts = selectedProducts.filter(p => p.id !== productId);
        const isStillUsed = remainingProducts.some(p => p.salesProgramId === productToRemove.salesProgramId);

        if (!isStillUsed) {
          // Nếu không còn sản phẩm nào sử dụng, xóa khỏi propertyUnitIds
          const updatedIds = prev.filter(id => id !== productToRemove.salesProgramId);

          return updatedIds;
        }

        return prev;
      });
    }

    // Thông báo cho FilterAddProduct về salesProgramId đã bị xóa
    if (productToRemove?.salesProgramId) {
      setRemovedProductInfo({
        salesProgramId: productToRemove.salesProgramId,
        timestamp: Date.now(),
      });
    }
  };

  const columnsEscrowProduct = [
    {
      title: 'Mã sản phẩm',
      dataIndex: 'code',
      key: 'code',
      width: 150,
      render: (code: string) => <div>{code}</div>,
    },
    {
      title: 'Chương trình bán hàng',
      dataIndex: 'name',
      key: 'name',
      width: 360,
      render: (name: string) => <div>{name}</div>,
    },
    {
      title: '',
      key: 'action',
      width: 80,
      align: 'center' as const,
      render: (_: string, record: any) => (
        <Button type="link" danger onClick={() => removeProduct(record.id)} size="small">
          Xóa
        </Button>
      ),
    },
  ];

  const customComponents = {
    header: {
      row: (props: React.HTMLAttributes<HTMLTableRowElement>) => (
        <>
          <tr {...props} />
          <tr>
            <td colSpan={3} className="ant-table-cell-total-product">
              <Typography.Text className="text-total-product">
                Tổng số sản phẩm ký quỹ: {selectedProducts.length}
              </Typography.Text>
            </td>
          </tr>
        </>
      ),
    },
  };

  const addInstallment = () => {
    if (!depositAmount || depositAmount <= 0) {
      setTotalPaymentError('Vui lòng thiết lập số tiền ký quỹ');
      return;
    }
    const newInstallment = {
      id: uuidv4(),
      name: '',
      type: 'currency',
      value: 0,
      convertedAmount: 0,
      expiredDateType: 'numberDay',
      exactDays: '',
      expiredDays: undefined,
      description: '',
    };
    setInstallments([...installments, newInstallment]);
  };

  // Hàm tính convertedAmount
  const calculateConvertedAmount = (value: number, type: 'percent' | 'currency', depositAmount: number) => {
    if (!value || !depositAmount || value <= 0 || depositAmount <= 0) return 0;

    let result: number;
    if (type === 'percent') {
      result = (value / 100) * depositAmount; // % → VNĐ
    } else {
      result = (value / depositAmount) * 100; // VNĐ → %
    }

    return Number(Math.round(result * 100) / 100);
  };

  // Hàm tính tổng số tiền thanh toán (chỉ tính các giá trị VND)
  const calculateTotalPayment = (installments: Installment[], depositAmount: number) => {
    return installments.reduce((total, inst) => {
      if (inst.type === 'currency' && inst.value) {
        return total + inst.value;
      } else if (inst.type === 'percent' && inst.value) {
        return total + (inst.value / 100) * depositAmount;
      }
      return total;
    }, 0);
  };

  // Hàm kiểm tra tổng số tiền thanh toán
  const validateTotalPayment = (installments: Installment[]) => {
    const total = calculateTotalPayment(installments, depositAmount);
    if (total > depositAmount) {
      setTotalPaymentError('Vui lòng nhập tổng giá trị thanh toán nhỏ hơn số tiền ký quỹ');
      return false;
    } else {
      setTotalPaymentError(null);
      return true;
    }
  };

  const updateInstallment = (id: string, field: string, value: any, index?: number) => {
    // Tìm index thực tế của installment trong array
    const actualIndex = installments.findIndex(inst => inst.id === id);
    const currentIndex = index !== undefined ? index : actualIndex;

    // Kiểm tra validation cho các trường khác nhau
    if (field === 'value') {
      // Validation cho giá trị thanh toán
      if (value !== null && value !== undefined) {
        const numValue = Number(value);
        if (numValue <= 0 || !Number.isInteger(numValue)) {
          console.log('Invalid payment value - must be positive integer:', numValue);
          // Có thể set error message ở đây nếu cần
          return;
        }
      }
    }

    // Kiểm tra nếu trường là expiredDays và giá trị rỗng hoặc không hợp lệ
    if (field === 'expiredDays') {
      if (value === null || value === undefined || value === '' || value <= 0) {
        form.setFields([
          {
            name: ['installments', currentIndex, 'expiredDays'],
            errors: ['Vui lòng nhập số ngày thanh toán'],
          },
        ]);
        return;
      }

      // Kiểm tra nếu expiredDays nhỏ hơn hoặc bằng đợt trước
      if (currentIndex > 0) {
        const prevInstallment = installments[currentIndex - 1];

        if (prevInstallment && prevInstallment.expiredDays && value <= prevInstallment.expiredDays) {
          form.setFields([
            {
              name: ['installments', currentIndex, 'expiredDays'],
              errors: ['Vui lòng nhập số ngày nhiều hơn số ngày TT của đợt liền trước'],
            },
          ]);
          return;
        }
      }

      // Clear errors nếu validation pass
      form.setFields([
        {
          name: ['installments', currentIndex, 'expiredDays'],
          errors: [],
        },
      ]);
    }

    const newInstallments = installments.map((inst: any) => {
      if (inst.id === id) {
        const updatedInst = { ...inst, [field]: value };

        // Tính toán ngày đến hạn nếu có depositSignedDate và expiredDays
        if (field === 'expiredDays' && value && form.getFieldValue('depositSignedDate')) {
          const signedDate = dayjs(form.getFieldValue('depositSignedDate'));
          updatedInst.expiredDate = calculateExpiredDate(signedDate, value);
        }

        // Cập nhật convertedAmount nếu thay đổi value hoặc type
        if (field === 'value' || field === 'type') {
          updatedInst.convertedAmount = calculateConvertedAmount(
            field === 'value' ? value || 0 : inst.value || 0,
            field === 'type' ? value : inst.type,
            depositAmount,
          );
        }
        return updatedInst;
      }
      return inst;
    });

    setInstallments(newInstallments);
    if (field === 'value' || field === 'type') {
      validateTotalPayment(newInstallments);
    }
  };

  const deleteInstallment = (id: string) => {
    const newInstallments = installments.filter((inst: any) => inst.id !== id);
    setInstallments(newInstallments);
    validateTotalPayment(newInstallments);
  };

  // Xử lý khi submit filter sản phẩm
  const handleAddProducts = (filterValues: any) => {
    // Tạo sản phẩm mẫu dựa trên filter criteria
    const newProducts: any[] = [];
    const newPropertyUnitIds: string[] = [];

    // Nếu có salesProgram được chọn
    if (filterValues.salesProgram && filterValues.salesProgram.length > 0) {
      filterValues.salesProgram.forEach((saleProgram: any) => {
        const productCode = `${saleProgram?.code || 'NO_CODE'}`;
        const productName = `${saleProgram?.name || 'NO_NAME'}`;

        // Kiểm tra xem sản phẩm đã tồn tại chưa
        const existingProduct = selectedProducts.find(p => p.code === productCode);
        if (!existingProduct) {
          const newProduct = {
            id: uuidv4(),
            code: productCode,
            name: productName,
            salesProgramId: saleProgram?.id,
            salesProgramData: saleProgram, // Lưu toàn bộ thông tin sale program
            blocks: filterValues.blocks || [],
            floors: filterValues.floors || [],
            rooms: filterValues.rooms || [],
          };

          newProducts.push(newProduct);

          // Thêm salesProgramId vào propertyUnitIds
          if (saleProgram?.id) {
            newPropertyUnitIds.push(saleProgram.id);
          }
        }
      });
    } else if (filterValues.salesProgramIds && filterValues.salesProgramIds.length > 0) {
      newPropertyUnitIds.push(...filterValues.salesProgramIds);
    }

    // Cập nhật danh sách sản phẩm đã chọn
    setSelectedProducts(prev => [...prev, ...newProducts]);

    // Cập nhật propertyUnitIds (loại bỏ duplicate)
    setSelectedPropertyUnitIds(prev => {
      const allIds = [...prev, ...newPropertyUnitIds];
      const uniqueIds = [...new Set(allIds)]; // Loại bỏ duplicate
      return uniqueIds;
    });

    // Clear thông báo về removed product sau khi add thành công
    if (newProducts.length > 0 && removedProductInfo) {
      setRemovedProductInfo(null);
    }
  };

  const onFinish = async (values: any) => {
    console.log(values);

    // Validate installments trước khi submit
    const invalidInstallments = installments.filter((inst, index) => {
      // Kiểm tra expiredDays
      if (!inst.expiredDays || inst.expiredDays <= 0) {
        return true;
      }

      // Kiểm tra giá trị thanh toán
      if (!inst.value || inst.value <= 0 || !Number.isInteger(Number(inst.value))) {
        return true;
      }

      // Kiểm tra thứ tự expiredDays
      if (index > 0) {
        const prevInstallment = installments[index - 1];
        if (prevInstallment.expiredDays && inst.expiredDays <= prevInstallment.expiredDays) {
          return true;
        }
      }

      return false;
    });

    if (invalidInstallments.length > 0) {
      console.error('Invalid installments found:', invalidInstallments);
      // Set errors cho các installments không hợp lệ
      invalidInstallments.forEach(inst => {
        const installmentIndex = installments.findIndex(i => i.id === inst.id);

        if (!inst.expiredDays || inst.expiredDays <= 0) {
          form.setFields([
            {
              name: ['installments', installmentIndex, 'expiredDays'],
              errors: ['Vui lòng nhập số ngày thanh toán'],
            },
          ]);
        }

        if (!inst.value || inst.value <= 0 || !Number.isInteger(Number(inst.value))) {
          console.error('Invalid payment value for installment:', inst);
          // Có thể thêm form.setFields cho value nếu cần hiển thị error trên UI
        }
      });
      return; // Không submit nếu có lỗi
    }

    // Xác định distributionChannel: sử dụng giá trị đã chọn hoặc giá trị mặc định
    const finalDistributionChannel = values.distributionChannel || defaultDistributionChannel.code;

    const payload = {
      projectId: id,
      orgchart: {
        id: orgchartPartnerId || '',
        code: values.orgchart?.code || '',
        name: values.orgchart?.name || '',
        taxCode: values.orgchart?.taxCode,
        bpID: values.orgchart?.bpID || '',
        email: values.orgchart?.email,
        type: values.orgchart?.type,
      },
      orgchartPartnerId: orgchartPartnerId,
      bank: {
        code: values.mainBank?.code,
        name: values.mainBank?.name,
        accountNumber: values.mainBank?.accountNumber,
        beneficiary: values.mainBank?.beneciary,
      },
      depositForm: values.depositForm,
      depositTime: values.depositTime,
      depositAmount: values.depositAmount,
      salePolicyId: values.salePolicyId || '',
      startDate: depositDates[0],
      expiredDate: depositDates[1],
      type: values.type,
      POnumber: values.POnumber,
      distributionChannel: finalDistributionChannel, // Sử dụng giá trị đã xử lý
      division: values.division ? Number(values.division) : undefined,
      propertyUnitIds: selectedPropertyUnitIds || [], // Sử dụng salesProgramIds từ products đã chọn
      installments: installments.map(inst => ({
        ...inst,
      })),
      files: fileList.map(file => ({
        name: file.name,
        url: file.url || '',
        absoluteUrl: file.absoluteUrl || '',
      })),
    };
    await _createDepositContract(payload);
    // Gửi payload tới API
  };

  const handleSelectOrgchartInternal = (values: any) => {
    setOrgchartPartnerId(values?.id);
    form.setFieldsValue({
      orgchart: {
        id: values?.id,
        name: values?.nameVN,
        code: values?.code,
        taxCode: values?.taxCode,
        email: values?.email,
      },
      // Reset field "Hợp tác với đơn vị bán hàng" khi chọn tên đơn vị mới
      orgchartPartnerId: undefined,
    });
  };

  const handleSelectOrgchartExternal = (values: any) => {
    setOrgchartPartnerId(values?.id);
    form.setFieldsValue({
      orgchart: {
        id: values?.id,
        code: values?.partnershipCode,
        name: values?.partnershipName,
        taxCode: values?.taxCode,
        bpID: values?.businessPartnerCode,
        email: values?.email,
      },
      // Reset field "Hợp tác với đơn vị bán hàng" khi chọn tên đơn vị mới
      orgchartPartnerId: undefined,
    });
  };

  const handleSelectDistributionChannel = (values: any) => {
    form.setFieldsValue({ distributionChannel: values?.code });
  };

  const handleSelectDivision = (values: any) => {
    form.setFieldsValue({ division: values?.code });
  };

  const handleSelectDepositProject = (values: any) => {
    form.setFieldsValue({ projectId: values?.id });
  };

  const handleSelectOrgchartPartnerInternal = (values: any) => {
    form.setFieldsValue({ orgchartPartnerId: values?.id });
  };

  const handleSelectOrgchartPartnerExternal = (values: any) => {
    form.setFieldsValue({ orgchartPartnerId: values?.id });
  };

  return (
    <div className="booking-ticket-detail">
      <div className="project-card">
        <div className="project-info">
          <Title level={5}>{`Hợp đồng ký quỹ ${initialValues?.name || ''}`}</Title>
          <Text type="secondary">
            Dự án: <span className="text-type">{initialValues?.name || ''}</span>
          </Text>
        </div>

        <div className="project-image">
          <img
            src={initialValues?.imageUrl ? `${import.meta.env.VITE_S3_IMAGE_URL}/${initialValues?.imageUrl}` : FPTLogo}
            alt="Project"
          />
        </div>
      </div>
      <Form form={form} layout="vertical" onFinish={onFinish} style={{ marginTop: 32 }}>
        <Row gutter={32}>
          <Col span={14}>
            {/* Thông tin khách hàng */}
            <Title level={5}>Thông tin khách hàng</Title>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Loại đơn vị" name={['orgchart', 'type']}>
                  <Select placeholder="Chọn loại đơn vị" defaultValue={ORGCHART_TYPE.EXTERNAL}>
                    <Option value={ORGCHART_TYPE.EXTERNAL}>Đơn vị ĐTHT</Option>
                    <Option value={ORGCHART_TYPE.INTERNAL}>Đơn vị nội bộ</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col span={12}>
                {orgchartType === ORGCHART_TYPE.INTERNAL ? (
                  <Form.Item label="Tên đơn vị" name={['orgchart', 'name']} key="internal">
                    <SingleSelectLazy
                      key="internal-select"
                      apiQuery={getListOrgchartInternal}
                      queryKey={['get-orgchart-internal']}
                      keysLabel={['code', 'nameVN']}
                      placeholder="Chọn tên đơn vị"
                      handleSelect={handleSelectOrgchartInternal}
                      enabled={orgchartType === ORGCHART_TYPE.INTERNAL}
                    />
                  </Form.Item>
                ) : (
                  <Form.Item label="Tên đơn vị" name={['orgchart', 'name']} key="external">
                    <SingleSelectLazy
                      key="external-select"
                      apiQuery={getListOrgchartExternal}
                      queryKey={['get-orgchart-external']}
                      keysLabel={['partnershipCode', 'partnershipName']}
                      placeholder="Chọn tên đơn vị"
                      handleSelect={handleSelectOrgchartExternal}
                      enabled={orgchartType === ORGCHART_TYPE.EXTERNAL}
                    />
                  </Form.Item>
                )}
              </Col>
              <Col span={12}>
                <Form.Item label="Mã đơn vị" name={['orgchart', 'code']}>
                  <Input placeholder="Hiển thị mã đơn vị" maxLength={15} disabled />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label="Mã Business Partner" name={['orgchart', 'bpID']}>
                  <Input placeholder="Hiển thị mã BP" maxLength={15} disabled />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item label="Mã số thuế" name={['orgchart', 'taxCode']}>
                  <Input placeholder="Hiển thị mã số thuế" maxLength={15} disabled />
                </Form.Item>
              </Col>

              <Col span={12}>
                <Form.Item
                  label="Địa chỉ email"
                  name={['orgchart', 'email']}
                  rules={[{ type: 'email', message: 'Địa chỉ email sai định dạng' }]}
                >
                  <Input placeholder="Nhập địa chỉ email" maxLength={254} />
                </Form.Item>
              </Col>

              <Col span={24}>
                {orgchartType === ORGCHART_TYPE.INTERNAL ? (
                  <Form.Item label="Hợp tác với đơn vị bán hàng" name="orgchartPartnerId" key="partner-internal">
                    <SingleSelectLazy
                      key="partner-internal-select"
                      apiQuery={getListOrgchartPartner}
                      queryKey={['get-orgchart-partner-internal']}
                      keysLabel={['code', 'nameVN']}
                      placeholder="Chọn tên đơn vị bán hàng"
                      handleSelect={handleSelectOrgchartPartnerInternal}
                      moreParams={{ nids: orgchartPartnerId }}
                      enabled={!!orgchartPartnerId}
                    />
                  </Form.Item>
                ) : (
                  <Form.Item label="Hợp tác với đơn vị bán hàng" name="orgchartPartnerId" key="partner-external">
                    <SingleSelectLazy
                      key="partner-external-select"
                      apiQuery={getListOrgchartPartner}
                      queryKey={['get-orgchart-partner-external']}
                      keysLabel={['code', 'nameVN']}
                      placeholder="Chọn tên đơn vị bán hàng"
                      handleSelect={handleSelectOrgchartPartnerExternal}
                      moreParams={{ ids: orgchartPartnerId }}
                      enabled={!!orgchartPartnerId}
                    />
                  </Form.Item>
                )}
              </Col>
            </Row>

            <Title level={5}>Thông tin thanh toán</Title>
            <Form.Item label="Tài khoản giao dịch">
              <div
                style={{
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                  padding: 20,
                  margin: 0,
                  marginBottom: 16,
                }}
              >
                <Form.List name="bankAccount">
                  {(fields, { add, remove }) => (
                    <>
                      {fields.map(({ key, name, ...restField }) => (
                        <Row align="middle" gutter={[8, 8]} key={key}>
                          <Col span={10}>
                            <Form.Item
                              {...restField}
                              name={[name, 'bankName']}
                              label="Ngân hàng"
                              rules={[{ required: false }]}
                            >
                              <Select
                                placeholder="Chọn ngân hàng"
                                allowClear
                                filterOption={(input, option) =>
                                  typeof option?.label === 'string'
                                    ? option.label.toLowerCase().includes(input.toLowerCase())
                                    : false
                                }
                                showSearch
                                loading={isLoadingBanks}
                                options={banks.map(item => ({
                                  value: item?.bankCode,
                                  label: item?.bankName,
                                }))}
                                onChange={(value, option) => {
                                  form.setFieldsValue({
                                    bankAccount: {
                                      [name]: {
                                        bankCode: value,
                                        bankName: Array.isArray(option) ? '' : option?.label || '',
                                        accountNumber: form.getFieldValue(['bankAccount', name, 'accountNumber']) || '',
                                        beneciary: form.getFieldValue(['bankAccount', name, 'beneciary']) || '',
                                      },
                                    },
                                  });
                                }}
                              />
                            </Form.Item>
                          </Col>
                          <Col span={6}>
                            <Form.Item
                              {...restField}
                              name={[name, 'accountNumber']}
                              label="Số tài khoản"
                              rules={[
                                {
                                  message: 'Vui lòng nhập số tài khoản',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập số tài khoản');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input placeholder="Nhập số tài khoản" maxLength={20} />
                            </Form.Item>
                          </Col>
                          <Col span={7}>
                            <Form.Item
                              {...restField}
                              name={[name, 'beneciary']}
                              label="Tên người thụ hưởng"
                              rules={[
                                {
                                  message: 'Vui lòng nhập tên người thụ hưởng',
                                  validator: (_, value) => {
                                    const bankCode = form.getFieldValue(['bankAccount', name, 'bankCode']);
                                    if (bankCode && !value?.trim()) {
                                      return Promise.reject('Vui lòng nhập tên người thụ hưởng');
                                    }
                                    return Promise.resolve();
                                  },
                                },
                              ]}
                            >
                              <Input placeholder="Nhập tên người thụ hưởng" maxLength={255} />
                            </Form.Item>
                          </Col>
                          <Col span={1}>
                            <CloseOutlined
                              style={{ marginTop: 15, textAlign: 'center', cursor: 'pointer' }}
                              onClick={() => {
                                remove(name);
                                handleRemoveBankAccount(name);
                              }}
                            />
                          </Col>
                        </Row>
                      ))}

                      {fields.length < 10 ? (
                        <Col span={23}>
                          <Button
                            type="dashed"
                            onClick={() => {
                              add({ bankCode: undefined, bankName: undefined, accountNumber: '', beneciary: '' });
                            }}
                            style={{ padding: 0 }}
                            block
                            icon={<PlusOutlined />}
                          >
                            Thêm tài khoản giao dịch
                          </Button>
                        </Col>
                      ) : null}
                    </>
                  )}
                </Form.List>
              </div>
            </Form.Item>
            <p style={{ marginBottom: 8 }}>
              Tài khoản giao dịch chính (default) <span style={{ color: 'red' }}>*</span>
            </p>
            <div
              style={{
                backgroundColor: 'rgba(0, 0, 0, 0.02)',
                border: '1px solid var(--colorBorder, rgba(0, 0, 0, 0.15))',
                padding: 20,
                margin: 0,
                marginBottom: 16,
              }}
            >
              <Form.Item
                name="mainBankId"
                rules={[{ required: true, message: 'Vui lòng chọn tài khoản giao dịch chính' }]}
              >
                <Select
                  placeholder="Chọn tài khoản giao dịch chính"
                  options={defaultBankOptions}
                  onChange={handleSelectBankInfo}
                  showSearch
                  optionFilterProp="label"
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                />
              </Form.Item>
              {/* Trường ẩn để lưu trữ đối tượng MainBank */}
              <Form.Item name="mainBank" hidden>
                <Input type="hidden" />
              </Form.Item>
            </div>

            <Title level={5}>Thông tin ký quỹ</Title>

            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Mã hợp đồng" name={['depositContract', 'code']}>
                  <Input placeholder="Mã hợp đồng" maxLength={15} disabled />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Ngày ký kết"
                  name="depositSignedDate"
                  rules={[{ required: true, message: 'Vui lòng chọn ngày ký kết' }]}
                  initialValue={dayjs()} // Giá trị mặc định là ngày hiện tại
                >
                  <DatePicker
                    style={{ width: '100%' }}
                    placeholder="Chọn ngày ký kết"
                    format="DD/MM/YYYY"
                    defaultValue={dayjs()} // Giá trị mặc định là ngày hiện tại
                    onChange={date => {
                      // Cập nhật lại expiredDate cho tất cả installments khi depositSignedDate thay đổi
                      const newInstallments = installments.map((inst: any) => {
                        if (inst.expiredDays && date) {
                          return {
                            ...inst,
                            expiredDate: calculateExpiredDate(date, inst.expiredDays),
                          };
                        }
                        return inst;
                      });
                      setInstallments(newInstallments);
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item label="Loại hợp đồng" name={'type'} initialValue={'ZH05'}>
                  <Select placeholder="Chọn hình thức ký quỹ">
                    <Option value="ZH05">ZH05 - SO phí môi giới BĐS</Option>
                    <Option value="ZL03">ZL03 - Leasing PMG</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Ngày hiệu lực hợp đồng" name="expiredDate">
                  <RangePicker
                    ref={depositRangePickerRef}
                    value={depositDates}
                    defaultValue={[dayjs(), null]} // Giá trị mặc định: ngày hiện tại cho ngày bắt đầu
                    onChange={dates => {
                      setDepositDates(dates as [Dayjs | null, Dayjs | null]);
                    }}
                    allowClear
                    placeholder={['Ngày bắt đầu', 'Ngày kết thúc']}
                    format="DD/MM/YYYY"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Mã hợp đồng bản cứng" name="POnumber">
                  <Input placeholder="Mã hợp đồng bản cứng" maxLength={35} />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Kênh phân phối" name="distributionChannel">
                  <SingleSelectLazy
                    apiQuery={getListDistributionChannel}
                    queryKey={['get-distribution-channel']}
                    keysLabel={['code', 'name']}
                    placeholder="Chọn kênh phân phối"
                    handleSelect={handleSelectDistributionChannel}
                    defaultValues={
                      defaultDistributionChannel
                        ? {
                            label: `${defaultDistributionChannel?.code} - ${defaultDistributionChannel?.name}`,
                            value: defaultDistributionChannel?.code,
                          }
                        : undefined
                    }
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Ngành hàng" name="division">
                  <SingleSelectLazy
                    apiQuery={getListDivision}
                    queryKey={['get-division']}
                    keysLabel={['code', 'name']}
                    handleSelect={handleSelectDivision}
                    placeholder="Chọn ngành hàng"
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Hình thức ký quỹ" name="depositForm">
                  <Select placeholder="Chọn hình thức ký quỹ">
                    <Option value="NO_DEPOSIT"> Không ký quỹ</Option>
                    <Option value="GOODWILL_DEPOSIT">Ký quỹ thiện chí </Option>
                    <Option value="COMMITTED_DEPOSIT">Ký quỹ cam kết</Option>
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Dự án ký quỹ" name="projectId">
                  <SingleSelectLazy
                    apiQuery={getListDepositProject}
                    queryKey={['get-deposit-project']}
                    keysLabel={'name'}
                    placeholder="Chọn dự án ký quỹ"
                    handleSelect={handleSelectDepositProject}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label="Thời gian ký quỹ (tháng)"
                  name="depositTime"
                  rules={[{ required: true, message: 'Vui lòng nhập thời gian ký quỹ' }]}
                >
                  <InputNumber
                    placeholder="Nhập thời gian ký quỹ"
                    style={{ width: '100%' }}
                    min={1}
                    max={9999}
                    precision={0}
                    parser={value => {
                      if (!value) return '';
                      const numericValue = value.replace(/\D/g, '');
                      if (numericValue.length > 4) {
                        return parseInt(numericValue.slice(0, 4)) || '';
                      }
                      return parseInt(numericValue) || '';
                    }}
                    formatter={formatNumber}
                    onKeyDown={e => {
                      // Chặn ký tự không phải số
                      const allowedKeys = ['Backspace', 'Delete', 'Tab', 'ArrowLeft', 'ArrowRight', 'Home', 'End'];
                      if (!/[0-9]/.test(e.key) && !allowedKeys.includes(e.key)) {
                        e.preventDefault();
                      }

                      const currentValue = e.currentTarget.value;
                      if (currentValue && currentValue.length >= 4 && /[0-9]/.test(e.key)) {
                        e.preventDefault();
                      }
                    }}
                    onChange={value => {
                      if (value && (value < 1 || value > 9999)) {
                        return;
                      }
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Số tiền ký quỹ" name="depositAmount">
                  <InputNumber
                    placeholder="Nhập số tiền ký quỹ"
                    maxLength={15}
                    style={{ width: '100%' }}
                    formatter={formatNumber}
                  />
                </Form.Item>
              </Col>
            </Row>

            {/* Sản phẩm ký quỹ */}
            <Title level={5}>Sản phẩm ký quỹ</Title>

            <Row gutter={16}>
              <Col span={24}>
                <p>
                  <FilterAddProduct
                    handleSubmit={handleAddProducts}
                    initialSearchValues={undefined} // Có thể truyền giá trị khởi tạo nếu cần
                    removedProductInfo={removedProductInfo} // Thông báo về product đã bị xóa
                  />
                </p>
                <TableComponent
                  queryKeyArr={['get-']}
                  columns={columnsEscrowProduct}
                  dataSource={selectedProducts}
                  pagination={false}
                  className="unit-conversion-table"
                  components={customComponents}
                  rowKey="id"
                  isPagination={false}
                  style={{ marginBottom: 16 }}
                />
              </Col>
            </Row>
          </Col>
          <Col span={10}>
            {/* Thông tin khác */}
            <Title level={5}>Thông tin phí mô giới</Title>
            <Form.Item label="Chính sách phí môi giới" name="note">
              <SingleSelectLazy
                apiQuery={getListSalesPolicy}
                queryKey={['get-sale-policy']}
                keysLabel={'name'}
                placeholder="Chọn chính sách phí môi giới"
                moreParams={{ isActive: true, projectId: '' }}
              />
            </Form.Item>

            <Title level={5}>Tài liệu đính kèm</Title>
            <Form.Item name="files">
              <UploadFileDepositContract
                fileList={fileList}
                setFileList={setFileList}
                uploadPath="property/primarty-transaction"
                size={25}
                isDetail
              />
            </Form.Item>
          </Col>
        </Row>
        {/* Kế hoạch thanh toán ký quỹ */}
        <Title level={5}>Kế hoạch thanh toán ký quỹ</Title>
        <Button type="dashed" onClick={addInstallment} icon={<PlusOutlined />} style={{ marginBottom: 16 }}>
          Thêm đợt TT
        </Button>
        {totalPaymentError && <div style={{ color: 'red', marginBottom: 16 }}>{totalPaymentError}</div>}

        <Row gutter={16}>
          <Col span={24}>
            <TableComponent
              columns={columnsInstallments}
              queryKeyArr={['']}
              rowKey="id"
              dataSource={installments}
              isPagination={false}
              className="table-installments"
            />
          </Col>
        </Row>
        <div className="create-footer">
          <div className="button-create">
            <Button htmlType="button" style={{ marginRight: 12 }}>
              Hủy
            </Button>

            <Button type="primary" htmlType="submit">
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </div>
  );
};

export default DepositContactForm;

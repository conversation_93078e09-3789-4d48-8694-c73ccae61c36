import {
  Button,
  Checkbox,
  Col,
  DatePicker,
  Form,
  Input,
  InputNumber,
  Modal,
  Radio,
  Row,
  Select,
  Typography,
  UploadFile,
} from 'antd';
import { useCallback, useEffect, useState } from 'react';
import {
  createOfferRefund,
  getListContractOrderMoney,
  getListCustomer,
  getListInterestReceipt,
  getListProject,
} from '../../../../service/offer';
import { useCreateField } from '../../../../hooks';
import UploadFileCreateOffer from '../uploadFileCreateOffer';
import SingleSelectLazy from '../../../../components/select/singleSelectLazy';
import dayjs from 'dayjs';
import { formatNumber } from '../../../../utilities/regex';
import {
  Bank,
  ContractLiquidationOrderMoney,
  Customer,
  ICreateOfferOrderMoneyAccountancy,
  InterestReceipt,
} from '../../../../types/offer';
import { FORMAT_DATE, FORMAT_DATE_API } from '../../../../constants/common';
import ModalComponent from '../../../../components/modal';
import './styles.scss';
import { Project } from '../../../../types/project/project';
import { getFullAddress } from '../../../../utilities/shareFunc';

const { Title } = Typography;
const { Option } = Select;

interface ExtendedUploadFile extends UploadFile {
  key?: string;
  absoluteUrl?: string;
}

interface OfferCreateModalProps {
  visible: boolean;
  onClose: () => void;
  states?: { label: string; value: string }[];
}

const OfferCreateModal = ({
  visible,
  onClose,
  states = [
    { label: 'Chuyển khoản', value: 'TRANSFER' },
    { label: 'Tiền mặt', value: 'CASH' },
  ],
}: OfferCreateModalProps) => {
  const [form] = Form.useForm();
  const money = Form.useWatch('money', form);
  const interestChecked = Form.useWatch('isInterest', form);
  const interestAmount = Form.useWatch('interestAmount', form);
  const contractId = Form.useWatch('contractId', form);
  const paymentBatchName = Form.useWatch('paymentBatch', form);
  const state = Form.useWatch(['state'], form);
  const [fileList, setFileList] = useState<ExtendedUploadFile[]>([]);
  const [type, setType] = useState<string>('PRIMARY_CONTRACT');
  const [banks, setBanks] = useState<Bank[]>([]);
  const [paymentBatchs, setPaymentBatchs] = useState<any[]>([]);
  const [propertyTicketId, setPropertyTicketId] = useState<string | undefined>('');
  const [projects, setProjects] = useState<any | undefined>();
  const [customer, setCustomer] = useState<Customer | undefined>();
  const [interestCalculations, setInterestCalculations] = useState<InterestReceipt | undefined>();
  const [interestCalculationsContract, setInterestCalculationsContract] = useState<any>([]);
  const [isMatchingInstallmentName, setIsMatchingInstallmentName] = useState<boolean>(false);

  const [, setTotalMoneyOfferOrder] = useState<number>(0);
  const [projectId, setProjectId] = useState<string | undefined>('');
  const [selectedPaymentBatchValue, setSelectedPaymentBatchValue] = useState<string | undefined>(undefined);
  const [remainingAmount, setRemainingAmount] = useState<number>(0);

  const { mutateAsync: create, isPending } = useCreateField<ICreateOfferOrderMoneyAccountancy>({
    keyOfListQuery: ['get-offer-order'],
    apiQuery: createOfferRefund,
    isMessageError: false,
    messageSuccess: 'Tạo đề nghị thu tiền thành công!',
  });

  useEffect(() => {
    if (state === 'TRANSFER') {
      form.setFieldsValue({
        bankName: undefined,
        bankNumber: undefined,
      });
    }
  }, [banks, form, state]);

  const handleSelectCustomer = (values: Customer) => {
    const companyAddress = getFullAddress(values?.company?.address);
    const personalAddress = getFullAddress(values?.info?.rootAddress);
    const newPropertyTicketId = dayjs().unix().toString();
    setPropertyTicketId(newPropertyTicketId);
    setCustomer(values);
    form.setFieldsValue({
      customerId: values?.id,
      address: values?.type === 'business' ? companyAddress : personalAddress,
    });
  };

  const handleSelectContract = (values: ContractLiquidationOrderMoney) => {
    const companyAddress = getFullAddress(values?.primaryTransaction?.customer?.company?.address);
    const personalAddress = getFullAddress(values?.primaryTransaction?.customer?.info?.rootAddress);

    setPropertyTicketId(values?.primaryTransaction?.id || '');

    const installments = values?.policyPayment?.schedule?.installments || [];
    const updatedPaymentBatch = [...installments, { value: 'other-batch', name: 'Khác' }];
    setPaymentBatchs(updatedPaymentBatch);

    // Lưu interestCalculations từ hợp đồng
    setInterestCalculationsContract(values?.interestCalculations);
    setInterestCalculations(undefined);
    setSelectedPaymentBatchValue(undefined);
    setIsMatchingInstallmentName(false);
    setRemainingAmount(0);
    setTotalMoneyOfferOrder(0);

    form.setFieldsValue({
      contractId: values?.id,
      productCode: values?.primaryTransaction?.propertyUnit?.code || '',
      name:
        values?.primaryTransaction?.customer?.type === 'business'
          ? values?.primaryTransaction?.customer?.company?.name
          : values?.primaryTransaction?.customer?.personalInfo?.name || '',
      identityNumber:
        values?.primaryTransaction?.customer?.type === 'business'
          ? values?.primaryTransaction?.customer?.taxCode
          : values?.primaryTransaction?.customer?.identities?.[0]?.value || '',
      address: values?.primaryTransaction?.customer?.type === 'business' ? companyAddress : personalAddress,
      paymentBatch: undefined,
      money: undefined,
    });
  };

  const handleSelectPaymentBatch = async (index: number) => {
    const selectedPaymentBatch = paymentBatchs[index];
    console.log(selectedPaymentBatch);
    if (selectedPaymentBatch) {
      form.setFieldsValue({
        paymentBatch: selectedPaymentBatch.name,
        interestReceipt: undefined,
        interestAmount: undefined,
        isInterest: false,
        totalMoney: undefined,
      });

      setSelectedPaymentBatchValue(selectedPaymentBatch.value);

      // So sánh paymentBatchName với installmentName trong interestCalculations
      const isMatchingInstallmentName = Array.isArray(interestCalculationsContract)
        ? interestCalculationsContract.some(
            (calculation: InterestReceipt) => calculation.installmentName === selectedPaymentBatch.name,
          )
        : false;

      setIsMatchingInstallmentName(isMatchingInstallmentName);

      if (selectedPaymentBatch.value !== 'other-batch') {
        const moneyOfferOrder = Math.round(
          (selectedPaymentBatch.totalAmount || 0) - (selectedPaymentBatch.totalTransfered || 0),
        );
        form.setFieldsValue({
          money: moneyOfferOrder > 0 ? moneyOfferOrder : 0,
        });
        setRemainingAmount(moneyOfferOrder > 0 ? moneyOfferOrder : 0);
        setTotalMoneyOfferOrder(moneyOfferOrder > 0 ? moneyOfferOrder : 0);
      } else {
        form.setFieldsValue({
          money: 0,
        });
        setRemainingAmount(0);
        setTotalMoneyOfferOrder(0);
      }

      try {
        await form.validateFields(['money']);
      } catch (error) {}
    }
  };

  const handleSelectProject = (values: Project) => {
    setProjects(values);
    setProjectId(values?.id as string | undefined);
    setBanks(values?.banks || []);

    setPropertyTicketId('');
    setTotalMoneyOfferOrder(0);
    setSelectedPaymentBatchValue(undefined);

    form.resetFields([
      'contractId',
      'customerId',
      'propertyTicket',
      'productCode',
      'name',
      'identityNumber',
      'address',
      'money',
      'bankName',
      'bankNumber',
      'paymentBatch',
    ]);

    form.setFieldsValue({
      projectId: values?.id,
      bankName: undefined,
      bankNumber: undefined,
    });
  };

  const handleSelectBank = (value: string) => {
    const selectedBank = banks?.find((bank: Bank) => bank?.code === value);
    form.setFieldsValue({
      bankName: selectedBank?.name || '',
      bankNumber: selectedBank?.accountNumber || '',
    });
  };

  const handleSelectProposalType = (value: string) => {
    setType(value);
    form.resetFields();
    setFileList([]);
    setBanks([]);
    setPaymentBatchs([]);
    setTotalMoneyOfferOrder(0);
    setSelectedPaymentBatchValue(undefined);

    form.setFieldsValue({
      contractId: undefined,
      type: value,
      state: 'TRANSFER',
      collectMoneyDate: dayjs(),
    });
  };
  useEffect(() => {
    const total = interestChecked ? (money || 0) + (interestAmount || 0) : money || 0;
    form.setFieldsValue({ totalMoney: total });
  }, [money, interestChecked, interestAmount, form]);

  const handleSelectInterestReceipt = (values: InterestReceipt) => {
    setInterestCalculations(values);
    form.setFieldsValue({ interestAmount: values?.remainingAmount });
  };

  const handleCancel = useCallback(async () => {
    if (form.isFieldsTouched()) {
      Modal.confirm({
        title: 'Xác nhận hủy',
        content: 'Dữ liệu chưa được lưu, bạn có chắc chắn muốn thoát khỏi trang không?',
        cancelText: 'Quay lại',
        onOk: async () => {
          await form.resetFields();
          setFileList([]);
          onClose();
        },
        okButtonProps: { type: 'default' },
        cancelButtonProps: { type: 'primary' },
      });
    } else {
      await form.resetFields();
      setFileList([]);
      onClose();
    }
  }, [form, onClose]);

  const handleCreate = async (values: ICreateOfferOrderMoneyAccountancy) => {
    const payload = {
      bankName: values?.bankName || '',
      bankNumber: values?.bankNumber || '',
      collectMoneyDate: values?.collectMoneyDate
        ? dayjs(values?.collectMoneyDate).format(FORMAT_DATE_API)
        : dayjs().format(FORMAT_DATE_API),
      contentBank: values?.contentBank,
      contractId: values?.contractId,
      description: values?.description,
      money: values?.money || 0,
      interestAmount: values?.interestAmount,
      interestCalculations: {
        ...interestCalculations,
      },
      isInterest: values?.isInterest,
      totalMoney: values?.money || 0,
      paymentBatch: selectedPaymentBatchValue === 'other-batch' ? 'other-batch' : values?.paymentBatch,
      files: fileList.map(file => ({
        uid: file.uid || '',
        name: file.name || '',
        url: file.key || '',
        absoluteUrl: file?.absoluteUrl || '',
        uploadName: file.name,
      })),
      propertyTicket: {
        id: propertyTicketId,
        customer: {
          id: customer?.id,
          name: customer?.name,
          address: customer?.type === 'business' ? customer?.company?.address : customer?.info?.rootAddress,
          type: customer?.type,
        },
        project: projects,
      },
      state: values?.state,
      type: values?.type,
    };

    try {
      const resp = await create(payload);
      if (resp?.data?.statusCode === '0' && resp?.data?.success) {
        await form.resetFields();
        setFileList([]);
        onClose();
      }
    } catch (error) {
      console.error('Create error:', error);
    }
  };

  return (
    <ModalComponent
      className="create-offer-modal"
      title="Tạo mới đề nghị thu tiền (Kế toán)"
      open={visible}
      onCancel={handleCancel}
      footer={null}
      destroyOnClose
    >
      <Form form={form} name="offer-refund-create" layout="vertical" onFinish={handleCreate}>
        <Row gutter={[64, 24]}>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin đề nghị thu tiền
            </Title>
            <Row gutter={24}>
              <Col span={24}>
                <Form.Item
                  label="Loại đề nghị"
                  name="type"
                  rules={[{ required: true, message: 'Vui lòng chọn loại đề nghị' }]}
                  initialValue="PRIMARY_CONTRACT"
                >
                  <Select placeholder="Chọn loại đề nghị" onChange={handleSelectProposalType}>
                    <Option value="PRIMARY_CONTRACT">Hợp đồng</Option>
                    <Option value="KTTT">Khác</Option>
                  </Select>
                </Form.Item>
              </Col>

              <Col span={24}>
                <Form.Item label="Dự án" name="projectId" rules={[{ required: true, message: 'Vui lòng chọn dự án' }]}>
                  <SingleSelectLazy
                    queryKey={['get-projects']}
                    apiQuery={getListProject}
                    placeholder="Chọn dự án"
                    keysLabel={'name'}
                    handleSelect={handleSelectProject}
                    moreParams={{ status: '01' }}
                  />
                </Form.Item>
              </Col>

              <Col span={24}>
                {type === 'PRIMARY_CONTRACT' ? (
                  <Form.Item
                    label="Hợp đồng"
                    name="contractId"
                    rules={[{ required: true, message: 'Vui lòng chọn hợp đồng' }]}
                  >
                    <SingleSelectLazy
                      queryKey={['get-contract']}
                      apiQuery={getListContractOrderMoney}
                      placeholder="Chọn hợp đồng"
                      keysLabel={'name'}
                      handleSelect={handleSelectContract}
                      enabled={!!projectId}
                      moreParams={{
                        _fields: 'id,name,primaryTransaction,policyPayment,interestCalculations',
                        status: 'accountant_waiting,approved',
                        forAccountant: true,
                        projectId: projectId,
                      }}
                    />
                  </Form.Item>
                ) : (
                  <Form.Item
                    label="Khách hàng"
                    name="customerId"
                    rules={[{ required: true, message: 'Vui lòng chọn khách hàng' }]}
                  >
                    <SingleSelectLazy
                      queryKey={['get-customer']}
                      apiQuery={getListCustomer}
                      placeholder="Chọn khách hàng"
                      keysLabel={['code', 'name']}
                      handleSelect={handleSelectCustomer}
                      enabled={type !== 'PRIMARY_CONTRACT'}
                    />
                  </Form.Item>
                )}
              </Col>
              {type === 'PRIMARY_CONTRACT' && (
                <Col span={24}>
                  <Form.Item label="Số sản phẩm" name="productCode">
                    <Input placeholder="Số sản phẩm" disabled />
                  </Form.Item>
                </Col>
              )}

              {type === 'PRIMARY_CONTRACT' && (
                <Col span={14}>
                  <Form.Item label="Tên khách hàng" name="name">
                    <Input placeholder="Tên khách hàng" disabled />
                  </Form.Item>
                </Col>
              )}

              {type === 'PRIMARY_CONTRACT' && (
                <Col span={10}>
                  <Form.Item label="Số giấy tờ" name="identityNumber">
                    <Input placeholder="Số giấy tờ" disabled />
                  </Form.Item>
                </Col>
              )}
              <Col span={24}>
                <Form.Item label="Địa chỉ liên lạc" name="address">
                  <Input placeholder="Địa chỉ liên lạc" disabled />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Phương thức giao dịch"
                  name="state"
                  rules={[{ required: true, message: 'Vui lòng chọn phương thức giao dịch' }]}
                  layout="horizontal"
                  initialValue={'TRANSFER'}
                >
                  <Radio.Group style={{ width: '100%' }}>
                    <Row>
                      {states.map(state => (
                        <Col span={12} style={{ display: 'flex', justifyContent: 'flex-end' }} key={state.value}>
                          <Radio key={state.value} value={state.value}>
                            {state.label}
                          </Radio>
                        </Col>
                      ))}
                    </Row>
                  </Radio.Group>
                </Form.Item>
                {state === 'TRANSFER' && (
                  <div
                    style={{
                      backgroundColor: 'rgba(0, 0, 0, 0.02)',
                      border: '1px solid rgba(0, 0, 0, 0.15)',
                      borderRadius: 4,
                      padding: 16,
                      marginTop: 8,
                    }}
                  >
                    <Row gutter={[16, 16]}>
                      <Col span={12}>
                        <Form.Item
                          label="Ngân hàng"
                          name="bankName"
                          rules={[{ required: true, message: 'Vui lòng chọn ngân hàng' }]}
                        >
                          <Select
                            placeholder="Chọn ngân hàng"
                            allowClear
                            filterOption={(input, option) =>
                              typeof option?.label === 'string'
                                ? option.label.toLowerCase().includes(input.toLowerCase())
                                : false
                            }
                            showSearch
                            options={banks.map(item => ({
                              value: item.code,
                              label: item.name,
                            }))}
                            onChange={handleSelectBank}
                          />
                        </Form.Item>
                      </Col>
                      <Col span={12}>
                        <Form.Item
                          label="Số tài khoản"
                          name="bankNumber"
                          rules={[{ required: true, message: 'Vui lòng nhập số tài khoản' }]}
                        >
                          <Input placeholder="Số tài khoản" disabled />
                        </Form.Item>
                      </Col>
                    </Row>
                    <Row gutter={[16, 16]}>
                      <Col span={24}>
                        <Form.Item name="contentBank">
                          <Input.TextArea placeholder="Nhập nội dung KH chuyển khoản" maxLength={255} />
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                )}
              </Col>
            </Row>
          </Col>
          <Col span={12}>
            <Title level={5} style={{ marginBottom: 16 }}>
              Thông tin chi phí
            </Title>
            <Row gutter={24}>
              {type === 'PRIMARY_CONTRACT' && (
                <Col span={24}>
                  <Form.Item
                    label="Đợt thanh toán"
                    name="paymentBatch"
                    rules={[{ required: true, message: 'Vui lòng chọn đợt thanh toán' }]}
                  >
                    <Select
                      placeholder="Chọn đợt thanh toán"
                      allowClear
                      filterOption={(input, option) =>
                        typeof option?.label === 'string'
                          ? option.label.toLowerCase().includes(input.toLowerCase())
                          : false
                      }
                      showSearch
                      options={paymentBatchs?.map((item, index) => ({
                        value: index,
                        label: item.name,
                      }))}
                      onChange={handleSelectPaymentBatch}
                    />
                  </Form.Item>
                </Col>
              )}
              <Col span={24}>
                <Form.Item
                  label="Số tiền đề nghị thu"
                  name="money"
                  rules={[
                    {
                      required: true,
                      message: 'Vui lòng nhập số tiền đề nghị thu',
                    },
                    {
                      validator: (_, value) => {
                        if (value <= 0) {
                          return Promise.reject(new Error('Vui lòng nhập vào giá trị lớn hơn 0'));
                        }
                        if (
                          selectedPaymentBatchValue !== 'other-batch' &&
                          remainingAmount > 0 &&
                          value > remainingAmount
                        ) {
                          return Promise.reject(
                            new Error(
                              'Khách hàng đã trả đủ tiền cho đợt thanh toán, vui lòng chọn đợt thanh toán khác!',
                            ),
                          );
                        }
                        return Promise.resolve();
                      },
                    },
                  ]}
                >
                  <InputNumber
                    placeholder="Nhập số tiền đề nghị thu"
                    style={{ width: '100%' }}
                    formatter={formatNumber}
                    maxLength={15}
                    precision={0}
                    min={1}
                    onKeyPress={e => {
                      const char = e.key;
                      if (!/[0-9]/.test(char)) {
                        e.preventDefault();
                      }
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Lý do thanh toán"
                  name="description"
                  rules={[{ required: true, message: 'Vui lòng nhập lý do thanh toán', whitespace: true }]}
                >
                  <Input.TextArea placeholder="Nhập lý do thanh toán" maxLength={255} />
                </Form.Item>
              </Col>
              <Col span={24}>
                <Form.Item
                  label="Ngày nộp tiền"
                  name="collectMoneyDate"
                  initialValue={dayjs()}
                  rules={[{ required: true, message: 'Vui lòng chọn ngày nộp tiền' }]}
                >
                  <DatePicker format={FORMAT_DATE} placeholder={FORMAT_DATE} />
                </Form.Item>
              </Col>
              {type === 'PRIMARY_CONTRACT' && isMatchingInstallmentName && (
                <>
                  <Col span={24}>
                    <Form.Item label="Phiếu công nợ" name="interestReceipt">
                      <SingleSelectLazy
                        queryKey={['get-contract']}
                        apiQuery={getListInterestReceipt}
                        placeholder="Chọn phiếu công nợ"
                        keysLabel={'title'}
                        handleSelect={handleSelectInterestReceipt}
                        moreParams={{ id: contractId, status: 'INIT', installmentName: paymentBatchName }}
                        enabled={!!paymentBatchName}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item label="Số tiền lãi" name="interestAmount">
                      <InputNumber
                        formatter={formatNumber}
                        placeholder="Số tiền lãi"
                        suffix={'VNĐ'}
                        style={{ width: '100%' }}
                        disabled
                      />
                    </Form.Item>
                  </Col>
                  <Col span={24}>
                    <Form.Item valuePropName="checked" name="isInterest">
                      <Checkbox>Tính lãi</Checkbox>
                    </Form.Item>
                  </Col>

                  <Col span={24}>
                    <Form.Item label="Tổng tiền" name="totalMoney">
                      <InputNumber
                        formatter={formatNumber}
                        placeholder="Tổng tiền"
                        suffix={'VNĐ'}
                        style={{ width: '100%' }}
                        disabled
                      />
                    </Form.Item>
                  </Col>
                </>
              )}
              {type === 'PRIMARY_CONTRACT' && (
                <Col span={24}>
                  <Form.Item label="Tải tệp định kèm" name="files">
                    <UploadFileCreateOffer
                      fileList={fileList}
                      setFileList={setFileList}
                      uploadPath="transaction/create/contract/installments"
                    />
                  </Form.Item>
                </Col>
              )}
            </Row>
          </Col>
        </Row>

        <div className="create-footer">
          <div className="button-create">
            <Button type="primary" htmlType="submit" loading={isPending}>
              Lưu
            </Button>
          </div>
        </div>
      </Form>
    </ModalComponent>
  );
};

export default OfferCreateModal;

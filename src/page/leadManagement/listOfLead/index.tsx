import { Button, Flex, TableColumnsType } from 'antd';
import { useEffect, useState } from 'react';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { LEAD_CONFIG } from '../../../configs/path';
import { PERMISSION_LEAD } from '../../../constants/permissions/lead';
import { useCheckPermissions, useFetch } from '../../../hooks';
import { getListOfLeadAll } from '../../../service/lead';
import { ILead } from '../../../types/lead';
import { columnsListOfLead } from '../columns';
import CreateOfLead from '../createOfLead';
import { useLeadStore } from '../store';
import FilterSearch, { TFilterLeadConfig } from './components/FilterSearch';

const ListOfLead = () => {
  const [openCreate, setOpenCreate] = useState(false);
  const [filterParams, setFilterParams] = useState<TFilterLeadConfig>();

  const { setType, setDefaultDataConfigs, setInitialValues } = useLeadStore(state => state);

  const { configCreate, configGetId } = useCheckPermissions(PERMISSION_LEAD);

  const actionsColumns: TableColumnsType = [
    ...columnsListOfLead,
    ...(configGetId
      ? [
          {
            dataIndex: 'action',
            key: 'action',
            width: '100px',
            align: 'center' as const,
            render: (_: unknown, record: ILead) => {
              const openViewDetail = () => {
                window.open(`${LEAD_CONFIG}/${record?.id}`, '_blank', 'noopener,noreferrer');
              };

              return <ActionsColumns handleViewDetail={openViewDetail} />;
            },
          },
        ]
      : []),
  ];

  const {
    data: dataLead,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<ILead[]>({
    queryKeyArrWithFilter: ['list-lead-repo', filterParams],
    api: getListOfLeadAll,
    moreParams: { ...filterParams },
  });

  useEffect(() => {
    setDefaultDataConfigs(undefined);
    setInitialValues({} as ILead);
  }, [setDefaultDataConfigs, setInitialValues]);

  const handleOpenModalCreate = () => {
    setOpenCreate(true);
    setType('create');
  };

  const handleCancelModalCreate = () => {
    setOpenCreate(false);
  };

  return (
    <div className="wrapper-list-Lead">
      <BreadCrumbComponent />
      <div className="header-content">
        <FilterSearch setFilterParams={setFilterParams} />

        <Flex gap={10}>
          {configCreate && (
            <Button type="primary" onClick={handleOpenModalCreate}>
              Thêm mới
            </Button>
          )}
        </Flex>
      </div>
      <div className="table-list-Lead">
        <TableComponent
          queryKeyArr={['list-lead-repo', filterParams]}
          columns={actionsColumns}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={dataLead?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <CreateOfLead open={openCreate} onCancel={handleCancelModalCreate} />
    </div>
  );
};

export default ListOfLead;

import { Col, DatePicker, Form, Row } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { Dispatch, SetStateAction, useState } from 'react';
import DropdownFilterSearch from '../../../../../components/dropdown/dropdownFilterSearch';
import MultiSelectLazy from '../../../../../components/select/mutilSelectLazy';
import { DEFAULT_PARAMS, FORMAT_DATE, FORMAT_DATE_API } from '../../../../../constants/common';
import { getListEmployeeInternal } from '../../../../../service/employee';
import { TEmployeeAll } from '../../../../../types/customers';
import './styles.scss';

export type TFilterLeadConfig = {
  createdBy?: string;
  createFrom?: string | Dayjs | null;
  createTo?: string | Dayjs | null;
  search?: string;
};

function FilterSearch({
  setFilterParams,
}: {
  setFilterParams: Dispatch<SetStateAction<TFilterLeadConfig | undefined>>;
}) {
  const [form] = Form.useForm();
  const [isOpenFilter, setIsOpenFilter] = useState(false);

  const handleSubmitFilter = (values: TFilterLeadConfig) => {
    const newFilter: Record<string, unknown> = {
      createdBy: values?.createdBy || null,
      createFrom: values?.createFrom ? dayjs(values?.createFrom).format(FORMAT_DATE_API) : null,
      createTo: values?.createTo ? dayjs(values?.createTo).format(FORMAT_DATE_API) : null,
    };
    setFilterParams(prev => ({ ...prev, ...DEFAULT_PARAMS, ...newFilter }));
    setIsOpenFilter(false);
  };
  const handleOpenChangeDropdown = (value: boolean) => {
    setIsOpenFilter(value);
  };

  const handleSelectEmployee = (values: TEmployeeAll[]) => {
    form.setFieldsValue({ createdBy: values.map(item => item.accountId).join(',') });
  };

  const handleClearFilters = () => {
    setTimeout(() => {
      setFilterParams(prev => ({ search: prev?.search }));
      setIsOpenFilter(false);
    }, 100);
  };
  const onChangeSearch = (searchTerm: string) => {
    const search = searchTerm || undefined;
    setFilterParams(prev => ({ ...prev, ...DEFAULT_PARAMS, search: search }));
  };

  return (
    <>
      <DropdownFilterSearch
        rootClassName="wrapper-filter-lead"
        submitFilter={handleSubmitFilter}
        placeholder="Tìm kiếm"
        showParams={false}
        handleOpenChange={handleOpenChangeDropdown}
        isOpenFilter={isOpenFilter}
        form={form}
        onChangeSearch={onChangeSearch}
        onClearFilters={handleClearFilters}
        extraFormItems={
          <>
            <Form.Item label="Người tạo" name="createdBy">
              <MultiSelectLazy
                enabled={isOpenFilter}
                queryKey={['getAllDataEmployees']}
                apiQuery={getListEmployeeInternal}
                keysLabel={['name', 'email']}
                keysTag={'email'}
                placeholder="Chọn nhân viên"
                handleListSelect={handleSelectEmployee}
              />
            </Form.Item>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item label="Ngày tạo" name="createFrom">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const createTo = form.getFieldValue('createTo');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (createTo && current > dayjs(createTo).startOf('day')) // Disable ngày sau "Đến ngày"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item label="Đến ngày" name="createTo">
                  <DatePicker
                    format={FORMAT_DATE}
                    disabledDate={current => {
                      const createFrom = form.getFieldValue('createFrom');
                      return (
                        (current && current > dayjs().startOf('day')) ||
                        (createFrom && current < dayjs(createFrom).startOf('day')) // Disable ngày trước "Ngày tạo"
                      );
                    }}
                  />
                </Form.Item>
              </Col>
            </Row>
          </>
        }
      />
    </>
  );
}
export default FilterSearch;

import { PlusOutlined } from '@ant-design/icons';
import { App, Button, Col, Form, Input, Row, Table, TableColumnsType, Typography } from 'antd';
import React, { useState } from 'react';
import ModalComponent from '../../../components/modal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { useCreateField } from '../../../hooks';
import { createLeadRepo } from '../../../service/lead';
import { IConfig, ILead } from '../../../types/lead';
import { columnsListOfConfig } from '../columns';
import {
  convertNotification,
  formatExploitTime,
  formatOrgCharts,
  formatWorkingTime,
} from '../components/common/shareFunc';
import ModalConfigLead from '../components/modalConfigLead';
import TimeLead from '../components/timeLead';
import UnitParticipating from '../components/unitParticipating';
import { useLeadStore } from '../store';
import './styles.scss';

const { Title } = Typography;
const { Item } = Form;

type Props = { open: boolean; onCancel: () => void };

const CreateOfLead = (props: Props) => {
  const { open, onCancel } = props;
  const { modal } = App.useApp();

  const [form] = Form.useForm();
  const [isOpenModalConfigLead, setIsOpenModalConfigLead] = useState(false);
  const [defaultDataConfig, setDefaultDataConfig] = useState<IConfig>();

  const { dataConfigs, removeDataConfig, refetchDataConfig, addDataConfig, setTypeConfig } = useLeadStore(
    state => state,
  );

  const { mutateAsync: createLead, isPending } = useCreateField<ILead>({
    apiQuery: createLeadRepo,
    keyOfListQuery: ['list-lead-repo'],
    label: 'kho cấu hình',
  });

  const handleSubmit = async (values: ILead) => {
    const assignDurationInMinuteConfigHot = form.getFieldValue(['configHot', 'assignDurationInMinute']);

    const newData = {
      ...values,
      configHot: {
        ...values?.configHot,
        deassignLimit: values?.configHot?.deassignLimit ? Number(values?.configHot?.deassignLimit) : undefined,
        assignDuration: assignDurationInMinuteConfigHot ? Number(assignDurationInMinuteConfigHot) : undefined,
        notification: convertNotification(values?.configHot?.notification as string[]),
        orgChartIds: [values?.configHot?.orgCharts?.[0]?.id],
        orgCharts: formatOrgCharts(values?.configHot?.orgCharts, values?.configHot?.deliverType),
        workingTime: values?.configHot?.workingTime?.map(formatWorkingTime) || null,
      },
      configs:
        dataConfigs?.map(item => ({
          ...item,
          deassignLimit: item.deassignLimit ? Number(item.deassignLimit) : undefined,
          assignDuration: item.assignDuration ? Number(item.assignDuration) : undefined,
          orgChartIds: [item?.orgCharts?.[0]?.id],
          orgCharts: formatOrgCharts(item.orgCharts, values?.configHot?.deliverType),
          projectId: item?.project?.id,
          exploitTime: formatExploitTime(item?.exploitTime) as { from: string; to: string },
          workingTime: item?.workingTime?.map(formatWorkingTime) || null,
        })) || [],
    } as ILead;
    const res = await createLead(newData);
    if (Number(res?.data?.statusCode) === 0) {
      onCancel();
      refetchDataConfig();
      form.resetFields();
    }
  };

  const columns: TableColumnsType = [
    ...columnsListOfConfig,
    {
      key: 'action',
      width: '96px',
      align: 'center',
      render: (_, record: IConfig, index: number) => {
        const textModalConfirmActive = record?.active === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';
        const openViewDetail = () => {
          setIsOpenModalConfigLead(true);
          setDefaultDataConfig({ ...record, _id: String(index) });
          setTypeConfig('update');
        };
        const handleActiveConfigLead = () => {
          return modalConfirm({
            modal: modal,
            title: `${textModalConfirmActive} khách hàng`,
            content: `Bạn có muốn ${textModalConfirmActive} khách hàng này không?`,
            handleConfirm: () => addDataConfig({ ...record, active: record?.active === 1 ? 2 : 1 }, String(index)),
          });
        };
        const handleDeleteConfigLead = () => {
          modalConfirm({
            modal: modal,
            title: `cấu hình lead`,
            content: `Bạn có muốn xoá Lead này không?`,
            handleConfirm: () => removeDataConfig(record),
          });
        };

        return (
          <ActionsColumns
            handleViewDetail={openViewDetail}
            textModalConfirmActive={textModalConfirmActive}
            handleActive={handleActiveConfigLead}
            handleDelete={handleDeleteConfigLead}
          />
        );
      },
    },
  ];

  const handleModalConfigLead = () => {
    setIsOpenModalConfigLead(true);
    setTypeConfig('create');
    setDefaultDataConfig(undefined);
  };

  return (
    <ModalComponent
      rootClassName="wrapper-create-of-lead"
      open={open}
      onCancel={() => {
        onCancel();
        form.resetFields();
        refetchDataConfig();
      }}
      title="Tạo mới cấu hình phân bổ"
      destroyOnClose
      footer={[
        <Button key="submit" type="primary" onClick={() => form.submit()} loading={isPending}>
          Lưu
        </Button>,
      ]}
    >
      <Form
        form={form}
        layout="horizontal"
        onFinish={handleSubmit}
        labelCol={{ xxl: 4, xl: 7, lg: 8, xs: 24 }}
        wrapperCol={{ xxl: 20, xl: 17, lg: 16, xs: 24 }}
      >
        <Title level={5}>Thông tin chung</Title>
        <Row gutter={16} className="general-info">
          <Col md={12} xs={24}>
            <Item
              label="Tên kho"
              name={'name'}
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 24 }}
              required
              rules={[{ required: true, message: 'Vui lòng nhập tên kho', whitespace: true }]}
            >
              <Input
                maxLength={60}
                placeholder="Nhập tên kho"
                onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                  e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                }}
              />
            </Item>
          </Col>
        </Row>
        <Title level={5}>Cấu hình Lead hot</Title>
        <Row gutter={16} className="general-info">
          <UnitParticipating parentName="configHot" />
          <Col span={24}></Col>
          <TimeLead parentName="configHot" />
          <Col span={24}>
            <Title level={5}>Danh sách cấu hình lead hiện có</Title>
            <Item
              name={['configs']}
              rules={[
                () => ({
                  validator() {
                    if (!dataConfigs?.length)
                      return Promise.reject(new Error('Bạn chưa thêm cấu hình lead hiện có nào'));
                    return Promise.resolve();
                  },
                }),
              ]}
              className="wrapper-table-config"
            >
              <Table
                columns={columns}
                dataSource={dataConfigs}
                rowKey={'_id'}
                sticky={{ offsetHeader: 0 }}
                summary={() => (
                  <Table.Summary fixed="top">
                    <Table.Summary.Row>
                      <Table.Summary.Cell index={0} colSpan={columns.length}>
                        <Button
                          type="default"
                          icon={<PlusOutlined />}
                          iconPosition={'end'}
                          onClick={handleModalConfigLead}
                        >
                          Thêm cấu hình
                        </Button>
                      </Table.Summary.Cell>
                    </Table.Summary.Row>
                  </Table.Summary>
                )}
              />
            </Item>
          </Col>
        </Row>
      </Form>
      <ModalConfigLead
        open={isOpenModalConfigLead}
        onCancel={() => setIsOpenModalConfigLead(false)}
        defaultDataConfig={defaultDataConfig}
      />
    </ModalComponent>
  );
};

export default CreateOfLead;

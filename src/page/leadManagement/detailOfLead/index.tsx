import { PlusOutlined } from '@ant-design/icons';
import { App, Button, Col, Form, Input, Row, Spin, Table, Typography } from 'antd';
import { ColumnsType } from 'antd/es/table';
import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import BreadCrumbComponent from '../../../components/breadCrumb';
import ButtonOfPageDetail from '../../../components/button/buttonOfPageDetail';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { modalConfirm } from '../../../components/modal/specials/ModalConfirm';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { PERMISSION_LEAD } from '../../../constants/permissions/lead';
import { useCheckPermissions, useFetch, useUpdateField } from '../../../hooks';
import {
  changeStatusLeadConfig,
  getDetailOfLeadAll,
  putLeadConfigHot,
  softDeleteLeadConfig,
} from '../../../service/lead';
import { IConfig, ILead, IWorkingTime } from '../../../types/lead';
import { columnsListOfConfig } from '../columns';
import {
  convertNotification,
  formatExploitTimeForComponent,
  formatNotificationForComponent,
  formatOrgCharts,
  formatWorkingTime,
  formatWorkingTimeForComponent,
} from '../components/common/shareFunc';
import ModalConfigLead from '../components/modalConfigLead';
import TimeLead from '../components/timeLead';
import UnitParticipating from '../components/unitParticipating';
import { useLeadStore } from '../store';
import './styles.scss';

const { Title } = Typography;
const { Item } = Form;

const DetailOfLead = () => {
  const { id } = useParams();
  const [form] = Form.useForm();
  const { modal } = App.useApp();
  const {
    configGetId: isPermissionConfigGetById,
    configChangeStatus: isPermissionGetConfigChangeStatus,
    configUpdate: isPermissionUpdate,
    configDelete: isPermissionConfigDelete,
  } = useCheckPermissions(PERMISSION_LEAD);

  const [isOpenModalDelete, setIsOpenModalDelete] = useState(false);
  const [isOpenModalConfigLead, setIsOpenModalConfigLead] = useState(false);
  const [defaultDataConfig, setDefaultDataConfig] = useState<IConfig | undefined>();
  const [resetKey, setResetKey] = useState(0);
  const {
    dataConfigs,
    setType,
    setDefaultDataConfigs,
    setTypeConfig,
    isModified,
    setIsModified,
    initialValues,
    setInitialValues,
  } = useLeadStore(state => state);

  const { data, isFetching } = useFetch<ILead>({
    queryKeyArr: ['get-lead-repo', id || ''],
    api: getDetailOfLeadAll,
    enabled: !!id,
    withFilter: false,
    moreParams: { id: id },
  });
  const dataLead = data?.data?.data;

  const { mutateAsync: changeStatusConfig } = useUpdateField<{ configCode: string; id?: string }>({
    apiQuery: changeStatusLeadConfig,
    keyOfDetailQuery: ['get-lead-repo', id || ''],
    label: '',
  });
  const { mutateAsync: updateLeadHot, isPending } = useUpdateField<ILead>({
    apiQuery: putLeadConfigHot,
    keyOfDetailQuery: ['get-lead-repo', id || ''],
    label: '',
  });

  useEffect(() => {
    if (!dataLead) return;

    const initialData: ILead = {
      ...dataLead,
      configHot: {
        ...dataLead.configHot,
        notification: Array.isArray(dataLead.configHot?.notification)
          ? dataLead.configHot.notification
          : formatNotificationForComponent(dataLead.configHot?.notification),
        workingTime: formatWorkingTimeForComponent(dataLead.configHot?.workingTime as IWorkingTime[]),
      },
      configs: dataLead.configs?.map(item => ({
        ...item,
        notification: Array.isArray(dataLead.configHot?.notification)
          ? dataLead.configHot.notification
          : formatNotificationForComponent(dataLead.configHot?.notification),
        workingTime: formatWorkingTimeForComponent(item?.workingTime as IWorkingTime[]),
        exploitTime: formatExploitTimeForComponent(item?.exploitTime as { from: string; to: string }),
      })) as IConfig[],
    };

    setType('update');
    setInitialValues(initialData);
    form.setFieldsValue(initialData);
    setDefaultDataConfigs(initialData?.configs);
  }, [dataLead, form, setDefaultDataConfigs, setInitialValues, setType]);

  const handleSubmit = async (values: ILead) => {
    try {
      await form.validateFields();
      const assignDurationInMinute = form.getFieldValue(['configHot', 'assignDurationInMinute']);
      const newData = {
        id: id!,
        name: values?.name?.trim(),
        configHot: {
          ...values?.configHot,
          assignDuration: assignDurationInMinute ? Number(assignDurationInMinute) : undefined,
          deassignLimit: values?.configHot?.deassignLimit ? Number(values?.configHot?.deassignLimit) : undefined,
          notification: convertNotification(values?.configHot?.notification as string[]),
          orgChartIds: [values?.configHot?.orgCharts?.[0]?.id],
          orgCharts: formatOrgCharts(values?.configHot?.orgCharts, values?.configHot?.deliverType),
          workingTime: values?.configHot?.workingTime?.map(formatWorkingTime) || null,
        },
      } as ILead;
      const res = await updateLeadHot(newData);
      const statusCode = Number(res?.data?.statusCode);
      if (statusCode === 0) {
        setIsModified(false);
      }
    } catch (error) {
      console.log('error :', error);
    }
  };

  const columns: ColumnsType<IConfig> = [
    ...columnsListOfConfig,
    {
      key: 'action',
      width: '96px',
      align: 'center',
      render: (_, record: IConfig) => {
        const textModalConfirmActive = record?.active === 1 ? 'Vô hiệu hoá' : 'Kích hoạt';

        const openViewDetail = () => {
          setIsOpenModalConfigLead(true);
          setDefaultDataConfig(record);
          setTypeConfig('update');
        };

        const handleActiveConfigLead = () => {
          return modalConfirm({
            modal: modal,
            title: `${textModalConfirmActive} khách hàng`,
            content: `Bạn có muốn ${textModalConfirmActive} khách hàng này không?`,
            handleConfirm: async () => await changeStatusConfig({ configCode: record?.code, id: id }),
          });
        };

        const handleDeleteConfigLead = () => {
          setIsOpenModalDelete(true);
          setDefaultDataConfig(record);
        };

        return (
          <ActionsColumns
            handleViewDetail={isPermissionConfigGetById ? openViewDetail : undefined}
            textModalConfirmActive={textModalConfirmActive}
            handleActive={isPermissionGetConfigChangeStatus ? handleActiveConfigLead : undefined}
            handleDelete={isPermissionConfigDelete ? handleDeleteConfigLead : undefined}
          />
        );
      },
    },
  ];
  const handleCancel = () => {
    form.resetFields();
    setIsModified(false);
    setResetKey(prev => prev + 1);
  };
  const validateForm = () => {
    setIsModified(true);
  };

  const handleModalConfigLead = () => {
    setIsOpenModalConfigLead(true);
    setTypeConfig('create');
    setDefaultDataConfig(undefined);
  };

  return (
    <div className="wrapper-detail-of-lead">
      <Spin spinning={isFetching || !initialValues} style={{ maxHeight: '100vh' }}>
        <BreadCrumbComponent titleBread={initialValues?.name} />
        <Form
          form={form}
          layout="horizontal"
          onFinish={handleSubmit}
          labelCol={{ xxl: 4, xl: 7, lg: 8, xs: 24 }}
          wrapperCol={{ xxl: 20, xl: 17, lg: 16, xs: 24 }}
          initialValues={initialValues}
          onValuesChange={validateForm}
        >
          <Title level={5}>Thông tin chung</Title>
          <Row gutter={16} className="general-info">
            <Col md={6} xs={24}>
              <Item label="Mã kho" name={'code'} labelCol={{ span: 24 }} wrapperCol={{ span: 24 }}>
                <Input disabled />
              </Item>
            </Col>
            <Col md={6} xs={24}>
              <Item
                label="Tên kho"
                name={'name'}
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 24 }}
                required
                rules={[{ required: true, message: 'Vui lòng nhập tên kho', whitespace: true }]}
              >
                <Input
                  maxLength={60}
                  placeholder="Nhập tên kho"
                  onInput={(e: React.ChangeEvent<HTMLInputElement>) => {
                    e.target.value = e.target.value.toUpperCase(); // Chuyển đổi trực tiếp
                  }}
                />
              </Item>
            </Col>
          </Row>
          <Title level={5}>Cấu hình Lead hot</Title>
          <Row gutter={16} className="general-info">
            <UnitParticipating
              parentName="configHot"
              defaultValueOrgCharts={initialValues?.configHot?.orgCharts}
              resetKey={resetKey}
            />
            <Col span={24}></Col>
            <TimeLead parentName="configHot" />
            <Col span={24}>
              <Title level={5}>Danh sách cấu hình lead hiện có</Title>
              <Item
                name={['configs']}
                rules={[
                  () => ({
                    validator() {
                      if (!dataConfigs?.length)
                        return Promise.reject(new Error('Bạn chưa thêm cấu hình lead hiện có nào'));
                      return Promise.resolve();
                    },
                  }),
                ]}
                className="wrapper-table-config"
              >
                <Table
                  columns={columns}
                  dataSource={dataConfigs}
                  rowKey={'code'}
                  sticky={{ offsetHeader: 0 }}
                  summary={() => (
                    <Table.Summary fixed="top">
                      <Table.Summary.Row>
                        <Table.Summary.Cell index={0} colSpan={columns.length}>
                          <Button
                            type="default"
                            icon={<PlusOutlined />}
                            iconPosition={'end'}
                            onClick={handleModalConfigLead}
                          >
                            Thêm cấu hình
                          </Button>
                        </Table.Summary.Cell>
                      </Table.Summary.Row>
                    </Table.Summary>
                  )}
                />
              </Item>
            </Col>
          </Row>
        </Form>
        {isModified && isPermissionUpdate && (
          <ButtonOfPageDetail
            handleSubmit={() => form.submit()}
            handleCancel={handleCancel}
            loadingSubmit={isPending}
          />
        )}
      </Spin>
      <ModalConfigLead
        open={isOpenModalConfigLead}
        onCancel={() => setIsOpenModalConfigLead(false)}
        defaultDataConfig={defaultDataConfig}
      />
      <ConfirmDeleteModal
        label="cấu hình"
        open={isOpenModalDelete}
        apiQuery={softDeleteLeadConfig}
        keyOfDetailQuery={['get-lead-repo', id || '']}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={id}
        extraValues={{ configCode: defaultDataConfig?.code }}
        title="Xóa cấu hình phân bổ"
        description="Vui lòng nhập lý do muốn xoá cấu hình phân bổ này"
      />
    </div>
  );
};

export default DetailOfLead;

import { Button, Col, Form, Input, Radio, Row, Table, TableColumnsType, Typography } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TBusinessPartner, TSelectDropdown } from '../../../../types/eVoucher';
import { DeleteOutlined } from '@ant-design/icons';
import { employeeApplyColumns } from '../columns';
import { useEVoucherStore } from '../../stroreEVoucher';
import { TPos } from '../../../../types/commissionPolicy';
import { getEmployees, getOrgCharts } from '../../../../service/evoucher';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { handleKeyDownAmountInterger } from '../../utils';

const { Item } = Form;
const { Text } = Typography;

const EmployeeApply: React.FC = () => {
  const form = Form.useFormInstance();

  const amountEmployeeApply = Form.useWatch('amountOfPosDistribution', form);
  const formCurrentListEmployeeApply = Form.useWatch('employeeApply', form);
  const formCurrentListEmployeeApplyPos = Form.useWatch('employeeApplyPos', form);

  const { initialValue, disabled: disableField, setIsModified } = useEVoucherStore();
  const defaultListEmployeeApply = useRef(initialValue?.employeeApply);
  const defaultListEmployeeApplyPos = useRef(initialValue?.employeeApply?.map(item => item?.pos));

  const [, setListEmployeeApplyPos] = useState<TPos[]>([]);
  const [listEmployeeApply, setListEmployeeApply] = useState<TBusinessPartner[]>([]);

  useEffect(() => {
    if (
      Array.isArray(defaultListEmployeeApply?.current) &&
      defaultListEmployeeApply?.current &&
      defaultListEmployeeApply?.current?.length > 0
    ) {
      setListEmployeeApply(defaultListEmployeeApply?.current);
    }
    if (
      Array.isArray(defaultListEmployeeApplyPos?.current) &&
      defaultListEmployeeApplyPos?.current &&
      defaultListEmployeeApplyPos?.current?.length > 0
    ) {
      const employeeApplyPos = defaultListEmployeeApplyPos?.current
        ?.map(item => item)
        .filter((pos, index, array) => pos && array.findIndex(p => p?.id === pos?.id) === index);
      setListEmployeeApplyPos(employeeApplyPos as TPos[]);
    }
  }, [defaultListEmployeeApply, defaultListEmployeeApplyPos, initialValue]);

  const handleRemove = useCallback(
    (value: string) => {
      const newVal = listEmployeeApply.filter(v => v?.id !== value);
      setListEmployeeApply(newVal);
      form?.setFieldValue('employeeApply', newVal);
    },
    [form, listEmployeeApply],
  );

  const handleSelectListPos = (values: TPos[]) => {
    form.setFieldsValue({
      employeeApplyPos:
        values?.map(value => ({
          name: value?.name,
          id: value?.id,
          code: value?.code,
        })) || [],
    });
    setListEmployeeApplyPos(values);
    setIsModified(true);
  };

  const handleSelectListEmployee = (values: TSelectDropdown[]) => {
    const newEmployeeApply =
      values?.map(value => ({
        phone: value?.option?.phone,
        email: value?.option?.email,
        pos: value?.option?.pos,
        name: value?.name,
        id: value?.id,
        code: value?.code,
      })) || [];
    form.setFieldsValue({
      employeeApply: newEmployeeApply,
    });
    setListEmployeeApply(newEmployeeApply);
    setIsModified(true);
  };

  const formattedDefaultPosValues = useMemo(() => {
    return defaultListEmployeeApplyPos?.current
      ? (defaultListEmployeeApplyPos?.current as TPos[])?.map((pos: TPos) => ({
          label: pos?.name ?? '',
          value: pos?.id ?? '',
          name: pos?.name,
          code: pos?.code || '',
          id: pos?.id,
        }))
      : [];
  }, []);

  const formattedDefaultEmployeeValues = useMemo(() => {
    return defaultListEmployeeApply?.current
      ? (defaultListEmployeeApply?.current as TBusinessPartner[])?.map((partner: TBusinessPartner) => ({
          label: partner?.name ?? '',
          value: partner?.id ?? '',
          name: partner?.name ?? '',
          code: partner?.code ?? '',
          id: partner?.id,
          phone: partner?.phone ?? '',
          email: partner?.email ?? '',
          pos: partner?.pos as unknown as string, // ép kiểu phù hợp với defaultvalue MultiSelect
        }))
      : [];
  }, []);

  const actionEmployeeApplyColumns: TableColumnsType = useMemo(() => {
    return [
      ...employeeApplyColumns,
      {
        title: 'Số lượng',
        width: '100px',
        dataIndex: 'amount',
        key: 'amount',
        render: () => {
          return <Text>{amountEmployeeApply || '-'}</Text>;
        },
      },
      {
        dataIndex: 'action',
        align: 'center',
        width: '80px',
        render: (_, record: TBusinessPartner) => {
          return (
            <Text
              style={{ color: '#1677FF', cursor: 'pointer' }}
              onClick={() => handleRemove(record?.id)}
              disabled={disableField}
            >
              Xóa
            </Text>
          );
        },
      },
    ];
  }, [amountEmployeeApply, disableField, handleRemove]);

  return (
    <>
      <Col xs={24} md={24}>
        <Item
          name={'amountOfPosDistribution'}
          label="Số lượng"
          required
          rules={[
            ({ getFieldValue }) => ({
              validator: async (_, value) => {
                const amount = getFieldValue('amount');

                if (amount && Number(value) > Number(amount)) {
                  return Promise.reject('Vui lòng nhập số lượng nhỏ hơn hoặc bằng số lượng phát hành');
                }
                if (!value || value.toString().trim() === '') {
                  return Promise.reject(new Error('Vui lòng nhập số lượng'));
                }
                return Promise.resolve();
              },
              validateTrigger: ['onChange'],
            }),
          ]}
        >
          <Input
            onKeyDown={handleKeyDownAmountInterger}
            placeholder="Nhập số lượng"
            maxLength={13}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item name={'confirmBeforeUse'} label="Xác nhận e-voucher trước khi sử dụng">
          <Radio.Group
            options={[
              { label: 'Có', value: true },
              { label: 'Không', value: false },
            ]}
            defaultValue={true}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item name="employeeApplyPos" label="Đơn vị xác nhận">
          <MultiSelectLazy
            placeholder="Chọn đơn vị"
            queryKey={['get-pos']}
            apiQuery={getOrgCharts}
            keysLabel={'name'}
            handleListSelect={handleSelectListPos}
            // enabled={!!enabled}
            keysTag={'name'}
            defaultValues={formattedDefaultPosValues}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item
          name="employeeApply"
          label="Nhân viên xác nhận"
          rules={[{ required: true, message: 'Vui lòng chọn nhân viên' }]}
        >
          <MultiSelectLazy
            placeholder="Chọn nhân viên"
            queryKey={['get-employees', formCurrentListEmployeeApplyPos]}
            apiQuery={getEmployees}
            keysLabel={'name'}
            handleListSelect={handleSelectListEmployee}
            // enabled={!!enabled}
            keysTag={'name'}
            moreParams={{ orgcharts: formCurrentListEmployeeApplyPos?.map((o: TPos) => o?.id)?.join(',') }}
            defaultValues={formattedDefaultEmployeeValues}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={12} style={{ marginBottom: '16px' }}>
        <Row gutter={{ md: 24, lg: 10 }}>
          <Col xs={24} md={16}>
            <Button
              icon={<DeleteOutlined />}
              onClick={() => {
                setListEmployeeApply([]);
                form?.setFieldValue('employeeApply', []);
              }}
              disabled={disableField}
            >
              Xóa tất cả
            </Button>
          </Col>
        </Row>
      </Col>
      <Col xs={24} md={24} style={{ marginBottom: '16px' }}>
        <Table
          className=""
          dataSource={formCurrentListEmployeeApply}
          columns={actionEmployeeApplyColumns}
          pagination={false}
        />
      </Col>
    </>
  );
};

export default EmployeeApply;

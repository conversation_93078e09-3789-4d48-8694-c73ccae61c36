import { Button, Col, Form, Radio, Row, Table, TableColumnsType, Typography } from 'antd';
import { useCallback, useEffect, useMemo, useRef, useState } from 'react';
import { TBusinessPartner, TPos, TSelectDropdown } from '../../../../types/eVoucher';
import { DeleteOutlined } from '@ant-design/icons';
import { employeeApplyCommonColumns } from '../columns';
import { useEVoucherStore } from '../../stroreEVoucher';
import { getEmployees, getOrgCharts } from '../../../../service/evoucher';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';

const { Item } = Form;
const { Text } = Typography;

const EmployeeApplyCommon: React.FC = () => {
  const form = Form.useFormInstance();

  const formCurrentListEmployeeApplyCommon = Form.useWatch('employeeApplyCommon', form);
  const formCurrentListEmployeeApplyCommonPos = Form.useWatch('employeeApplyCommonPos', form);

  const { initialValue, disabled: disableField, setIsModified } = useEVoucherStore();
  const defaultListEmployeeApplyCommon = useRef(initialValue?.employeeApplyCommon);
  const defaultListEmployeeApplyCommonPos = useRef(initialValue?.employeeApplyCommon?.map(item => item?.pos));
  const defaultListSelfCreatedVoucher = useRef(initialValue?.listSelfCreatedVoucher);

  const [, setListEmployeeApplyCommonPos] = useState<TPos[]>([]);
  const [listEmployeeApplyCommon, setListEmployeeApplyCommon] = useState<TBusinessPartner[]>([]);
  const [, setListSelfCreatedVoucher] = useState<TPos[]>([]);

  useEffect(() => {
    if (
      Array.isArray(defaultListEmployeeApplyCommon?.current) &&
      defaultListEmployeeApplyCommon?.current &&
      defaultListEmployeeApplyCommon?.current?.length > 0
    ) {
      setListEmployeeApplyCommon(defaultListEmployeeApplyCommon?.current);
    }
    if (
      Array.isArray(defaultListEmployeeApplyCommonPos?.current) &&
      defaultListEmployeeApplyCommonPos?.current &&
      defaultListEmployeeApplyCommonPos?.current?.length > 0
    ) {
      const employeeApplyPosCommon = defaultListEmployeeApplyCommonPos?.current
        ?.map(item => item)
        .filter((pos, index, array) => pos && array.findIndex(p => p?.id === pos?.id) === index);
      setListEmployeeApplyCommonPos(employeeApplyPosCommon as TPos[]);
    }
    if (
      Array.isArray(defaultListSelfCreatedVoucher?.current) &&
      defaultListSelfCreatedVoucher?.current &&
      defaultListSelfCreatedVoucher?.current?.length > 0
    ) {
      setListSelfCreatedVoucher(defaultListSelfCreatedVoucher?.current as TPos[]);
    }
  }, [defaultListEmployeeApplyCommon, defaultListEmployeeApplyCommonPos, defaultListSelfCreatedVoucher]);

  const handleRemove = useCallback(
    (value: string) => {
      const newVal = listEmployeeApplyCommon.filter(v => v?.id !== value);
      setListEmployeeApplyCommon(newVal);
      form?.setFieldValue('employeeApplyCommon', newVal);
    },
    [form, listEmployeeApplyCommon],
  );

  const handleSelectListPos = (fieldName: string) => (values: TSelectDropdown[]) => {
    form.setFieldsValue({
      [fieldName]:
        values?.map(value => ({
          name: value?.name,
          id: value?.id,
          code: value?.code,
        })) || [],
    });
    switch (fieldName) {
      case 'listSelfCreatedVoucher':
        setListSelfCreatedVoucher(values);
        break;
      default:
        setListEmployeeApplyCommonPos(values);
    }

    setIsModified(true);
  };

  const handleSelectListEmployee = (values: TSelectDropdown[]) => {
    const newEmployeeApplyCommon =
      values?.map(value => ({
        phone: value?.option?.phone,
        email: value?.option?.email,
        pos: value?.option?.pos,
        name: value?.name,
        id: value?.id,
        code: value?.code,
      })) || [];
    form.setFieldsValue({
      employeeApplyCommon: newEmployeeApplyCommon,
    });
    setListEmployeeApplyCommon(newEmployeeApplyCommon);
    setIsModified(true);
  };

  const formattedDefaultPosValues = useMemo(() => {
    return defaultListEmployeeApplyCommonPos?.current
      ? (defaultListEmployeeApplyCommonPos?.current as TPos[])?.map((pos: TPos) => ({
          label: pos?.name ?? '',
          value: pos?.id ?? '',
          name: pos?.name,
          code: pos?.code || '',
          id: pos?.id,
        }))
      : [];
  }, []);

  const formattedDefaultEmployeeValues = useMemo(() => {
    return defaultListEmployeeApplyCommon?.current
      ? (defaultListEmployeeApplyCommon?.current as TBusinessPartner[])?.map((partner: TBusinessPartner) => ({
          label: partner?.name ?? '',
          value: partner?.id ?? '',
          name: partner?.name ?? '',
          code: partner?.code ?? '',
          id: partner?.id,
          phone: partner?.phone ?? '',
          email: partner?.email ?? '',
          pos: partner?.pos as unknown as string, // ép kiểu phù hợp với defaultvalue MultiSelect
        }))
      : [];
  }, []);

  const actionEmployeeApplyCommonColumns: TableColumnsType = useMemo(() => {
    return [
      ...employeeApplyCommonColumns,
      {
        dataIndex: 'action',
        align: 'center',
        width: '80px',
        render: (_, record: TBusinessPartner) => {
          return (
            <Text
              style={{ color: '#1677FF', cursor: 'pointer' }}
              onClick={() => handleRemove(record?.id)}
              disabled={disableField}
            >
              Xóa
            </Text>
          );
        },
      },
    ];
  }, [disableField, handleRemove]);

  return (
    <>
      <Col xs={24} md={24}>
        <Item
          name="listSelfCreatedVoucher"
          label="Đơn vị sử dụng"
          required
          rules={[{ required: true, message: 'Vui lòng chọn đơn vị sử dụng' }]}
        >
          <MultiSelectLazy
            placeholder="Chọn đơn vị"
            queryKey={['get-pos']}
            apiQuery={getOrgCharts}
            keysLabel={'name'}
            handleListSelect={handleSelectListPos('listSelfCreatedVoucher')}
            // enabled={!!enabled}
            keysTag={'name'}
            defaultValues={formattedDefaultPosValues}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item name={'confirmBeforeUseCommon'} label="Xác nhận e-voucher trước khi sử dụng">
          <Radio.Group
            options={[
              { label: 'Có', value: true },
              { label: 'Không', value: false },
            ]}
            defaultValue={true}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item name="employeeApplyCommonPos" label="Đơn vị xác nhận">
          <MultiSelectLazy
            placeholder="Chọn đơn vị"
            queryKey={['get-pos']}
            apiQuery={getOrgCharts}
            keysLabel={'name'}
            handleListSelect={handleSelectListPos('employeeApplyCommonPos')}
            // enabled={!!enabled}
            keysTag={'name'}
            defaultValues={formattedDefaultPosValues}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item
          name="employeeApplyCommon"
          label="Nhân viên xác nhận"
          rules={[{ required: true, message: 'Vui lòng chọn nhân viên' }]}
        >
          <MultiSelectLazy
            placeholder="Chọn nhân viên"
            queryKey={['get-employees', formCurrentListEmployeeApplyCommonPos]}
            apiQuery={getEmployees}
            keysLabel={'name'}
            handleListSelect={handleSelectListEmployee}
            // enabled={!!enabled}
            keysTag={'name'}
            moreParams={{ orgcharts: formCurrentListEmployeeApplyCommonPos?.map((o: TPos) => o?.id)?.join(',') }}
            defaultValues={formattedDefaultEmployeeValues}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={12} style={{ marginBottom: '16px' }}>
        <Row gutter={{ md: 24, lg: 10 }}>
          <Col xs={24} md={16}>
            <Button
              icon={<DeleteOutlined />}
              onClick={() => {
                setListEmployeeApplyCommon([]);
                form?.setFieldValue('employeeApplyCommon', []);
              }}
              disabled={disableField}
            >
              Xóa tất cả
            </Button>
          </Col>
        </Row>
      </Col>
      <Col xs={24} md={24} style={{ marginBottom: '16px' }}>
        <Table
          className=""
          dataSource={formCurrentListEmployeeApplyCommon}
          columns={actionEmployeeApplyCommonColumns}
          pagination={false}
        />
      </Col>
    </>
  );
};

export default EmployeeApplyCommon;

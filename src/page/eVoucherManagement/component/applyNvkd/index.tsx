import { Button, Col, Form, Input, Row, Table, TableColumnsType, Typography } from 'antd';
import { useCallback, useMemo, useRef, useState } from 'react';
import { TBusinessPartner, TPos, TSelectDropdown } from '../../../../types/eVoucher';
import { DeleteOutlined } from '@ant-design/icons';
import { applyNvkdColumns } from '../columns';
import { useEVoucherStore } from '../../stroreEVoucher';
import { getEmployees, getOrgCharts } from '../../../../service/evoucher';
import MultiSelectLazy from '../../../../components/select/mutilSelectLazy';
import { handleKeyDownAmountInterger } from '../../utils';

const { Item } = Form;
const { Text } = Typography;

const ApplyNvkd: React.FC = () => {
  const form = Form.useFormInstance();

  const amountApplyNvkd = Form.useWatch('applyNvkdAmount', form);
  const formCurrentListApplyNvkd = Form.useWatch('applyNvkd', form);
  const formCurrentListApplyNvkdPos = Form.useWatch('applyNvkdPos', form);

  const { initialValue, disabled: disableField, setIsModified } = useEVoucherStore();
  const defaultListApplyNvkd = useRef(initialValue?.applyNvkd);
  const defaultListApplyNvkdPos = useRef(initialValue?.applyNvkd?.map(item => item?.pos));

  const [, setListApplyNvkdPos] = useState<TPos[]>([]);
  const [listApplyNvkd, setListApplyNvkd] = useState<TBusinessPartner[]>([]);

  const handleRemove = useCallback(
    (value: string) => {
      const newVal = listApplyNvkd.filter(v => v?.id !== value);
      setListApplyNvkd(newVal);
      form?.setFieldValue('applyNvkd', newVal);
    },
    [form, listApplyNvkd],
  );

  const handleSelectListPos = (values: TSelectDropdown[]) => {
    form.setFieldsValue({
      applyNvkdPos:
        values?.map(value => ({
          name: value?.name,
          id: value?.id,
          code: value?.code,
        })) || [],
    });
    setListApplyNvkdPos(values);
    setIsModified(true);
  };

  const handleSelectListEmployee = (values: TSelectDropdown[]) => {
    const newApplyNvkd =
      values?.map(value => ({
        phone: value?.option?.phone,
        email: value?.option?.email,
        pos: value?.option?.pos,
        name: value?.name,
        id: value?.id,
        code: value?.code,
      })) || [];
    form.setFieldsValue({
      applyNvkd: newApplyNvkd,
    });
    setListApplyNvkd(newApplyNvkd);
    setIsModified(true);
  };

  const formattedDefaultPosValues = useMemo(() => {
    return defaultListApplyNvkdPos?.current
      ? (defaultListApplyNvkdPos?.current as TPos[])?.map((pos: TPos) => ({
          label: pos?.name ?? '',
          value: pos?.id ?? '',
          name: pos?.name,
          code: pos?.code || '',
          id: pos?.id,
        }))
      : [];
  }, []);

  const formattedDefaultEmployeeValues = useMemo(() => {
    return defaultListApplyNvkd?.current
      ? (defaultListApplyNvkd?.current as TBusinessPartner[])?.map((partner: TBusinessPartner) => ({
          label: partner?.name ?? '',
          value: partner?.id ?? '',
          name: partner?.name ?? '',
          code: partner?.code ?? '',
          id: partner?.id,
          phone: partner?.phone ?? '',
          email: partner?.email ?? '',
          pos: partner?.pos as unknown as string, // ép kiểu phù hợp với defaultvalue MultiSelect
        }))
      : [];
  }, []);

  const actionApplyNvkdColumns: TableColumnsType = useMemo(() => {
    return [
      ...applyNvkdColumns,
      {
        title: 'Số lượng',
        width: '100px',
        dataIndex: 'amount',
        key: 'amount',
        render: () => {
          return <Text>{amountApplyNvkd || '-'}</Text>;
        },
      },
      {
        dataIndex: 'action',
        align: 'center',
        width: '80px',
        render: (_, record: TBusinessPartner) => {
          return (
            <Button
              style={{ background: 'none', border: 'none' }}
              onClick={() => handleRemove(record?.id)}
              disabled={disableField}
            >
              <Text style={{ color: '#1677FF' }}>Xóa</Text>
            </Button>
          );
        },
      },
    ];
  }, [amountApplyNvkd, disableField, handleRemove]);

  return (
    <>
      <Col xs={24} md={24}>
        <Item name="applyNvkdPos" label="Đơn vị bán hàng">
          <MultiSelectLazy
            placeholder="Chọn đơn vị"
            queryKey={['get-pos']}
            apiQuery={getOrgCharts}
            keysLabel={'name'}
            handleListSelect={handleSelectListPos}
            // enabled={!!enabled}
            keysTag={'name'}
            disabled={disableField}
            defaultValues={formattedDefaultPosValues}
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item
          name="applyNvkd"
          label="Nhân viên kinh doanh"
          rules={[{ required: true, message: 'Vui lòng chọn nhân viên' }]}
        >
          <MultiSelectLazy
            placeholder="Chọn nhân viên"
            queryKey={['get-employees', formCurrentListApplyNvkdPos]}
            apiQuery={getEmployees}
            keysLabel={'name'}
            handleListSelect={handleSelectListEmployee}
            // enabled={!!enabled}
            keysTag={'name'}
            moreParams={{ orgcharts: formCurrentListApplyNvkdPos?.map((o: TPos) => o?.id)?.join(',') }}
            disabled={disableField}
            defaultValues={formattedDefaultEmployeeValues}
          />
        </Item>
      </Col>
      <Col xs={24} md={24}>
        <Item
          name={'applyNvkdAmount'}
          label="Số lượng"
          required
          rules={[
            ({ getFieldValue }) => ({
              validator: async (_, value) => {
                const amount = getFieldValue('amount');

                if (amount && Number(value) > Number(amount)) {
                  return Promise.reject('Vui lòng nhập số lượng nhỏ hơn hoặc bằng số lượng phát hành');
                }
                if (!value || value.toString().trim() === '') {
                  return Promise.reject(new Error('Vui lòng nhập số lượng'));
                }
                return Promise.resolve();
              },
              validateTrigger: ['onChange'],
            }),
          ]}
        >
          <Input
            onKeyDown={handleKeyDownAmountInterger}
            placeholder="Nhập số lượng"
            maxLength={13}
            disabled={disableField}
          />
        </Item>
      </Col>
      <Col xs={24} md={12} style={{ marginBottom: '16px' }}>
        <Row gutter={{ md: 24, lg: 10 }}>
          <Col xs={24} md={16}>
            <Button
              icon={<DeleteOutlined />}
              onClick={() => {
                setListApplyNvkd([]);
                form?.setFieldValue('applyNvkd', []);
              }}
              disabled={disableField}
            >
              Xóa tất cả
            </Button>
          </Col>
        </Row>
      </Col>
      <Col xs={24} md={24} style={{ marginBottom: '16px' }}>
        <Table className="" dataSource={formCurrentListApplyNvkd} columns={actionApplyNvkdColumns} pagination={false} />
      </Col>
    </>
  );
};

export default ApplyNvkd;

import { create } from 'zustand';
import { IEVoucher } from '../../types/eVoucher';

interface IEVoucherStore {
  filters: Record<string, unknown>;
  setFilter: (filter: Record<string, unknown>) => void;
  disabled: boolean;
  initialValue: IEVoucher | undefined;
  isModified: boolean;
  setDisabled: (value: boolean) => void;
  setInitialValue: (value?: IEVoucher) => void;
  setIsModified: (value: boolean) => void;
  getCurrentFilter: () => Record<string, string>;
}

export const useEVoucherStore = create<IEVoucherStore>((set, get) => ({
  filters: {},
  setFilter: filter =>
    set({
      filters: {
        // ...state.filters,
        ...filter,
      },
    }),
  disabled: false,
  initialValue: undefined,
  isModified: false,
  setDisabled: value => set({ disabled: value }),
  setInitialValue: value => set({ initialValue: value }),
  setIsModified: value => set({ isModified: value }),
  getCurrentFilter: () => {
    const state = get();
    const filter = state.filters || {};
    const rawFilter = typeof filter === 'object' ? { ...filter } : {};
    return Object.entries(rawFilter)
      .filter(([, value]) => Boolean(value))
      .reduce(
        (acc, [key, value]) => {
          acc[key] = String(value);
          return acc;
        },
        {} as Record<string, string>,
      );
  },
}));

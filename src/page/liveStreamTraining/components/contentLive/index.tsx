import { EyeOutlined, MoreOutlined, PhoneOutlined } from '@ant-design/icons';
import { Button, Col, Flex, Result, Row, Space, Tabs, TabsProps, Typography } from 'antd';
import { useState } from 'react';
import * as uuid from 'uuid';
import angryIcon from '../../../../assets/images/emoji_react/emoji-angry.svg';
import careIcon from '../../../../assets/images/emoji_react/emoji-care.svg';
import laughIcon from '../../../../assets/images/emoji_react/emoji-laugh.svg';
import likeIcon from '../../../../assets/images/emoji_react/emoji-like.svg';
import loveIcon from '../../../../assets/images/emoji_react/emoji-love.svg';
import sadIcon from '../../../../assets/images/emoji_react/emoji-sad.svg';
import surpriseIcon from '../../../../assets/images/emoji_react/emoji-surprise.svg';
import { useCreateField, useFetch } from '../../../../hooks';
import { getListComments, getWatchLiveStream, postReactLiveStream } from '../../../../service/liveStream';
import { IComment, TGetDataReact, TGetUserWatchLive } from '../../../../types/liveStream';
import { changeViewCount } from '../../../../utilities/shareFunc';
import { useStoreLiveStreamRoom } from '../../storeLiveStreamRoom';
import CommentSection from './components/commentSection';
import ParticipantList from './components/ParticipantList';
import WinnerList from './components/WinnerList';
import './style.scss';

const { Title } = Typography;

const reactions: TGetDataReact[] = [
  {
    label: 'Like',
    icon: likeIcon,
    value: 'LIKE',
  },
  {
    label: 'Love',
    icon: loveIcon,
    value: 'LOVE',
  },
  {
    label: 'Care',
    icon: careIcon,
    value: 'CARE',
  },
  {
    label: 'Angry',
    icon: angryIcon,
    value: 'ANGRY',
  },
  {
    label: 'Haha',
    icon: laughIcon,
    value: 'HAHA',
  },
  {
    label: 'Sad',
    icon: sadIcon,
    value: 'SAD',
  },
  {
    label: 'Wow',
    icon: surpriseIcon,
    value: 'WOW',
  },
];

const ContentLive = () => {
  const { initialDataEvent, dataAccount, isAdmin } = useStoreLiveStreamRoom();

  const liveBanner = initialDataEvent?.prizeBackgroundLive;
  const srcLiveUrl = initialDataEvent?.livestreamUrl;
  console.log('srcLiveUrl :', srcLiveUrl);
  const countView = (initialDataEvent?.fLive || 0) + (initialDataEvent?.countGuest || 0);
  const eventId = initialDataEvent?.id;
  const { data: dataComments } = useFetch<IComment[]>({
    api: getListComments,
    queryKeyArr: ['get-list-comments', eventId],
    withFilter: false,
    moreParams: {
      eventId: eventId,
    },
    enabled: !!eventId && !!dataAccount,
  });
  const countComment = dataComments?.data?.data?.length || 0;

  const { data: watchLive } = useFetch<TGetUserWatchLive[]>({
    api: () => getWatchLiveStream(eventId),
    queryKeyArr: ['get-user-watch-live', eventId],
    enabled: !!eventId,
  });
  const dataUserWatchLive = watchLive?.data?.data?.rows;
  const countConnect = dataUserWatchLive?.filter(user => user.raiseHand === true)?.length;

  const sendReaction = useCreateField({
    apiQuery: postReactLiveStream,
    isMessageSuccess: false,
    isMessageError: false,
  });
  const items: TabsProps['items'] = [
    !initialDataEvent?.hideChat && {
      label: `Bình luận (${countComment})`,
      key: 'comments',
      children: <CommentSection />,
    },
    {
      label: 'Danh sách trúng thưởng',
      key: 'winners',
      children: <WinnerList />,
    },
    {
      label: `Danh sách người tham dự (${countView + (dataUserWatchLive?.length || 0)})`,

      key: 'participants',
      children: <ParticipantList keyPanel="participants" />,
    },
    initialDataEvent?.allowRequestDemand &&
      isAdmin && {
        label: `Kết nối từ khách hàng ${countConnect ? `(${countConnect})` : ''}`,
        key: 'connect',
        children: <ParticipantList keyPanel="connect" />,
      },
  ].filter(Boolean) as TabsProps['items'];
  const [floatingReactions, setFloatingReactions] = useState<(TGetDataReact & { id: string; x: number })[]>([]);

  const handleReactionClick = async (item: TGetDataReact) => {
    const id = uuid.v4();
    const newData = {
      eventId: eventId,
      type: item.value,
      userCheckinCode: !isAdmin ? dataAccount?.checkInCode : undefined,
      userEmail: !isAdmin ? dataAccount?.email : undefined,
    };

    try {
      const res = await sendReaction.mutateAsync(newData);
      if (res?.data?.statusCode === '0') {
        console.error('Error sending reaction:', res?.data?.message);
        setFloatingReactions(prev => [...prev, { ...item, id, x: Math.random() * 100 }]);

        setTimeout(() => {
          setFloatingReactions(prev => prev.filter(item => item.id !== id));
        }, 2000);
        return;
      }
    } catch (error) {
      console.error('Error sending reaction:', error);
      return;
    }
  };
  return (
    <div className="wrapper-content-live">
      <Row className="wrapper-content-live-row" gutter={{ md: 35, xs: 16 }}>
        {/* Video + Controls */}
        <Col lg={18} md={16} xs={24} style={{ height: '100%' }}>
          <Title level={4}>{initialDataEvent?.name}</Title>
          {srcLiveUrl && /^https?:\/\/.+/.test(srcLiveUrl) ? (
            <div className="video-container">
              <iframe
                width="100%"
                height="100%"
                src={srcLiveUrl}
                style={{ border: 0, objectFit: 'contain' }}
                allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
                referrerPolicy="strict-origin-when-cross-origin"
                allowFullScreen
              ></iframe>

              <div className="view-count">
                <EyeOutlined /> {changeViewCount(countView)}
              </div>
              {!initialDataEvent?.hideReaction && (
                <>
                  <div className="reaction-bar">
                    <Space size="small">
                      {reactions.map(reaction => (
                        <img
                          src={reaction.icon}
                          key={reaction.value}
                          alt={reaction.label}
                          className="reaction-icon"
                          onClick={() => handleReactionClick(reaction)}
                        />
                      ))}
                    </Space>
                  </div>

                  {floatingReactions?.map(reaction => (
                    <img
                      key={reaction?.id}
                      src={reaction?.icon}
                      className="floating-icon"
                      style={{ left: `${reaction?.x}%`, bottom: '20px' }}
                    />
                  ))}
                </>
              )}
            </div>
          ) : (
            <Result status="warning" title="Không có đường dẫn livestream hợp lệ." />
          )}
        </Col>

        <Col lg={6} md={8} xs={24} style={{ height: '100%' }}>
          <Flex vertical gap={16} className={`wrapper-panel ${liveBanner && 'banner'}`} style={{ height: '100%' }}>
            {liveBanner && (
              <img
                className="banner-live"
                src={`${import.meta.env.VITE_S3_IMAGE_URL}/${liveBanner}`}
                alt="banner live"
                height={150}
                width={'100%'}
              />
            )}
            <Tabs items={items} more={{ icon: <MoreOutlined /> }} />
          </Flex>
        </Col>
      </Row>
      <div className="footer">
        <Button type="primary" icon={<PhoneOutlined />}>
          {initialDataEvent?.hotline}
        </Button>
      </div>
    </div>
  );
};

export default ContentLive;

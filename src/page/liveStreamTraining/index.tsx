import { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { useFetch, useUpdateField } from '../../hooks';
import { getDetailOfTrainingLive, getInformationAttendees, patchCheckInCode } from '../../service/liveStream';
import { TAttendeeCheckInInfo, TGetDataCheckIn, TSubmitCheckIn } from '../../types/liveStream';
import { TTraining } from '../../types/training/training';
import CheckInModal from './components/CheckInModal';
import ContentLive from './components/contentLive';
import HeaderLiveRoom from './components/header';
import ModalInformation from './components/ModalInformation';
import { useStoreLiveStreamRoom } from './storeLiveStreamRoom';
import './style.scss';

const LiveStreamTraining = () => {
  const { id } = useParams();
  const [open, setOpen] = useState(true);
  const [idAttendees, setIdAttendees] = useState('');
  const { setInitialDataEvent, setDataAccount, setIsAdmin, dataAccount, setOpenModalInformation } =
    useStoreLiveStreamRoom();
  const dataAccountString = sessionStorage.getItem('dataAccount');
  const getIsAdminInLive = sessionStorage.getItem('isAdminInLive');

  const { data: detailEvent } = useFetch<TTraining>({
    api: () => getDetailOfTrainingLive(id),
    queryKeyArr: ['detail-of-training', id],
  });
  const dataEvent = detailEvent?.data?.data;
  const eventId = dataEvent?.id;
  // const codeToken = sessionStorage.getItem('codeToken');

  const { data: dataAttendees } = useFetch<TAttendeeCheckInInfo>({
    api: () => getInformationAttendees(idAttendees || dataAccount?.id),
    queryKeyArr: ['get-information-attendees', idAttendees || dataAccount?.id],
    enabled: !!idAttendees || (!!dataAccount?.id && getIsAdminInLive !== 'true'),
  });
  const dataAttendeesInfo = dataAttendees?.data?.data;

  const checkInCode = useUpdateField({
    apiQuery: patchCheckInCode,
    keyOfDetailQuery: ['get-user-watch-live', eventId],
    isMessageSuccess: false,
    isMessageError: false,
  });

  useEffect(() => {
    if (dataEvent) {
      setInitialDataEvent(dataEvent);
    }
  }, [dataEvent, setInitialDataEvent]);

  useEffect(() => {
    const listAdmin = dataEvent?.listAdmin || [];
    const dataAccount = dataAccountString ? JSON.parse(dataAccountString) : null;
    const isAdmin = listAdmin?.some(admin => admin.id === dataAccount?.id);

    if (dataAccountString) {
      setDataAccount(dataAccount);
    }
    if (isAdmin) {
      setIsAdmin(true);
      sessionStorage.setItem('isAdminInLive', String(isAdmin));
    }
  }, [dataAccountString, dataEvent?.listAdmin, setDataAccount, setIsAdmin]);

  useEffect(() => {
    if (dataAttendeesInfo) {
      setDataAccount(dataAttendeesInfo);
      sessionStorage.setItem('dataAccount', JSON.stringify(dataAttendeesInfo));
    }
  }, [dataAttendeesInfo, setDataAccount]);

  const handleSubmitCheckIn = async (values: TSubmitCheckIn) => {
    const res = await checkInCode.mutateAsync(values);
    const data = res?.data?.data as TGetDataCheckIn;
    if (res?.data?.statusCode === '0') {
      const attendeeId = data?.id || '';
      const checkInCode = data?.checkInCode || '';
      const emailToken = data?.email || '';

      setIdAttendees(attendeeId);
      sessionStorage.setItem('codeToken', checkInCode);
      sessionStorage.setItem('userId', attendeeId);
      sessionStorage.setItem('emailToken', emailToken);
      setOpenModalInformation(true);
      setOpen(false);
    }
  };

  return (
    <div className="root-live-room">
      <HeaderLiveRoom />
      <ModalInformation />
      <ContentLive />

      {!dataAccount?.id && <CheckInModal open={open} onSubmit={handleSubmitCheckIn} />}
    </div>
  );
};

export default LiveStreamTraining;

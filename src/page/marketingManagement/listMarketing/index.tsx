import { But<PERSON>, Flex } from 'antd';
import BreadCrumbComponent from '../../../components/breadCrumb';
import TableComponent from '../../../components/table';
import { useFetch } from '../../../hooks';
import FilterSearch from './components/filterSearch';
import './styles.scss';
import { columns } from './columns';
import { useStoreMarketing } from '../store';
import { deleteMarketing, getListMarketing } from '../../../service/marketing';
import { IMarketing } from '../../../types/marketing';
import { ActionsColumns } from '../../../components/table/components/ActionsColumns';
import { MARKETING } from '../../../configs/path';
import { ColumnsType } from 'antd/lib/table';
import ConfirmDeleteModal from '../../../components/modal/specials/ConfirmDeleteModal';
import { MutationFunction } from '@tanstack/react-query';
import { useState } from 'react';
import CreateModalMarketing from '../createMarketing';

function ListMarketing() {
  const { getCurrentFilter } = useStoreMarketing();

  const [currentRecord, setCurrentRecord] = useState<IMarketing>();
  const [isOpenModalDelete, setIsOpenModalDelete] = useState<boolean>(false);
  const [isModalVisible, setIsModalVisible] = useState<boolean>(false);

  const toggleModalCreate = () => setIsModalVisible(!isModalVisible);

  const {
    data: listMarketing,
    isLoading,
    isPlaceholderData,
    isFetching,
  } = useFetch<IMarketing[]>({
    queryKeyArrWithFilter: ['get-list-marketing', getCurrentFilter()],
    api: getListMarketing,
    moreParams: {
      ...(getCurrentFilter() as Record<string, string | undefined>),
    },
  });
  const actionColumns: ColumnsType<IMarketing> = [
    ...columns,
    {
      dataIndex: 'action',
      key: 'action',
      width: '50px',
      align: 'center',
      fixed: 'right',
      render: (_, record) => {
        const openViewDetail = () => {
          window.open(`${MARKETING}/${record?.id}`, '_blank', 'noopener,noreferrer');
        };

        const handleDeleteMarketing = () => {
          setIsOpenModalDelete(true);
          setCurrentRecord(record);
        };

        return (
          <ActionsColumns
            overlayClassName="action-column-data-role"
            handleViewDetail={openViewDetail}
            handleDelete={handleDeleteMarketing}
          />
        );
      },
    },
  ];

  return (
    <div className="wrapper-list-marketing">
      <BreadCrumbComponent />
      <div className="header-content">
        <Flex gap={17}>
          <FilterSearch />
        </Flex>
        <Flex gap={10}>
          <Button type="primary" onClick={toggleModalCreate}>
            Thêm mới
          </Button>
        </Flex>
      </div>
      <div className="table-marketing">
        <TableComponent
          queryKeyArr={['get-list-marketing', getCurrentFilter()]}
          columns={actionColumns}
          loading={isLoading || isPlaceholderData || isFetching}
          dataSource={listMarketing?.data?.data?.rows ?? []}
          rowKey="id"
        />
      </div>
      <ConfirmDeleteModal
        label="kế hoạch"
        fieldNameReason="reasonDelete"
        open={isOpenModalDelete}
        apiQuery={deleteMarketing as MutationFunction<unknown, unknown>}
        keyOfListQuery={['list-of-commission-debt-penalty', getCurrentFilter()]}
        onCancel={() => setIsOpenModalDelete(false)}
        idDetail={currentRecord?.id}
        title="xóa kế hoạch"
        description="Vui lòng nhập lý do xoá kế hoạch"
      />
      <CreateModalMarketing keyQuery={['get-list-marketing']} visible={isModalVisible} onClose={toggleModalCreate} />
    </div>
  );
}

export default ListMarketing;

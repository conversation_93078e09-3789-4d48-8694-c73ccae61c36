import { Button, Col, Flex, Form, FormInstance, Row, Space, Tag, Typography } from 'antd';
import './styled.scss';
import CategoryTable from './CategoryTable';
import { getLabelHandleStatus } from '../../../../utils';
import { TAttachment, TDetailHandoverProduct } from '../../../../../../types/delivery';
import { v4 as uuidv4 } from 'uuid';
import { useEffect, useState } from 'react';
import { useUpdateField } from '../../../../../../hooks';
import { confirmDelivery } from '../../../../../../service/delivery';

const { Title } = Typography;

interface CommonFormProps {
  data?: TDetailHandoverProduct;
  onFormInstanceChange?: (formInstance: FormInstance) => void;
}

const PropertyHandoverTab: React.FC<CommonFormProps> = (props: CommonFormProps) => {
  const { data } = props;

  const [form] = Form.useForm();

  const [initialValues, setInitialValues] = useState<TDetailHandoverProduct>();

  const { mutateAsync: mutateConfirmDelivery } = useUpdateField({
    apiQuery: confirmDelivery,
    keyOfListQuery: ['get-detail-handover-unit'],
    keyOfDetailQuery: ['get-detail-handover-unit', data?.id],
  });

  useEffect(() => {
    if (data) {
      const initialData = {
        ...data,
      };
      form.setFieldsValue(initialData);
      setInitialValues(initialData as TDetailHandoverProduct);
    }
  }, [data, form]);

  const handleConfỉm = async (values: TDetailHandoverProduct) => {
    const payload = {
      id: data?.id,
      itemsForEligible: data?.cerHandoverStatus === 'Eligible' ? data?.deliveryItems : undefined,
      itemsCerInProcess: data?.cerHandoverStatus === 'Cer_in_process' ? data?.cerInProcessItems : undefined,
      itemsForCerReadyHandover:
        data?.cerHandoverStatus === 'Cer_ready_handover' ? data?.cerReadyHandoverItems : undefined,
      files: values?.files?.map(item => (item as TAttachment)?.key),
      cerHandoverStatus:
        data?.cerHandoverStatus === 'Eligible'
          ? 'Cer_in_process'
          : data?.cerHandoverStatus === 'Cer_in_process'
            ? 'Cer_ready_handover'
            : 'Cer_handed_over',
    };
    const response = await mutateConfirmDelivery(payload as TDetailHandoverProduct);
    if (response?.data?.statusCode === '0') {
      // console.log('response', response);
    }
  };

  return (
    <div className="wrapper-detail-personal-customer">
      {initialValues && (
        <Form
          form={form}
          layout="vertical"
          onFinish={handleConfỉm}
          // onValuesChange={validateForm}
          initialValues={initialValues}
          className="space-y-6"
        >
          <Row gutter={{ md: 24, lg: 40, xl: 80, xxl: 126 }}>
            <Col span={24}>
              <div className="header-content">
                <Flex gap={17}> </Flex>
                <Flex gap={10}>
                  <Button type="primary" onClick={() => form.submit()}>
                    Xác nhận
                  </Button>
                </Flex>
              </div>
            </Col>
            <Col xs={24} md={24}>
              <div className="general-information" style={{ marginBottom: '24px' }}>
                <p>Trạng thái</p>
                <span>
                  <Tag color="pink">{getLabelHandleStatus(data?.cerHandoverStatus as string)}</Tag>
                </span>
              </div>
            </Col>
            <Col xs={24} md={24}>
              <Space direction="vertical" size={10} style={{ width: '100%' }}>
                <div
                  className={
                    !['scheduled', 'Eligible']?.includes(data?.cerHandoverStatus as string)
                      ? 'session-disabled-wrapper'
                      : ''
                  }
                >
                  <div
                    className={
                      !['scheduled', 'Eligible']?.includes(data?.cerHandoverStatus as string)
                        ? 'session-disabled-content'
                        : ''
                    }
                  >
                    <Title level={5}>Sản phẩm đủ điều kiện làm sổ</Title>
                    <CategoryTable
                      dataSource={data?.eligibleItems?.map((item, index) => ({
                        ...item,
                        key: uuidv4(),
                        children: item?.list?.map(o => ({ ...o, id: uuidv4(), parentIndex: index })),
                      }))}
                      form={form}
                      fieldParent="eligibleItems"
                    />
                  </div>
                  {!['scheduled', 'Eligible']?.includes(data?.cerHandoverStatus as string) && (
                    <div className="session-overlay" />
                  )}
                </div>
                <div className={data?.cerHandoverStatus !== 'Cer_in_process' ? 'session-disabled-wrapper' : ''}>
                  <div className={data?.cerHandoverStatus !== 'Cer_in_process' ? 'session-disabled-content' : ''}>
                    <Title level={5}>Sản phẩm đang làm sổ</Title>
                    <CategoryTable
                      dataSource={data?.cerInProcessItems?.map((item, index) => ({
                        ...item,
                        key: uuidv4(),
                        children: item?.list?.map(o => ({ ...o, id: uuidv4(), parentIndex: index })),
                      }))}
                      form={form}
                      fieldParent="cerInProcessItems"
                    />
                  </div>
                  {data?.cerHandoverStatus !== 'Cer_in_process' && <div className="session-overlay" />}
                </div>

                <div className={data?.cerHandoverStatus !== 'Cer_ready_handover' ? 'session-disabled-wrapper' : ''}>
                  <div className={data?.cerHandoverStatus !== 'Cer_ready_handover' ? 'session-disabled-content' : ''}>
                    <Title level={5}>Sản phẩm đã có sổ - đợi bàn giao</Title>
                    <CategoryTable
                      dataSource={data?.cerReadyHandoverItems?.map((item, index) => ({
                        ...item,
                        key: uuidv4(),
                        children: item?.list?.map(o => ({ ...o, id: uuidv4(), parentIndex: index })),
                      }))}
                      form={form}
                      fieldParent="cerReadyHandoverItems"
                    />
                  </div>
                  {data?.cerHandoverStatus !== 'Cer_ready_handover' && <div className="session-overlay" />}
                </div>
              </Space>
            </Col>
          </Row>
        </Form>
      )}
    </div>
  );
};

export default PropertyHandoverTab;

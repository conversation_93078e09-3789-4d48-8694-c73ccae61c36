import { DownOutlined, LeftOutlined, RightOutlined } from '@ant-design/icons';
import { EventContentArg } from '@fullcalendar/core/index.js';
import FullCalendar from '@fullcalendar/react';
import { Badge, Button, DatePicker, Flex, Radio, Space, Typography } from 'antd';
import dayjs, { Dayjs } from 'dayjs';
import { useEffect, useRef, useState } from 'react';
import viLocale from '@fullcalendar/core/locales/vi';
import dayGridPlugin from '@fullcalendar/daygrid';
import timeGridPlugin from '@fullcalendar/timegrid';
import interactionPlugin from '@fullcalendar/interaction';
import { useFetch } from '../../../../hooks';
import { TCreateHandoverCalender, TGetDataCalenderHandover } from '../../../../types/delivery';
import { getListHandoverSchedule } from '../../../../service/delivery';
import './styled.scss';
import CreateModal from './CreateModal';
import { useProjectStore } from '../../store';
import { DeliveryScheduleModal } from './HandoverModal';

const { Text } = Typography;

const notes = [
  {
    color: 'red',
    text: 'Căn hộ bị từ chối lịch bàn giao căn hộ',
  },
  {
    color: 'blue',
    text: 'Căn hộ có yêu cầu thay đổi lịch bàn giao',
  },
  {
    color: 'yellow',
    text: 'Căn hộ chưa xác nhận lịch bàn giao',
  },
  {
    color: 'green',
    text: 'Căn hộ đã xác nhận lịch bàn giao',
  },
];

interface HandoverCalendarProps {
  toggleVisibleChangeStatusXD?: () => void;
}

const HandoverCalendar: React.FC<HandoverCalendarProps> = (props: HandoverCalendarProps) => {
  const { toggleVisibleChangeStatusXD } = props;
  const calendarRef = useRef<FullCalendar>(null);

  const { deliveryId } = useProjectStore();
  const [isOpenCreateModal, setIsOpenCreateModal] = useState<boolean>(false);
  const [isOpenUpdateModal, setIsOpenUpdateModal] = useState<boolean>(false);
  const [isOpenHandoverModal, setIsOpenHandoverModal] = useState<boolean>(false);

  const [currentHandoverCalenderDetail, setCurrentHandoverCalenderDetail] = useState<TCreateHandoverCalender>({});

  const toggleOpenCreateModal = () => setIsOpenCreateModal(!isOpenCreateModal);
  const toggleOpenUpdateModal = () => setIsOpenUpdateModal(!isOpenUpdateModal);
  const toggleOpenHandoverModal = () => setIsOpenHandoverModal(!isOpenHandoverModal);

  const { data: dataCalender } = useFetch<TGetDataCalenderHandover>({
    queryKeyArr: ['get-list-calender', deliveryId],
    api: getListHandoverSchedule,
    withFilter: false,
    moreParams: { deliveryId: deliveryId },
  });

  const calenderHandover = (dataCalender?.data?.data?.rows as unknown as TGetDataCalenderHandover[])?.map(item => ({
    title: `${dayjs(item?.handoverStartTime).format('HH:mm')} - ${dayjs(item?.handoverEndTime).format('HH:mm')} ${item?.handoverApartment?.primaryTransaction?.propertyUnit?.code}`,
    start: item?.handoverStartTime,
    end: item?.handoverEndTime,
    backgroundColor: item?.status === 'unconfirmed' ? '#FAAD14' : '',
    display: 'auto', // chỉ hiện nội dung, không có background
    className: 'fc-event-no-bg',
    id: item?.id,
  }));

  const [viewMode, setViewMode] = useState<'dayGridMonth' | 'timeGridWeek' | 'timeGridDay'>('dayGridMonth');
  const [currentDate, setCurrentDate] = useState<Dayjs>(dayjs());

  const handleGotoPrev = () => {
    calendarRef.current?.getApi().prev();
    setCurrentDate(dayjs(calendarRef.current?.getApi().getDate()));
  };
  const handleGotoNext = () => {
    calendarRef.current?.getApi().next();
    setCurrentDate(dayjs(calendarRef.current?.getApi().getDate()));
  };
  const handleToday = () => {
    calendarRef.current?.getApi().today();
    setCurrentDate(dayjs(calendarRef.current?.getApi().getDate()));
  };

  const handleDatePickerChange = (date: Dayjs | null) => {
    if (date) {
      setCurrentDate(date);
      calendarRef.current?.getApi().gotoDate(date.toDate());
    }
  };

  // const handleDatesSet = (arg: DatesSetArg) => {
  //   setCurrentDate(dayjs(arg.start));
  // };

  const renderEventContent = (arg: EventContentArg) => {
    const dotColor = arg.event.backgroundColor;

    // const startTime = dayjs(arg.event.start).format('HH:mm');
    const title = arg?.event?.title;

    return (
      <div className="fc-event-custom">
        <span
          className="dot"
          style={{
            backgroundColor: dotColor,
          }}
        />
        <span>{title}</span>
      </div>
    );
  };

  useEffect(() => {
    const calendarApi = calendarRef.current?.getApi();
    if (calendarApi) {
      calendarApi.changeView(viewMode);
    }
  }, [calendarRef, viewMode]);
  return (
    <>
      {/*Button Action*/}
      <div className="header-content">
        <Flex gap={0}> </Flex>
        <Flex gap={10}>
          <Button type="default" onClick={toggleOpenHandoverModal}>
            Bàn giao
          </Button>
          <Button type="default" onClick={toggleVisibleChangeStatusXD}>
            Điều chỉnh trạng thái XD
          </Button>
          <Button type="primary" onClick={toggleOpenCreateModal}>
            Tạo mới
          </Button>
        </Flex>
      </div>
      {/*Action with FullCalender*/}
      <div className="header-content">
        <Flex gap={0}>
          <Button
            icon={<LeftOutlined />}
            onClick={handleGotoPrev}
            style={{ border: 'none', padding: '0px !important' }}
          />
          <Button
            icon={<RightOutlined />}
            onClick={handleGotoNext}
            style={{ border: 'none', padding: '0px !important' }}
          />
          <DatePicker
            picker={viewMode === 'timeGridWeek' ? 'week' : viewMode === 'dayGridMonth' ? 'month' : 'date'}
            value={currentDate}
            onChange={handleDatePickerChange}
            format={(val: Dayjs | null) =>
              val
                ? viewMode === 'timeGridWeek'
                  ? `${val.startOf('week').format('DD')} - ${val.endOf('week').format('DD')}  Tháng ${val.endOf('week').format('M')}, ${val.endOf('week').format('YYYY')}`
                  : viewMode === 'dayGridMonth'
                    ? `Tháng ${val.format('M')} ${val.format('YYYY')}`
                    : `${val.format('DD')} Tháng ${val.format('M')} ${val.format('YYYY')}`
                : ''
            }
            allowClear={false}
            bordered={false}
            // locale={viLocale}
            className="custom-month-picker"
            suffixIcon={<DownOutlined />}
          />
          {/* <div style={{ fontWeight: 600, fontSize: 20 }}>{getCurrentViewLabel()}</div> */}
        </Flex>
        <Flex gap={10}>
          <div className="calendar-bottom-buttons">
            <Button onClick={handleToday}>Hôm nay</Button>
            <Radio.Group className="view-switcher" value={viewMode} onChange={e => setViewMode(e.target.value)}>
              <Radio.Button value="dayGridMonth">Tháng</Radio.Button>
              <Radio.Button value="timeGridWeek">Tuần</Radio.Button>
              <Radio.Button value="timeGridDay">Ngày</Radio.Button>
            </Radio.Group>
          </div>
        </Flex>
      </div>
      <div className="calender-handover">
        <FullCalendar
          ref={calendarRef}
          locale={viLocale}
          initialView={viewMode}
          headerToolbar={false}
          eventContent={renderEventContent}
          plugins={[dayGridPlugin, timeGridPlugin, interactionPlugin]}
          // datesSet={handleDatesSet}
          height="auto"
          slotMinTime="07:00:00"
          slotMaxTime="20:00:00"
          slotDuration="01:00:00"
          allDaySlot={false}
          eventClick={info => {
            setCurrentHandoverCalenderDetail(
              (dataCalender?.data?.data?.rows as unknown as TGetDataCalenderHandover[])?.filter(
                item => item?.id === info.event.id,
              )[0],
            );
            toggleOpenUpdateModal();
            // You can open a modal, navigate, etc.
          }}
          slotLabelFormat={{
            hour: 'numeric',
            minute: '2-digit',
            hour12: false, // or true for 12-hour format
            meridiem: 'narrow', // This helps show "SA" or "CH" if locale supports it
          }}
          slotLabelContent={arg => {
            //Change view lable to SA and CH
            const hour = arg.date.getHours();
            const suffix = hour < 12 ? 'SA' : 'CH';
            const display = `${(hour % 12 || 12).toString().padStart(2, '0')}${suffix}`;
            return { html: `<span>${display}</span>` };
          }}
          dayHeaderContent={args => {
            const date = args.date.getDate();
            const weekdayMap: Record<number, string> = {
              0: 'Chủ Nhật',
              1: 'Thứ Hai',
              2: 'Thứ Ba',
              3: 'Thứ Tư',
              4: 'Thứ Năm',
              5: 'Thứ Sáu',
              6: 'Thứ Bảy',
            };
            const day = args.date.getDay();
            return viewMode === 'dayGridMonth'
              ? weekdayMap[day]
              : viewMode === 'timeGridWeek'
                ? `${date} ${weekdayMap[day]}`
                : [];
          }}
          events={calenderHandover}
        />
      </div>

      {/*Ghi chú*/}
      <div style={{ padding: 16, background: '#fff' }}>
        <Text strong>Ghi chú:</Text>
        <Space direction="vertical" size="small" style={{ marginTop: 8, display: 'flex' }}>
          {notes.map(({ color, text }) => (
            <Badge key={text} color={color} text={<Text style={{ marginLeft: 4 }}>{text}</Text>} />
          ))}
        </Space>
      </div>
      <CreateModal isOpen={isOpenCreateModal} title="Tạo lịch bàn giao" onClose={toggleOpenCreateModal} />
      <CreateModal
        isOpen={isOpenUpdateModal}
        title="Chỉnh sửa lịch bàn giao"
        onClose={toggleOpenUpdateModal}
        currentRecord={currentHandoverCalenderDetail}
      />
      <DeliveryScheduleModal isOpen={isOpenHandoverModal} onCancel={toggleOpenHandoverModal} />
    </>
  );
};
export default HandoverCalendar;

import { <PERSON><PERSON>, <PERSON>, DatePicker, Form, Modal, Row, Select } from 'antd';
import { FrameItem, IDelivery, TCreateHandoverCalender, THandoverCalender } from '../../../../types/delivery';
import { useCallback, useEffect, useState } from 'react';
import { useCreateField, useFetch, useUpdateField } from '../../../../hooks';
import { FORMAT_DATE, FORMAT_DATE_TIME } from '../../../../constants/common';
import dayjs from 'dayjs';
import {
  createHandoverSchedule,
  getDetailDeliveryConfig,
  getListEmployee,
  getListHandoverByStatus,
  updateHandoverSchedule,
} from '../../../../service/delivery';
import { useProjectStore } from '../../store';
import { TEmployee } from '../../../../types/eVoucher';

type ModalSaveLeadSourceProps = {
  isOpen: boolean;
  title: string;
  currentRecord?: TCreateHandoverCalender;
  setCurrentRecord?: React.Dispatch<React.SetStateAction<TCreateHandoverCalender | undefined>>;
  onClose: () => void;
};

const OPTIONS_STATUS = [
  { label: 'Chưa xác nhận', value: 'unconfirmed' },
  { label: 'Yêu cầu thay đổi', value: 'requested' },
  { label: 'Đã xác nhận', value: 'confirmed' },
  { label: 'Từ chối', value: 'rejected' },
];

const CreateModal: React.FC<ModalSaveLeadSourceProps> = (props: ModalSaveLeadSourceProps) => {
  const { isOpen, title, currentRecord, setCurrentRecord, onClose } = props;
  const { deliveryId } = useProjectStore();
  const [form] = Form.useForm();

  const [initialValues, setInitialValues] = useState<THandoverCalender>();
  const handoverStartTime = Form.useWatch('handoverStartTime', form);
  const propertyUnitCode = Form.useWatch(['handoverApartment', 'primaryTransaction', 'propertyUnit', 'code'], form);

  const dayOfWeek = dayjs(handoverStartTime).locale('en').format('dddd');

  const { data: dataListHandoverByStatus } = useFetch<THandoverCalender[]>({
    queryKeyArrWithFilter: ['get-handover-by-status'],
    api: getListHandoverByStatus,
    moreParams: { id: deliveryId },
  });

  const listHandoverByStatus = dataListHandoverByStatus?.data?.data?.rows;

  //Object Căn hộ để check thuộc bàn giao sổ hay không
  const handoverApartmentCurrentSelected = listHandoverByStatus?.find(item => {
    const unitId = item?.primaryTransaction?.propertyUnit?.id;
    return unitId && unitId === propertyUnitCode;
  });

  const { data: dataListSupportEmployee } = useFetch<TEmployee[]>({
    queryKeyArrWithFilter: ['get-support-employee'],
    api: getListEmployee,
  });

  const listSupportEmployee = dataListSupportEmployee?.data?.data;

  const { data: dataDetailDeliveryConfig } = useFetch<IDelivery>({
    api: getDetailDeliveryConfig,
    queryKeyArr: ['get-detail-handover-unit', handoverStartTime],
    enabled: !!handoverStartTime,
    cacheTime: 10,
    moreParams: {
      deliveryId: deliveryId,
      handoverStartTime: dayjs(handoverStartTime).toISOString(),
      type: handoverApartmentCurrentSelected?.ownershipCertificateId ? 'ownershipCertificate' : 'handover',
      ownershipCertificateId: handoverApartmentCurrentSelected?.ownershipCertificateId,
    },
  });
  const detailDeliveryConfig = dataDetailDeliveryConfig?.data?.data;

  const { mutateAsync: mutateHandoverSchedule } = useCreateField<TCreateHandoverCalender>({
    apiQuery: createHandoverSchedule,
    keyOfListQuery: ['get-list-calender'],
    isMessageSuccess: true,
    isMessageError: false,
  });
  const { mutateAsync: mutateAsyncUpdateLeadSource } = useUpdateField({
    apiQuery: updateHandoverSchedule,
    keyOfListQuery: ['get-list-calender'],
    isMessageSuccess: true,
    isMessageError: false,
  });

  const handleCancelModalUpdate = useCallback(() => {
    setCurrentRecord && setCurrentRecord(undefined);
    onClose();
  }, [onClose, setCurrentRecord]);

  const handleCancelModalCreate = useCallback(() => {
    setCurrentRecord && setCurrentRecord(undefined);
    onClose();
    form.resetFields();
  }, [form, onClose, setCurrentRecord]);

  const handleSubmit = useCallback(
    async (values: TCreateHandoverCalender) => {
      const [start, end] =
        values?.handoverTime
          ?.split(/\s*-\s*/) // tách theo dấu “-” và loại bỏ khoảng trắng
          ?.map(str => str.trim()) ?? [];

      const supportEmployee = listSupportEmployee?.filter(item => item?.id === values?.supportEmployee);

      const combinedStart = `${dayjs(values?.handoverStartTime).format(FORMAT_DATE)} ${start}`;
      const combinedEnd = `${dayjs(values?.handoverStartTime).format(FORMAT_DATE)} ${end}`;

      const newData: TCreateHandoverCalender = {
        ...values,
        id: currentRecord?.id,
        handoverApartment: handoverApartmentCurrentSelected
          ? handoverApartmentCurrentSelected
          : currentRecord?.handoverApartment,
        supportEmployee: supportEmployee ? supportEmployee[0] : {},
        deliveryId: deliveryId,
        ownershipCertificateId: handoverApartmentCurrentSelected?.ownershipCertificateId,
        handoverEndTime: dayjs(combinedEnd, FORMAT_DATE_TIME).toISOString(),
        handoverStartTime: dayjs(combinedStart, FORMAT_DATE_TIME).toISOString(),
        status: 'unconfirmed',
      };

      const res = !currentRecord?.id
        ? await mutateHandoverSchedule(newData)
        : await mutateAsyncUpdateLeadSource(newData);
      if (res?.data?.statusCode === '0') {
        currentRecord?.id ? handleCancelModalUpdate() : handleCancelModalCreate();
      }
    },
    [
      currentRecord?.handoverApartment,
      currentRecord?.id,
      deliveryId,
      handleCancelModalCreate,
      handleCancelModalUpdate,
      handoverApartmentCurrentSelected,
      listSupportEmployee,
      mutateAsyncUpdateLeadSource,
      mutateHandoverSchedule,
    ],
  );

  useEffect(() => {
    if (form.isFieldsTouched(true) && isOpen) {
      const handleBeforeunload = (e: BeforeUnloadEvent) => {
        e.preventDefault();
      };

      window.addEventListener('beforeunload', handleBeforeunload);
      return () => {
        window.removeEventListener('beforeunload', handleBeforeunload);
      };
    }
  }, [form, isOpen]);

  //   const handleSelectEmployee = (value: string) => {
  //     form.setFieldsValue({ supportEmployee: value });
  //   };

  useEffect(() => {
    if (currentRecord) {
      form.setFieldValue('id', currentRecord?.id);
      form.setFieldsValue({
        ...currentRecord,
        handoverStartTime: dayjs.utc(currentRecord?.handoverStartTime).local(),
        supportEmployee: currentRecord?.supportEmployee?.id,
        handoverTime: `${dayjs.utc(currentRecord?.handoverStartTime).local().format('HH:mm')} - ${dayjs.utc(currentRecord?.handoverEndTime).local().format('HH:mm')}`,
      });
      setInitialValues(currentRecord);
    }
  }, [currentRecord, form]);
  return (
    <>
      <Modal
        className="modal-share-care"
        open={isOpen}
        title={title}
        width={380}
        onCancel={onClose}
        confirmLoading
        maskClosable={false}
        footer={
          <>
            <Button type="default" onClick={onClose}>
              Hủy
            </Button>
            <Button type="primary" onClick={() => form.submit()}>
              Đặt lịch hẹn
            </Button>
          </>
        }
      >
        <Form
          form={form}
          layout="vertical"
          onFinish={handleSubmit}
          //   onValuesChange={validateForm}
          initialValues={initialValues ? initialValues : undefined}
          className="space-y-6"
        >
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item
                name={['handoverApartment', 'primaryTransaction', 'propertyUnit', 'code']}
                label="Căn hộ bàn giao"
                rules={[{ required: true, message: 'Vui lòng nhập tên căn hộ bàn giao' }]}
              >
                <Select
                  placeholder="Chọn căn hộ"
                  allowClear
                  filterOption={(input, option) =>
                    typeof option?.label === 'string' ? option.label.toLowerCase().includes(input.toLowerCase()) : false
                  }
                  showSearch
                  style={{ width: '100%' }}
                  //   loading={isLoading}
                  options={listHandoverByStatus?.map(item => ({
                    value: item?.primaryTransaction?.propertyUnit?.id,
                    label: item?.primaryTransaction?.propertyUnit?.code,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name={'handoverStartTime'}
                label="Ngày bàn giao"
                rules={[{ required: true, message: 'Vui lòng chọn ngày bàn giao' }]}
              >
                <DatePicker
                  format={FORMAT_DATE}
                  disabledDate={current => {
                    return current && current < dayjs().startOf('day');
                  }}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                label="Nhân viên hỗ trợ"
                required
                name={['supportEmployee']}
                rules={[{ required: true, message: 'Vui lòng chọn nhân viên hỗ trợ' }]}
              >
                <Select
                  placeholder="Chọn nhân viên"
                  allowClear
                  showSearch
                  style={{ width: '100%' }}
                  //   onChange={handleSelectEmployee}
                  //   loading={isLoading}
                  options={listSupportEmployee?.map(item => ({
                    value: item.id,
                    label: item?.name,
                  }))}
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item
                name={'handoverTime'}
                label="Thời gian bàn giao"
                rules={[{ required: true, message: 'Vui lòng chọn thời gian bàn giao' }]}
              >
                <Select
                  placeholder="Chọn khung giờ bàn giao"
                  style={{ width: '100%' }}
                  options={
                    detailDeliveryConfig
                      ? (
                          detailDeliveryConfig[
                            `${dayOfWeek.charAt(0).toLowerCase() + dayOfWeek.slice(1)}Frame`
                          ] as FrameItem[]
                        )?.map(item => ({
                          ...item,
                          value: `${item?.startTime} - ${item?.endTime}`,
                          label: `${item?.startTime} - ${item?.endTime}`,
                        }))
                      : []
                  }
                />
              </Form.Item>
            </Col>
            <Col span={24}>
              <Form.Item label="Trạng thái">
                <Select
                  placeholder="Chọn trạng thái"
                  style={{ width: '100%' }}
                  disabled
                  defaultValue={'unconfirmed'}
                  //   loading={isLoading}
                  options={OPTIONS_STATUS}
                />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};
export default CreateModal;

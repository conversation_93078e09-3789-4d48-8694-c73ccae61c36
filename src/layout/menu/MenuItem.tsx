import {
  CodeSandboxOutlined,
  CustomerServiceOutlined,
  EditOutlined,
  FileSyncOutlined,
  GiftOutlined,
  <PERSON><PERSON>ollectOutlined,
  SolutionOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  ASSIGN_LEAD,
  COMMISSION,
  COMMISSION_HISTORY_IMPORT,
  COMMISSION_PERIOD_DEFAULT,
  COMMISSION_PERIOD_LIST,
  COMMISSION_POLICY,
  CUSTOMER_BUSINESS,
  CUSTOMER_PERSONAL,
  DELIVERY_CONFIGURATION,
  DISCOUNT_POLICY,
  EMPLOYEE_EXTERNAL_MANAGEMENT,
  EMPLOYEE_INTERNAL_MANAGEMENT,
  HISTORY_CUSTOMER,
  IMPORT_HISTORY_PROJECT_MANAGEMENT,
  INDICATOR,
  INVESTORS_MANAGEMENT,
  LEAD,
  LEAD_ASSIGNED,
  LEAD_CONFIG,
  LEAD_DASHBOARD,
  LEAD_HISTORY_IMPORT,
  LEAD_REPORT,
  LEAD_SOURCE,
  OFFICIAL_CUSTOMER_BUSINESS,
  OFFICIAL_CUSTOMER_PERSONAL,
  PAYMENT_POLICY,
  PROJECTS_MANAGEMENT,
  PROPOSAL,
  SALES_POLICY,
  SELL_PROGRAM_MANAGEMENT,
  UNIT_MANAGEMENT,
  UNIT_PARTNER_MANAGEMENT,
  USER_ROLES_MANAGEMENT,
  E_VOUCHER,
  OFFER_COLLECT_MONEY_MANAGEMENT,
  OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT,
  OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT,
  TRAINING_USER_MANAGEMENT,
  TRAINING_USER_CREATE_MANAGEMENT,
  TRAINING,
  DEPOSIT_CONTRACT_MANAGEMENT,
  E_VOUCHER_LIST,
  E_VOUCHER_HISTORY,
  PURCHASE_CONTRACT_MANAGEMENT,
  DELIVERY,
  COMMISSION_DEBT_POLICY,
  OFFER_REFUND_MONEY_PAYMENT_MANAGEMENT,
  TRANSFER_CONTRACT_MANAGEMENT,
  PROPERTY_ATTRIBUTES_MANAGEMENT,
  DEBT_COMMISSION_PERIOD,
  DEPOSIT_CONTRACT,
  OWNERSHIP_CERTIFICATE_CONFIGURATION,
  LIQUIDATION_PROPOSAL_MANAGEMENT,
  LIQUIDATION_CONTRACT_MANAGEMENT,
  PRICE_COEFFICIENT,
  COMMISSION_DEBT_PENALTY,
  DEBT_REPORT,
  PERSONAL_COMMISSION,
  MARKETING,
} from '../../configs/path';

// Define types for menu items and submenus
export interface MenuItem {
  key: string;
  label: string;
  path?: string;
  children?: MenuItem[];
}

export interface MenuGroup {
  key: string;
  label: string;
  children: MenuItem[];
}

export interface SubMenuData {
  key: string;
  label: string;
  icon?: React.ReactNode;
  children: MenuItem[];
  groups: MenuGroup[];
  path?: string;
}

// Interface for groups within a submenu

export const menuItems: SubMenuData[] = [
  {
    key: 'sub1',
    label: 'Phân quyền và người dùng',
    icon: <UserOutlined />,
    children: [
      // {
      //   key: USER_ADMIN_MANAGEMENT,
      //   label: 'Danh sách người dùng Admin',
      //   path: USER_ADMIN_MANAGEMENT,
      // },
    ],
    groups: [
      { key: 'group1', label: 'Sơ đồ tuỳ biến', children: [] },
      {
        key: 'group2',
        label: 'Quản lý đơn và vị đối tác',
        children: [
          {
            key: UNIT_MANAGEMENT,
            label: 'Danh sách đơn vị',
            path: UNIT_MANAGEMENT,
          },
          {
            key: UNIT_PARTNER_MANAGEMENT,
            label: 'Danh sách đối tác hợp tác',
            path: UNIT_PARTNER_MANAGEMENT,
          },
          {
            key: DEPOSIT_CONTRACT,
            label: 'Hợp đồng ký quỹ',
            path: DEPOSIT_CONTRACT,
          },
        ],
      },
      {
        key: 'user-management',
        label: 'Quản lý người dùng',
        children: [
          {
            key: EMPLOYEE_INTERNAL_MANAGEMENT,
            label: 'Danh sách người dùng',
            path: EMPLOYEE_INTERNAL_MANAGEMENT,
          },
          {
            key: EMPLOYEE_EXTERNAL_MANAGEMENT,
            label: 'Danh sách người dùng ngoài cơ cấu',
            path: EMPLOYEE_EXTERNAL_MANAGEMENT,
          },
          // {
          //   key: '3',
          //   label: 'Lịch sử tải nhập người dùng',
          //   path: '/3',
          // },
        ],
      },
      {
        key: 'group4',
        label: 'Quản lý vai trò',
        children: [
          {
            key: USER_ROLES_MANAGEMENT,
            label: 'Danh sách vai trò',
            path: USER_ROLES_MANAGEMENT,
          },
          // {
          //   key: '9',
          //   label: 'Lịch sử tải nhập vai trò',
          //   path: '',
          // },
        ],
      },
    ],
  },
  {
    key: 'sub2',
    label: 'Lead & Customers',
    icon: <CustomerServiceOutlined />,
    children: [],
    groups: [
      {
        key: 'group1',
        label: 'Khai thác Lead',
        children: [
          {
            key: LEAD,
            label: 'Danh sách Lead',
            path: LEAD,
          },
          {
            key: LEAD_SOURCE,
            label: 'Danh sách nguồn Lead',
            path: LEAD_SOURCE,
          },
          {
            key: ASSIGN_LEAD,
            label: 'Danh sách Lead chờ phân bổ',
            path: ASSIGN_LEAD,
          },
          {
            key: LEAD_ASSIGNED,
            label: 'Danh sách Lead đã phân bổ',
            path: LEAD_ASSIGNED,
          },
          {
            key: LEAD_DASHBOARD,
            label: 'Khai thác Lead',
            path: LEAD_DASHBOARD,
          },
          {
            key: LEAD_CONFIG,
            label: 'Cấu hình phân bổ Lead',
            path: LEAD_CONFIG,
          },
          {
            key: LEAD_HISTORY_IMPORT,
            label: 'Lịch sử tải nhập Lead',
            path: LEAD_HISTORY_IMPORT,
          },
          {
            key: LEAD_REPORT,
            label: 'Báo cáo thống kê',
            path: LEAD_REPORT,
          },
        ],
      },
      {
        key: 'customers',
        label: 'Quản lý khách hàng',
        children: [
          {
            key: CUSTOMER_PERSONAL,
            label: 'Khách hàng tiềm năng cá nhân',
            path: CUSTOMER_PERSONAL,
          },
          {
            key: CUSTOMER_BUSINESS,
            label: 'Khách hàng tiềm năng doanh nghiệp',
            path: CUSTOMER_BUSINESS,
          },
          {
            key: OFFICIAL_CUSTOMER_PERSONAL,
            label: 'Khách hàng chính thức cá nhân',
            path: OFFICIAL_CUSTOMER_PERSONAL,
          },
          {
            key: OFFICIAL_CUSTOMER_BUSINESS,
            label: 'Khách hàng chính thức doanh nghiệp',
            path: OFFICIAL_CUSTOMER_BUSINESS,
          },
          {
            key: HISTORY_CUSTOMER,
            label: 'Lịch sử tải nhập KHTN',
            path: HISTORY_CUSTOMER,
          },
        ],
      },
    ],
  },
  {
    key: 'sub3',
    label: 'Dự án và sản phẩm',
    icon: <CodeSandboxOutlined />,
    children: [
      {
        key: INVESTORS_MANAGEMENT,
        label: 'Danh sách chủ đầu tư',
        path: INVESTORS_MANAGEMENT,
      },
      {
        key: PROJECTS_MANAGEMENT,
        label: 'Danh sách dự án',
        path: PROJECTS_MANAGEMENT,
      },
      // {
      //   key: '71',
      //   label: 'Lịch sử gửi email',
      //   path: '/71',
      // },
      {
        key: IMPORT_HISTORY_PROJECT_MANAGEMENT,
        label: 'Lịch sử tải nhập sản phẩm',
        path: IMPORT_HISTORY_PROJECT_MANAGEMENT,
      },
    ],
    groups: [
      {
        key: 'delivery',
        label: 'Bàn giao',
        children: [
          {
            key: DELIVERY_CONFIGURATION,
            label: 'Thiết lập bàn giao',
            path: DELIVERY_CONFIGURATION,
          },
          {
            key: DELIVERY,
            label: 'Quản lý danh sách bàn giao',
            path: DELIVERY,
          },
          {
            key: OWNERSHIP_CERTIFICATE_CONFIGURATION,
            label: 'Thiết lập bàn giao sổ',
            path: OWNERSHIP_CERTIFICATE_CONFIGURATION,
          },
        ],
      },
      {
        key: 'product-management',
        label: 'Quản lý sản phẩm',
        children: [
          {
            key: PROPERTY_ATTRIBUTES_MANAGEMENT,
            label: 'Quản lý thuộc tính',
            path: PROPERTY_ATTRIBUTES_MANAGEMENT,
          },
          {
            key: PRICE_COEFFICIENT,
            label: 'Quản lý cấu hình tính giá',
            path: PRICE_COEFFICIENT,
          },
        ],
      },
    ],
  },
  {
    key: 'sub4',
    label: 'Bán hàng',
    icon: <MoneyCollectOutlined />,
    children: [
      {
        key: SELL_PROGRAM_MANAGEMENT,
        label: 'Chương trình bán hàng',
        path: SELL_PROGRAM_MANAGEMENT,
      },
    ],
    groups: [
      {
        key: 'group1',
        label: 'Quản lý chính sách',
        children: [
          {
            key: '812',
            label: 'Chính sách thanh toán',
            path: PAYMENT_POLICY,
          },
          {
            key: DISCOUNT_POLICY,
            label: 'Chính sách chiết khấu',
            path: DISCOUNT_POLICY,
          },
          // {
          //   key: '912',
          //   label: 'Chính sách khuyến mãi',
          //   path: '/912',
          // },
          // {
          //   key: '83',
          //   label: 'Quản lý hàng khuyến mãi',
          //   path: '/83',
          // },
        ],
      },
      {
        key: 'group2',
        label: 'Quản lý chi phí môi giới hoa hồng',
        children: [
          {
            key: SALES_POLICY,
            label: 'Chính sách phí/ hoa hồng',
            path: SALES_POLICY,
          },
          {
            key: COMMISSION_POLICY,
            label: 'Bộ chỉ tiêu KPI',
            path: COMMISSION_POLICY,
          },
          {
            key: INDICATOR,
            label: 'Thiết lập chỉ tiêu',
            path: INDICATOR,
          },
          {
            key: COMMISSION,
            label: 'Tính phí môi giới',
            path: COMMISSION,
          },
          {
            key: COMMISSION_PERIOD_LIST,
            label: 'Tính phí hoa hồng',
            path: COMMISSION_PERIOD_LIST,
          },
          {
            key: PERSONAL_COMMISSION,
            label: 'Hoa hồng cá nhân',
            path: PERSONAL_COMMISSION,
          },
          {
            key: COMMISSION_HISTORY_IMPORT,
            label: 'Lịch sử tải nhập tính phí và hoa hồng',
            path: COMMISSION_HISTORY_IMPORT,
          },
          {
            key: COMMISSION_PERIOD_DEFAULT,
            label: 'Cấu hình thời gian kỳ phí/hoa hồng',
            path: COMMISSION_PERIOD_DEFAULT,
          },
        ],
      },
      {
        key: 'pay',
        label: 'Thanh toán',
        children: [
          {
            key: OFFER_COLLECT_MONEY_MANAGEMENT,
            label: 'Danh sách đề nghị thu tiền',
            path: OFFER_COLLECT_MONEY_MANAGEMENT,
          },
          {
            key: OFFER_REFUND_MONEY_PAYMENT_MANAGEMENT,
            label: 'Danh sách đề nghị hoàn tiền',
            path: OFFER_REFUND_MONEY_PAYMENT_MANAGEMENT,
          },
        ],
      },

      {
        key: 'group4',
        label: 'Kế toán',
        children: [
          {
            key: OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT,
            label: 'Danh sách đề nghị thu tiền',
            path: OFFER_ORDER_MONEY_ACCOUNTANCY_MANAGEMENT,
          },

          {
            key: OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT,
            label: 'Danh sách đề nghị hoàn tiền',
            path: OFFER_REFUND_MONEY_ACCOUNTANCY_MANAGEMENT,
          },
        ],
      },
      {
        key: 'training',
        label: 'Quản lý sự kiện',
        children: [
          {
            key: TRAINING,
            label: 'Thiết lập tổ chức sự kiện',
            path: TRAINING,
          },
          {
            key: TRAINING_USER_CREATE_MANAGEMENT,
            label: 'Đăng ký tham gia sự kiện',
            path: TRAINING_USER_CREATE_MANAGEMENT,
          },
          {
            key: TRAINING_USER_MANAGEMENT,
            label: 'Danh sách khách mời',
            path: TRAINING_USER_MANAGEMENT,
          },
          // {
          //   key: '18',
          //   label: 'Lịch sử tải nhập khách hàng tham dự',
          //   path: '/18',
          // },
        ],
      },
      // {
      //   key: 'group4',
      //   label: 'Kế toán',
      //   children: [
      //     {
      //       key: '19',
      //       label: 'Quản lý Danh sách các giao dịch thu',
      //       path: '/19',
      //     },
      //     {
      //       key: '20',
      //       label: 'Quản lý danh sách các giao dịch chi',
      //       path: '/20',
      //     },
      //     {
      //       key: '21',
      //       label: 'Lịch sử giao dịch thanh toán online',
      //       path: '/21',
      //     },
      //   ],
      // },
      {
        key: 'group5',
        label: 'Hợp đồng',
        children: [
          {
            key: '22',
            label: 'Hợp đồng cọc',
            path: DEPOSIT_CONTRACT_MANAGEMENT,
          },
          {
            key: '23',
            label: 'Hợp đồng mua bán',
            path: PURCHASE_CONTRACT_MANAGEMENT,
          },
          {
            key: '24',
            label: 'Hợp đồng chuyển nhượng',
            path: TRANSFER_CONTRACT_MANAGEMENT,
          },
          {
            key: DEBT_REPORT,
            label: 'Thống kê công nợ',
            path: DEBT_REPORT,
          },
          // {
          //   key: '26',
          //   label: 'Lịch sử tải nhập hợp đồng',
          //   path: '/26',
          // },
        ],
      },
      {
        key: 'debt-commission',
        label: 'Hoa hồng công nợ',
        children: [
          {
            key: COMMISSION_DEBT_POLICY,
            label: 'Bộ chỉ tiêu KPI',
            path: COMMISSION_DEBT_POLICY,
          },
          {
            key: COMMISSION_DEBT_PENALTY,
            label: 'Tính phạt và hoa hồng',
            path: COMMISSION_DEBT_PENALTY,
          },
          {
            key: DEBT_COMMISSION_PERIOD,
            label: 'Cấu hình kỳ tính hoa hồng công nợ',
            path: DEBT_COMMISSION_PERIOD,
          },
          {
            key: '1112',
            label: 'Lịch sử tải nhập hoa hồng công nợ',
            path: '/1112',
          },
        ],
      },
      {
        key: 'group6',
        label: 'Thanh lý',
        children: [
          {
            key: '27',
            label: 'Đơn đề nghị thanh lý',
            path: LIQUIDATION_PROPOSAL_MANAGEMENT,
          },
          {
            key: '28',
            label: 'Thanh lý hợp đồng',
            path: LIQUIDATION_CONTRACT_MANAGEMENT,
          },
        ],
      },
    ],
  },
  {
    key: 'sub5',
    label: 'Báo cáo',
    icon: <FileSyncOutlined />,
    children: [
      //     {
      //       key: '45',
      //       label: 'Báo cáo yêu cầu tư vấn',
      //       path: '/45',
      //     },
      //     {
      //       key: '55',
      //       label: 'Báo cáo nhân viên',
      //       path: '/55',
      //     },
      //     {
      //       key: '65',
      //       label: 'Báo cáo bản mới',
      //       path: '/65',
      //     },
      //     {
      //       key: '75',
      //       label: 'Báo cáo đặt chỗ, chuyển cọc',
      //       path: '/75',
      //     },
      //     {
      //       key: '85',
      //       label: 'Báo cáo khai thác Lead',
      //       path: '/85',
      //     },
      //     {
      //       key: '95',
      //       label: 'Báo cáo kết quả khảo sát Lead',
      //       path: '/95',
      //     },
      //     {
      //       key: '105',
      //       label: 'Báo cáo thống kê sản phẩm',
      //       path: '/105',
      //     },
      //     {
      //       key: '115',
      //       label: 'Báo cáo dự án',
      //       path: '/115',
      //     },
      //     {
      //       key: '125',
      //       label: 'Báo cáo đại lý',
      //       path: '/125',
      //     },
      //     {
      //       key: '135',
      //       label: 'Báo cáo nhân viên',
      //       path: '/135',
      //     },
      //     {
      //       key: '145',
      //       label: 'Báo cáo doanh thu',
      //       path: '/145',
      //     },
    ],
    groups: [],
  },
  {
    key: 'sub6',
    label: 'Tờ trình',
    icon: <EditOutlined />,
    children: [],
    groups: [
      {
        key: 'group1',
        label: 'Quản lý tờ trình',
        children: [
          {
            key: PROPOSAL,
            label: 'Danh sách tờ trình',
            path: PROPOSAL,
          },
        ],
      },
    ],
  },
  {
    key: 'sub7',
    label: 'E-voucher',
    icon: <GiftOutlined />,
    children: [
      {
        key: E_VOUCHER_LIST,
        label: 'Danh sách e-voucher',
        path: E_VOUCHER_LIST,
      },
      {
        key: E_VOUCHER,
        label: 'Thiết lập e-voucher',
        path: E_VOUCHER,
      },
      {
        key: E_VOUCHER_HISTORY,
        label: 'Lịch sử e-voucher',
        path: E_VOUCHER_HISTORY,
      },
    ],
    groups: [],
  },
  {
    key: 'sub8',
    label: 'Kế hoạch kinh doanh tiếp thị',
    icon: <SolutionOutlined />,
    children: [
      {
        key: MARKETING,
        label: 'Danh sách kế hoạch kinh doanh tiếp thị',
        path: MARKETING,
      },
    ],
    groups: [],
  },
];
